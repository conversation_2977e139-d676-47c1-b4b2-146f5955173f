{"name": "highlands-game-app", "private": true, "version": "1.0.0", "description": "zmp-blank-templates", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"login": "zmp login", "start": "zmp start", "deploy": "zmp deploy", "build": "zmp build", "lint": "npx eslint src", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@antscorp/ama-ui": "file:.yalc/@antscorp/ama-ui", "@antscorp/ama-ui-v1": "^0.0.0", "@dnd-kit/core": "^6.3.1", "@react-google-maps/api": "^2.20.7", "@sentry/react": "^8.47.0", "@sentry/vite-plugin": "^3.1.2", "@tanstack/react-query": "^5.62.8", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "fast-deep-equal": "^3.1.3", "flubber": "^0.4.2", "howler": "^2.2.4", "immer": "^10.1.1", "lodash-es": "^4.17.21", "lucide-react": "^0.471.0", "md5": "^2.3.0", "motion": "^11.14.4", "phaser": "^3.88.2", "polish": "^0.2.3", "polished": "^4.3.1", "prop-types": "^15.8.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-barcode": "^1.5.3", "react-canvas-confetti": "^2.0.7", "react-dom": "^18.3.1", "react-howler": "^5.2.0", "react-router-dom": "^6.23.0", "react-simple-pull-to-refresh": "^1.3.3", "recoil": "^0.7.7", "socket.io-client": "^4.8.1", "styled-components": "^6.1.13", "swiper": "11.2.4", "typed.js": "^2.1.0", "use-immer": "^0.11.0", "usehooks-ts": "^3.1.0", "zaui-tokens": "^0.1.8", "zmp-sdk": "latest", "zmp-ui": "latest", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/addon-viewport": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-webpack5": "^8.4.7", "@storybook/test": "^8.4.7", "@svgr/webpack": "^8.1.0", "@types/crypto-js": "^4.2.2", "@types/dotenv-webpack": "^7.0.8", "@types/flubber": "^0.4.0", "@types/lodash-es": "^4.17.12", "@types/md5": "^2.3.5", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-howler": "^5.2.3", "@vitejs/plugin-react": "1.3.2", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "dotenv-webpack": "^8.1.1", "eslint": "^9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-css-modules": "^2.12.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-styled-components-a11y": "^2.1.36", "globals": "^15.13.0", "postcss": "^8.4.38", "postcss-cli": "^8.3.1", "postcss-loader": "^8.1.1", "postcss-preset-env": "^6.7.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.76.0", "sass-loader": "^16.0.3", "storybook": "^8.4.7", "style-loader": "^4.0.0", "tailwindcss": "^3.4.3", "ts-loader": "^9.5.1", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.7.2", "typescript-eslint": "^8.18.0", "vite": "^2.6.14", "vite-plugin-compression2": "^1.3.3", "vite-plugin-svgr": "4.3.0", "vite-tsconfig-paths": "4.0.5", "webpack": "^5.101.3"}, "resolutions": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1"}, "engines": {"node": ">=20.0.0", "yarn": ">=1.22.0"}}