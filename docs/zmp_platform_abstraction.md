- **Core Principle: Platform Abstraction**
  - **Never import zmp-sdk or zmp-ui directly in business logic components**
  - **Always use wrapper classes, utilities, or abstracted UI components**
  - **Design for easy platform migration from Zalo to WeChat, Alipay, etc.**

- **Required Architecture Pattern**
  - **Create abstraction layers for all platform-specific functionality**
  - **Use dependency injection pattern for platform services**
  - **Implement interface-based design for cross-platform compatibility**

- **SDK Abstraction Requirements**
  - **✅ DO: Create wrapper services for zmp-sdk APIs**

    ```typescript
    // ✅ GOOD: Abstracted service layer
    // src/services/platform/auth.ts
    export interface IPlatformAuth {
      getAccessToken(): Promise<string>;
      getPhoneNumber(): Promise<{ token: string }>;
      getUserInfo(): Promise<any>;
    }

    export class ZaloPlatformAuth implements IPlatformAuth {
      async getAccessToken() {
        return await getAccessToken();
      }
      // ... other methods
    }

    // Usage in components
    import { platformAuth } from 'services/platform';
    const token = await platformAuth.getAccessToken();
    ```

  - **❌ DON'T: Direct imports in components**

    ```typescript
    // ❌ BAD: Direct import in component
    import { getAccessToken } from 'zmp-sdk/apis';
    const token = await getAccessToken();
    ```

- **UI Component Abstraction Requirements**
  - **✅ DO: Create wrapper components for zmp-ui**

    ```typescript
    // ✅ GOOD: Abstracted UI component
    // src/components/ui/Button.tsx (existing pattern)
    import { Button as ZMPButton } from "zmp-ui";
    import { ButtonProps as ZMPButtonProps } from "zmp-ui/button";

    interface ButtonProps extends ZMPButtonProps {}

    export const Button: React.FC<ButtonProps> = (props) => {
      return <StyledButton {...props} />;
    };
    ```

  - **❌ DON'T: Use zmp-ui components directly in pages**

    ```typescript
    // ❌ BAD: Direct usage in page
    import { Button } from "zmp-ui";
    <Button>Click me</Button>
    ```

- **File Organization Structure**
  - **Platform Services**: `src/services/platform/`
    - `auth.ts` - Authentication related APIs
    - `navigation.ts` - Navigation and routing
    - `storage.ts` - Local storage and caching
    - `device.ts` - Device capabilities and sensors
    - `payment.ts` - Payment and transactions
    - `social.ts` - Social features (follow, share)
  - **Platform Components**: `src/components/platform/`
    - `Button.tsx` - Cross-platform button component
    - `Input.tsx` - Cross-platform input component
    - `Modal.tsx` - Cross-platform modal component
    - `Navigation.tsx` - Cross-platform navigation
  - **Platform Types**: `src/types/platform.ts`
    - Common interfaces for all platforms
    - Platform-specific type extensions
    - Migration helper types

- **Implementation Guidelines**
  - **✅ DO:**
    - Create `IPlatformService` interface for each service category
    - Implement Zalo-specific classes that implement these interfaces
    - Use dependency injection to provide platform services
    - Create platform-agnostic business logic
    - Document platform-specific limitations and workarounds
  - **❌ DON'T:**
    - Mix platform-specific code with business logic
    - Hardcode platform-specific behavior
    - Create tight coupling between components and platform APIs
    - Skip interface definitions for platform services

- **Migration Preparation**
  - **Platform Detection**: Implement runtime platform detection

    ```typescript
    // src/utils/platform.ts
    export const getPlatform = () => {
      // Detect current platform (Zalo, WeChat, Alipay, etc.)
      return 'zalo'; // or 'wechat', 'alipay'
    };

    export const isZalo = () => getPlatform() === 'zalo';
    export const isWeChat = () => getPlatform() === 'wechat';
    ```

  - **Service Factory**: Use factory pattern for platform services

    ```typescript
    // src/services/platform/factory.ts
    export const createPlatformService = (platform: string) => {
      switch (platform) {
        case 'zalo':
          return new ZaloPlatformService();
        case 'wechat':
          return new WeChatPlatformService();
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }
    };
    ```

- **Existing Code Migration Strategy**
  - **Phase 1**: Create abstraction interfaces and Zalo implementations
  - **Phase 2**: Gradually replace direct imports with wrapper services
  - **Phase 3**: Extract business logic from platform-specific code
  - **Phase 4**: Test with mock platform services
  - **Phase 5**: Implement other platform services (WeChat, Alipay)

- **Testing and Validation**
  - **Unit Tests**: Mock platform services for testing
  - **Integration Tests**: Test with different platform implementations
  - **Migration Tests**: Verify functionality across platforms
  - **Performance Tests**: Ensure abstraction doesn't impact performance

- **Documentation Requirements**
  - **Platform Compatibility Matrix**: Document supported features per platform
  - **Migration Guide**: Step-by-step migration instructions
  - **API Reference**: Document all abstraction layer APIs
  - **Limitations**: Document platform-specific limitations and workarounds

- **Code Review Checklist**
  - [ ] No direct imports of zmp-sdk or zmp-ui in business logic
  - [ ] All platform APIs go through abstraction layer
  - [ ] Platform services implement common interfaces
  - [ ] Business logic is platform-agnostic
  - [ ] Platform detection is implemented
  - [ ] Migration path is documented
  - [ ] Tests cover multiple platform scenarios

- **Legacy Code Handling Strategy**
  - **Existing Direct Imports**: Code that already directly imports zmp-sdk/zmp-ui
  - **Migration Priority**: Only migrate when directly related to user requirements
  - **No Forced Refactoring**: Don't refactor existing code unless explicitly requested
  - **Documentation**: Mark legacy code for future migration consideration

- **Legacy Code Identification**
  - **Files with Direct Imports**:
    - `src/utils/api.ts` - Direct zmp-sdk imports
    - `src/queries/user.ts` - Direct zmp-sdk imports
    - `src/state.ts` - Direct zmp-sdk imports
    - `src/services/api.ts` - Direct zmp-sdk imports
    - `src/components/layout/Layout.tsx` - Direct zmp-sdk imports
    - `src/pages/home/<USER>/News.tsx` - Direct zmp-sdk imports
    - `src/pages/gift-form/components/GiftForm.tsx` - Direct zmp-sdk imports
    - `src/components/common/WalkThrough.tsx` - Direct zmp-sdk imports
    - `src/hooks/useRequestZaloPermissions.ts` - Direct zmp-sdk imports
    - `src/components/common/SystemError.tsx` - Direct zmp-sdk imports
    - `src/components/common/Navigation.tsx` - Direct zmp-sdk imports
    - `src/pages/games/affiliate/screens/Home.tsx` - Direct zmp-sdk imports
    - `src/pages/games/affiliate/screens/CouponSelection.tsx` - Direct zmp-sdk imports
    - `src/components/common/ErrorBoundary.tsx` - Direct zmp-sdk imports
    - `src/pages/games/mix-and-match/screens/StartScreen/index.tsx` - Direct zmp-sdk imports
    - `src/components/common/MemberRegistration/MemberRegistration.tsx` - Direct zmp-sdk imports
    - `src/pages/games/mix-and-match/screens/GameScreen/GameScreenV2.tsx` - Direct zmp-sdk imports

  - **Files with Direct zmp-ui Usage**:
    - `src/pages/form.tsx` - Direct zmp-ui imports
    - `src/pages/vouchers/voucher-detail/index.tsx` - Direct zmp-ui imports
    - `src/pages/vouchers/voucher-detail/ClauseSheet.tsx` - Direct zmp-ui imports
    - `src/components/user-card.tsx` - Direct zmp-ui imports
    - `src/pages/gift-form/components/Header.tsx` - Direct zmp-ui imports
    - `src/pages/gift-form/components/AllocatedVoucher.tsx` - Direct zmp-ui imports
    - `src/pages/home/<USER>/MemberShip.tsx` - Direct zmp-ui imports
    - `src/pages/brand-list/brand-detail/index.tsx` - Direct zmp-ui imports
    - `src/components/ui/SystemNotificationModal.tsx` - Direct zmp-ui imports
    - `src/components/ui/Empty.tsx` - Direct zmp-ui imports
    - `src/components/ui/Spinner.tsx` - Direct zmp-ui imports
    - `src/components/ui/CustomCheckbox.tsx` - Direct zmp-ui imports
    - `src/components/ui/ComingSoon.tsx` - Direct zmp-ui imports
    - `src/components/ui/ActionBar.tsx` - Direct zmp-ui imports
    - `src/components/layout/HomeHeader/components/Registered.tsx` - Direct zmp-ui imports
    - `src/components/layout/HomeHeader/components/MemberCard.tsx` - Direct zmp-ui imports
    - `src/components/layout/HomeHeader/HomeHeader.tsx` - Direct zmp-ui imports
    - `src/components/app.tsx` - Direct zmp-ui imports
    - `src/components/common/ErrorBoundary.tsx` - Direct zmp-ui imports
    - `src/components/common/WalkThrough.tsx` - Direct zmp-ui imports
    - `src/pages/gift/components/VoucherList/VoucherItem.tsx` - Direct zmp-ui imports
    - `src/components/common/UserGretting.tsx` - Direct zmp-ui imports
    - `src/components/common/TermAndConditionSheet.tsx` - Direct zmp-ui imports
    - `src/components/common/MembershipCard.tsx` - Direct zmp-ui imports
    - `src/components/common/Navigation.tsx` - Direct zmp-ui imports
    - `src/pages/games/affiliate/components/AllocatedVoucher.tsx` - Direct zmp-ui imports
    - `src/components/common/LuckyMoneyEmpty.tsx` - Direct zmp-ui imports

- **Legacy Code Migration Rules**
  - **✅ DO When User Requests Changes:**
    - If user modifies a file with direct imports, migrate to abstraction layer
    - If user adds new functionality to legacy files, use abstraction layer
    - If user requests refactoring of specific legacy components
    - Document migration in commit messages and code comments
  - **❌ DON'T Force Migration:**
    - Don't refactor legacy code just for "clean code" purposes
    - Don't migrate files that aren't being modified
    - Don't create migration tasks without user request
    - Don't break existing functionality during migration

- **Legacy Code Documentation**
  - **Add Comments to Legacy Files:**

    ```typescript
    // TODO: Legacy code - Consider migration to platform abstraction layer
    // This file directly imports zmp-sdk/zmp-ui and should be refactored
    // when making changes to this component
    import { getAccessToken } from 'zmp-sdk/apis';
    ```

  - **Mark in TODO Comments:**

    ```typescript
    // LEGACY: Direct zmp-sdk import - migrate to platform abstraction when modifying
    // LEGACY: Direct zmp-ui usage - migrate to platform abstraction when modifying
    ```

- **Migration Triggers**
  - **User-Requested Changes**: Any modification to legacy files
  - **New Feature Development**: Adding functionality to legacy components
  - **Bug Fixes**: When fixing issues in legacy code
  - **Performance Improvements**: When optimizing legacy components
  - **Code Review Requests**: When team requests migration

- **Examples from Current Codebase**
  - **✅ Good Pattern**: `src/components/ui/Button.tsx` - Wraps zmp-ui Button
  - **✅ Good Pattern**: `src/services/zalo.ts` - Centralizes Zalo API calls
  - **⚠️ Legacy Code (No Forced Migration)**: Direct imports in multiple components
  - **⚠️ Legacy Code (No Forced Migration)**: Mixed platform and business logic in some files
