# Ant Design Mobile Form Implementation Guide

## Overview

This guide provides comprehensive best practices, rules, and patterns for implementing forms and custom fields with Ant Design Mobile in the Golden Gate Mini App project.

## Core Principles

### 1. **Never Import SDK Directly in Form Logic**

```tsx
// ❌ BAD: Direct SDK imports in form components
import { getAccessToken } from 'zmp-sdk/apis';

// ✅ GOOD: Use platform abstraction layer
import { platformAuth } from 'services/platform';
```

### 2. **Always Use Form.Item for Field Management**

```tsx
// ❌ BAD: Manual state management
const [userName, setUserName] = useState('');
const [selectedTags, setSelectedTags] = useState([]);

// ✅ GOOD: Form.Item handles all state automatically
<Form.Item name="userName" rules={[{required: true}]}>
  <Input />
</Form.Item>

<Form.Item name="selectedTags" initialValue={[]}>
  <CustomTagSelector />
</Form.Item>
```

### 3. **Custom Fields Must Follow Interface Pattern**

All custom fields MUST implement the standard controlled component interface:

```tsx
export interface CustomFieldProps {
  value?: T; // ✅ Required: Current field value
  onChange?: (value: T) => void; // ✅ Required: Value change handler
  // Optional props...
  disabled?: boolean;
  placeholder?: string;
}

export const CustomField: React.FC<CustomFieldProps> = ({ value, onChange, ...props }) => {
  // Implementation that respects value/onChange contract
};
```

## Form Implementation Rules

### Rule 1: Form Structure and Validation

```tsx
// ✅ CORRECT: Complete form setup
const MyForm: React.FC<MyFormProps> = ({ onSubmit, initialValues = {} }) => {
  const [form] = Form.useForm<FormValues>();

  const handleSubmit = (values: FormValues) => {
    // No manual state aggregation needed
    onSubmit?.(values);
  };

  return (
    <Form
      form={form}
      initialValues={initialValues}
      onFinish={handleSubmit}
      validateMessages={customValidationMessages}
    >
      <Form.Item name="fieldName" rules={[{ required: true, message: 'Field is required' }]}>
        <CustomField />
      </Form.Item>
    </Form>
  );
};
```

### Rule 2: Handle Form Submission Properly

```tsx
// ❌ BAD: Manual state aggregation
const handleSubmit = (values: Partial<FormValues>) => {
  const finalValues = {
    ...values,
    manualField: someLocalState, // Anti-pattern
    anotherField: anotherState, // Anti-pattern
  };
  onSubmit?.(finalValues);
};

// ✅ GOOD: Form handles everything
const handleSubmit = (values: FormValues) => {
  // All fields are automatically included
  onSubmit?.(values);
};
```

### Rule 3: Initial Values and Reset

```tsx
// ✅ CORRECT: Proper initial values
<Form
  form={form}
  initialValues={{
    reservationTime: new Date(),
    numberOfPeople: 2,
    selectedTags: ['Default tag'],
  }}
>
  {/* Form fields */}
</Form>;

// ✅ CORRECT: Form reset
const handleReset = () => {
  form.resetFields(); // Resets to initialValues
};
```

## Custom Field Implementation Patterns

### Pattern 1: Basic Custom Field

```tsx
export interface TextFieldProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

export const TextField: React.FC<TextFieldProps> = ({ value = '', onChange, placeholder }) => {
  return (
    <input
      type="text"
      value={value}
      onChange={(e) => onChange?.(e.target.value)}
      placeholder={placeholder}
    />
  );
};

// Usage in Form
<Form.Item name="customText">
  <TextField placeholder="Enter text" />
</Form.Item>;
```

### Pattern 2: Complex Custom Field with State

```tsx
export interface DateTimePickerFieldProps {
  value?: Date;
  onChange?: (value: Date) => void;
}

export const DateTimePickerField: React.FC<DateTimePickerFieldProps> = ({ value, onChange }) => {
  const [isVisible, setIsVisible] = useState(false);

  const handleDateSelect = (selectedDate: Date) => {
    onChange?.(selectedDate);
    setIsVisible(false);
  };

  return (
    <>
      <button onClick={() => setIsVisible(true)}>
        {value ? formatDate(value) : 'Select date'}
      </button>

      <DatePickerModal
        visible={isVisible}
        value={value}
        onSelect={handleDateSelect}
        onClose={() => setIsVisible(false)}
      />
    </>
  );
};
```

### Pattern 3: Multi-Select Custom Field

```tsx
export interface TagSelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  options: string[];
}

export const TagSelector: React.FC<TagSelectorProps> = ({ value = [], onChange, options }) => {
  const handleTagToggle = (tag: string) => {
    const newValue = value.includes(tag) ? value.filter((t) => t !== tag) : [...value, tag];
    onChange?.(newValue);
  };

  return (
    <div className="flex flex-wrap gap-2">
      {options.map((tag) => (
        <button
          key={tag}
          className={value.includes(tag) ? 'selected' : 'unselected'}
          onClick={() => handleTagToggle(tag)}
        >
          {tag}
        </button>
      ))}
    </div>
  );
};
```

## Advanced Patterns

### Pattern 4: External Value Synchronization

```tsx
export const SyncedCustomField: React.FC<CustomFieldProps> = ({ value, onChange }) => {
  const [internalValue, setInternalValue] = useState('');

  // Sync with external value changes
  useEffect(() => {
    setInternalValue(value || '');
  }, [value]);

  const handleChange = (newValue: string) => {
    setInternalValue(newValue);
    onChange?.(newValue);
  };

  return <input value={internalValue} onChange={(e) => handleChange(e.target.value)} />;
};
```

### Pattern 5: Custom Field with Validation Integration

```tsx
// Form.Item can automatically validate custom fields
<Form.Item
  name="customField"
  rules={[
    { required: true, message: 'This field is required' },
    {
      validator: async (_, value) => {
        if (value < 1 || value > 20) {
          throw new Error('Value must be between 1 and 20');
        }
      },
    },
  ]}
>
  <NumberPickerField min={1} max={20} />
</Form.Item>
```

## Common Mistakes to Avoid

### ❌ Anti-Pattern 1: Manual State Management

```tsx
// DON'T DO THIS
const [formData, setFormData] = useState({});
const [errors, setErrors] = useState({});

const handleFieldChange = (field: string, value: any) => {
  setFormData((prev) => ({ ...prev, [field]: value }));
  // Manual validation...
};
```

### ❌ Anti-Pattern 2: Breaking Form.Item Contract

```tsx
// DON'T DO THIS
<Form.Item name="badField">
  <div>
    <Input
      value={manualValue} // ❌ Manual value control
      onChange={handleManualChange} // ❌ Breaks Form.Item contract
    />
  </div>
</Form.Item>
```

### ❌ Anti-Pattern 3: Nested Elements in Form.Item

```tsx
// DON'T DO THIS
<Form.Item name="badStructure">
  <div>
    <CustomField />  {/* ❌ Form.Item can't control this */}
  </div>
</Form.Item>

// DO THIS INSTEAD
<Form.Item name="goodStructure">
  <CustomField />    {/* ✅ Direct child */}
</Form.Item>
```

## Form Validation Best Practices

### 1. **Use Built-in Validation Rules**

```tsx
<Form.Item
  name="email"
  rules={[
    { required: true, message: 'Email is required' },
    { type: 'email', message: 'Please enter valid email' },
  ]}
>
  <Input type="email" />
</Form.Item>
```

### 2. **Custom Validation Functions**

```tsx
<Form.Item
  name="phoneNumber"
  rules={[
    {
      validator: async (_, value) => {
        if (value && !isValidPhoneNumber(value)) {
          throw new Error('Please enter a valid phone number');
        }
      },
    },
  ]}
>
  <Input />
</Form.Item>
```

### 3. **Conditional Validation**

```tsx
<Form.Item shouldUpdate>
  {({ getFieldValue }) => {
    const userType = getFieldValue('userType');

    return (
      <Form.Item name="businessLicense" rules={userType === 'business' ? [{ required: true }] : []}>
        <Input />
      </Form.Item>
    );
  }}
</Form.Item>
```

## Performance Optimization

### 1. **Memoize Custom Components**

```tsx
export const OptimizedCustomField = React.memo<CustomFieldProps>(({ value, onChange, options }) => {
  // Component implementation
});
```

### 2. **Use shouldUpdate Wisely**

```tsx
// ✅ GOOD: Specific field dependency
<Form.Item shouldUpdate={(prev, curr) => prev.fieldA !== curr.fieldA}>
  {({ getFieldValue }) => (
    <ConditionalField dependsOn={getFieldValue('fieldA')} />
  )}
</Form.Item>

// ❌ BAD: Updates on any change
<Form.Item shouldUpdate>
  {/* Will re-render on any form change */}
</Form.Item>
```

## Testing Custom Fields

### 1. **Unit Testing Custom Fields**

```tsx
import { render, fireEvent } from '@testing-library/react';
import { Form } from '@antscorp/ama-ui';

test('CustomField integrates with Form.Item', async () => {
  const mockSubmit = jest.fn();

  const { getByRole } = render(
    <Form onFinish={mockSubmit}>
      <Form.Item name="testField">
        <CustomField />
      </Form.Item>
      <button type="submit">Submit</button>
    </Form>,
  );

  // Test field interaction
  fireEvent.change(getByRole('textbox'), { target: { value: 'test' } });
  fireEvent.click(getByRole('button'));

  expect(mockSubmit).toHaveBeenCalledWith({ testField: 'test' });
});
```

### 2. **Integration Testing Forms**

```tsx
test('Form validates and submits correctly', async () => {
  const { getByRole, getByText } = render(<MyForm onSubmit={mockSubmit} />);

  // Test validation
  fireEvent.click(getByRole('button', { name: /submit/i }));
  expect(getByText(/required/i)).toBeInTheDocument();

  // Test successful submission
  fireEvent.change(getByRole('textbox'), { target: { value: 'valid' } });
  fireEvent.click(getByRole('button', { name: /submit/i }));

  await waitFor(() => {
    expect(mockSubmit).toHaveBeenCalledWith({ field: 'valid' });
  });
});
```

## Project-Specific Guidelines

### 1. **Golden Gate Brand Integration**

```tsx
// Use project color variables
const StyledField = styled(CustomField)`
  --primary-color: var(--color-main-primary);
  --border-color: var(--color-grey-secondary);
  --text-color: var(--color-text-primary);
`;
```

### 2. **Mobile Optimization**

```tsx
// Always consider mobile touch targets
const MobileOptimizedField = styled.button`
  min-height: 44px; // Minimum touch target
  min-width: 44px;
  touch-action: manipulation;
`;
```

### 3. **Internationalization Ready**

```tsx
// Use translation keys instead of hardcoded text
<Form.Item name="field" rules={[{ required: true, message: t('validation.required') }]}>
  <CustomField placeholder={t('form.placeholder')} />
</Form.Item>
```

## Debugging and Troubleshooting

### Common Issues and Solutions

1. **Form.Item not receiving value updates**
   - Check if custom field implements `value` and `onChange` props correctly
   - Ensure Form.Item is direct parent of custom field

2. **Validation not working**
   - Verify `name` prop is set on Form.Item
   - Check if custom field calls `onChange` properly

3. **Initial values not applied**
   - Use `initialValue` on Form.Item for field-specific defaults
   - Use `initialValues` on Form for multiple fields

4. **Form submission not including all fields**
   - Ensure all fields have `name` prop in Form.Item
   - Check if custom fields properly call `onChange`

### Debug Tools

```tsx
// Add this to debug form state
<Form.Item shouldUpdate>
  {() => <pre>{JSON.stringify(form.getFieldsValue(), null, 2)}</pre>}
</Form.Item>
```

## Conclusion

Following these patterns ensures:

- **Consistency** with Ant Design Mobile ecosystem
- **Maintainability** through standardized patterns
- **Testability** with proper separation of concerns
- **Performance** through optimized re-rendering
- **Accessibility** with proper form semantics

Remember: Always favor Form.Item's automatic state management over manual state handling for the most robust and maintainable forms.

