{"name": "@antscorp/ama-ui", "version": "0.0.41+a2c3c1fc", "main": "dist/ama-ui.es.js", "module": "dist/ama-ui.es.js", "types": "dist/index.d.ts", "sideEffects": false, "type": "module", "files": ["dist"], "scripts": {"prepublishOnly": "yarn && yarn build", "dev": "vite", "build": "yarn clean && vite build --config vite.config.build.ts", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"antd-mobile": "^5.38.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "react": "^18.3.1", "react-dom": "^18.3.1", "styled-components": "^6.1.14", "tailwindcss-animate": "^1.0.7"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "yalcSig": "a2c3c1fcd85f720203926dd417fb6c65"}