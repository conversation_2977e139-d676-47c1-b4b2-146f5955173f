module.exports = {
  purge: {
    enabled: true,
    content: ['./src/**/*.{js,jsx,ts,tsx,vue}'],
  },
  theme: {
    extend: {
      colors: {
        primary: 'var(--color-primary)',
        'main-primary': 'var(--color-main-primary)',
        'brown-secondary': 'var(--color-brown-secondary)',
        description: 'var(--hl-color-text-description)',
        placeholder: 'var(--color-placeholder)',
        background: 'var(--color-background)',
        secondary: 'var(--color-text-secondary)',
        tertiary: 'var(--color-text-tertiary)',
        error: 'var(--color-error)',
        success: 'var(--color-success)',
        'error-light': 'var(--color-background-error)',
        'success-light': 'var(--color-background-success)',
        'primary-light': 'var(--color-background-primary)',
        black: {
          DEFAULT: 'var(--color-text-primary)',
          300: 'var(--color-text-primary)',
        },
        red: {
          300: 'var(--color-error)',
          DEFAULT: 'var(--color-error)',
        },
        blue: {
          DEFAULT: '#0068FF',
          300: '#0068FF',
        },
        green: {
          DEFAULT: 'var(--color-success)',
          300: 'var(--color-success)',
        },
        gray: {
          default: 'var(--color-grey-primary)',
          800: 'var(--color-grey-secondary)',
        },
        NL800: 'var(--NL800)',
        NL300: 'var(--NL300)',
        NL700: 'var(--NL700)',
        GGG: 'var(--GGG)',
      },
      padding: {
        layout: 'var(--layout-content-padding)',
      },
      fontFamily: {
        redRose: ['Red Rose', 'cursive'],
        oceanSansStd: ['OceanSansStd', 'sans-serif'],
        mansalva: ['Mansalva', 'sans-serif'],
        roboto: ['Roboto', 'sans-serif'],
      },
      backgroundColor: {
        gray: 'var(--color-background-disabled)',
      },
      fontSize: {
        base: ['0.938rem', { lineHeight: '1.375rem' }],
        lg: ['1.063rem', { lineHeight: '1.5rem' }],

        heading: ['1.25rem', { lineHeight: '8rem' }],
        title: ['1.063rem', { lineHeight: '1.625rem' }],
        subtitle: ['0.938rem', { lineHeight: '1.375rem' }],
        button: ['1rem', { lineHeight: '1.5rem' }],
        normal: ['0.938rem', { lineHeight: '1.375rem' }],
        small: ['0.75rem', { lineHeight: '1.125rem' }],
        caption: ['0.625rem', { lineHeight: '0.875rem' }],
        link: ['0.938rem', { lineHeight: '1.375rem' }],
      },
    },
    screens: {
      xxs: '380px',
      xs: '400px',
      'h-xs': {
        raw: '(max-height: 800px)',
      },
      'h-xxs': {
        raw: '(max-height: 700px)',
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      addUtilities({
        '.scrollbar-hide': {
          /* IE and Edge */
          '-ms-overflow-style': 'none',

          /* Firefox */
          'scrollbar-width': 'none',

          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
      });
    },
  ],
};
