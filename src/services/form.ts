// Types
import { CommonResponse } from "types";

import { AllocateVoucher } from "schemas";
import { fetchInstance } from "./api";
import { getSearchParamsAsObject } from "utils";

const FORM_ENDPOINT = "/zma/ggg/form/form";

export type GiftSubmitArgs = {
  data: {
    phoneNumber: string;
    name: string;
    idByOa: string;
    extra?: Record<string, any>;
  };
};

export const formServices = {
  giftSubmit: async ({
    data,
  }: GiftSubmitArgs): Promise<CommonResponse<AllocateVoucher>> => {
    // Get utm params
    const searchParams = getSearchParamsAsObject();
    const utmObjects = {};

    Object.keys(searchParams).forEach((key) => {
      if (key.startsWith("utm_")) {
        utmObjects[key] = searchParams[key];
      }
    });

    const response = await fetchInstance(`${FORM_ENDPOINT}/submit`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        ...data,
        extra: {
          ...utmObjects,
          ...data.extra,
        },
      }),
    });

    const responseData = await response.json();

    return responseData;
  },
};
