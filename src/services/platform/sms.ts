import { openSMS } from "zmp-sdk/apis";

export interface IPlatformSMS {
  sendSMSAsync: (
    adddresses: string | string[],
    message: string
  ) => Promise<void>;
}

export class ZaloPlatformSMS implements IPlatformSMS {
  sendSMSAsync(addresses: string | string[], message: string): Promise<void> {
    if (Array.isArray(addresses)) {
      throw new Error("Zalo Platform SMS does not support multiple addresses");
    }

    return openSMS({
      phoneNumber: addresses,
      content: message,
    });
  }
}
