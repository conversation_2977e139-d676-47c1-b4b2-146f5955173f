// Types
import { CommonResponse } from "types";

// Schemas
import { CheckCanAllocateVoucher, CheckUserResponse } from "schemas";

// Api
import { fetchInstance } from "./api";

const USER_ENDPOINT = `/zma/ggg/form/user`;

export type CheckUserCanAllocateArgs = {
  endPoint?: string;
};

export const userServices = {
  checkUser: async (): Promise<CommonResponse<CheckUserResponse>> => {
    const response = await fetchInstance(`${USER_ENDPOINT}/check`);

    const data = await response.json();

    return data;
  },
  checkUserCanAllocate: async (
    { endPoint }: CheckUserCanAllocateArgs = { endPoint: "user/check" }
  ): Promise<CommonResponse<CheckCanAllocateVoucher>> => {
    const response = await fetchInstance(`/zma/ggg/form/${endPoint}`);

    const data = await response.json();

    return data;
  },
};
