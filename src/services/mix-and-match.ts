// Api
import { CommonResponse } from "types";
import { fetchInstance } from "./api";

// Schemas
import { AllocateVoucher, LeaderBoardItem, Mission } from "schemas";
import { getSearchParamsAsObject } from "utils";

const MIX_AND_MATCH_ENDPOINT = "/zma/ggg/form/mix-match";

// Types
export type AllocateVoucherMixAndMatchArgs = {
  bodyData: {
    userName: string;
    idByOa: string;
    phoneNumber: string;
    missionId: number;
    selectedIngredients: string[];
    sessionId: string;
    extra?: Record<string, any>;
  };
};

export type GetLeaderBoardArgs = {
  phoneNumber: string;
};

// Responses
export type GetRandomMissionResponse = CommonResponse<{
  sessionId: string;
  mission: Mission;
}>;

export type AllocateVoucherMixAndMatchResponse =
  CommonResponse<AllocateVoucher>;

export type GetLeaderboardResponse = CommonResponse<LeaderBoardItem[]>;

export const mixAndMatchServices = {
  getRandomMission: async (): Promise<GetRandomMissionResponse> => {
    const response = await fetchInstance(
      `${MIX_AND_MATCH_ENDPOINT}/random-mission`
    );

    const data = await response.json();

    return data;
  },
  allocateVoucher: async (
    args: AllocateVoucherMixAndMatchArgs
  ): Promise<AllocateVoucherMixAndMatchResponse> => {
    // Get utm params
    const searchParams = getSearchParamsAsObject();
    const utmObjects = {};

    Object.keys(searchParams).forEach((key) => {
      if (key.startsWith("utm_")) {
        utmObjects[key] = searchParams[key];
      }
    });

    const response = await fetchInstance(`${MIX_AND_MATCH_ENDPOINT}/allocate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        ...args.bodyData,
        extra: {
          ...utmObjects,
          ...args.bodyData.extra, // Merge with any additional extra data
        },
      }),
    });

    const data = await response.json();

    return data;
  },
  getLeaderBoard: async (
    args: GetLeaderBoardArgs
  ): Promise<GetLeaderboardResponse> => {
    const { phoneNumber } = args;

    const response = await fetchInstance(
      `${MIX_AND_MATCH_ENDPOINT}/leaderboard?phoneNumber=${phoneNumber}`
    );

    const data = await response.json();

    return data as GetLeaderboardResponse;
  },
};
