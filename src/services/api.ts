// Libraries
import { getAccessToken } from "zmp-sdk/apis";
import * as Sentry from "@sentry/react";

// Constants
import { APP_CONFIG } from "constant";

const { API_ZMA_URL, SANDBOX_API_ZMA_URL, PRODUCTION_API_ZMA_URL } = APP_CONFIG;

const searchPrams = new URLSearchParams(window.location.search);
const env = searchPrams.get("env");
const BASE_URL = `${
  `${env}`.toLowerCase() === "testing" ? SANDBOX_API_ZMA_URL : API_ZMA_URL
}`;

export const fetchInstance = async (
  input: RequestInfo | URL,
  init?: RequestInit
) => {
  try {
    const accessToken = await getAccessToken();

    const response = await fetch(`${BASE_URL}${input}`, {
      ...init,
      headers: {
        Authorization: `Bearer ${
          accessToken || import.meta.env.VITE_DEV_ACCESS_TOKEN
        }`,
        ...init?.headers,
      },
    });

    switch (response?.status) {
      case 401:
      case 403: {
        Sentry.captureException(response);

        const data = await response.json();

        throw new Error(data?.message);
      }

      default:
        break;
    }

    return response;
  } catch (error) {
    Sentry.captureException({
      input,
      init,
      error,
    });

    throw error;
  }
};
