// Types
import { CommonResponse, GetListParams } from "types";

// Api
import { fetchInstance } from "./api";

// Schemas
import { CheckCanAllocateDeal, CheckCanAllocateVoucher, Deal, GlobalTracking } from "schemas";

const OBJECT_ENDPOINT = "/zma/ggg/form/object";

export type GetListDealResponse = CommonResponse<{
  deals: Deal[];
  globalTracking: GlobalTracking;
}>;

export type CheckCanAllocateVoucherResponse =
  CommonResponse<CheckCanAllocateVoucher>;

export const objectServices = {
  getListDeal: async (): Promise<GetListDealResponse> => {
    const response = await fetchInstance(`${OBJECT_ENDPOINT}/deal`, {
      method: "GET",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify({
      //     ...params,
      //   }),
    });
    const data = await response.json();

    return data;
  },

  checkCanAllocateVoucher: async (
    CheckCanAllocateVoucher = {}
  ): Promise<CheckCanAllocateVoucherResponse> => {
    const response = await fetchInstance(`${OBJECT_ENDPOINT}/deals`);
    const data = await response.json();

    return data;
  },
};
