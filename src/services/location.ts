import { CommonResponse } from "types";
import { fetchInstance } from "./api";

export interface GetVietNamProvinceListArgs {
  country?: string;
}

const LOCATION_ENDPOINT = `/location/vietnam/province`;

export const locationServices = {
  getVietNamProvinceList: async (
    args?: GetVietNamProvinceListArgs
  ): Promise<CommonResponse<any>> => {
    const response = await fetchInstance(LOCATION_ENDPOINT, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    return data;
  },
};
