import openPackageSound from "assets/sound-effects/open-package.mp3";
// import summerMusic from "assets/music/summer.mp3";
import bubblePopSound from "assets/sound-effects/bubble-pop.mp3";

export const sounds = {
  openPackage: new Howl({
    src: [openPackageSound],
    volume: 1,
  }),
  // summer: new Howl({
  //   src: ["https://st-media-template.antsomi.com/file/summer.mp3"],
  //   volume: 0.4,
  //   html5: true,
  //   autoplay: true,
  //   preload: false,
  //   // loop: true,
  //   onend: () => {
  //     sounds.summer.play();
  //   },
  // }),
  bubblePop: new Howl({
    src: [bubblePopSound],
    volume: 1,
  }),
};
