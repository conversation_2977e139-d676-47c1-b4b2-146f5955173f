// Libraries
import { atom, selector } from "recoil";
import { zaloServices } from "services";
import { getUserInfo } from "zmp-sdk";

// Apis
import { getAccessToken, getPhoneNumber } from "zmp-sdk/apis";

// Utils
import { formatVietnamesePhoneNumber } from "utils";

export const userState = selector({
  key: "user",
  get: () =>
    getUserInfo({
      avatarType: "normal",
    }),
});

export const displayNameState = atom({
  key: "displayName",
  default: "",
});

export const luckyMoneyState = atom({
  key: "luckyMoney",
  default: {
    isFullLoading: true,
  },
});

export const loyaltyState = atom({
  key: "loyalty",
  default: {
    schemeFilter: {
      point: "all",
    },
    isOpenSummerGamePopup: true,
  },
});

export const onboardingState = atom({
  key: "onboarding",
  default: {
    isShowSplash: true,
    isShowWalkThrough: true,
  },
});
