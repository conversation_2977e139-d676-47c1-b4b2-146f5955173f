// Schemas
import { ConsolationMessages, Utm, Voucher } from "schemas";

// Utils
import { isExpired } from "./dateTime";
import { getSearchParamsAsObject } from "./browser";
import { LUCKY_MONEY_CONFIG } from "constant";

/**
 * Checks if the given voucher is available.
 * A voucher is considered available if it's status is "available" and the expiry date is in the future.
 *
 * @param {Voucher} voucher - The voucher to check.
 * @returns {boolean} true if the voucher is available, false otherwise.
 */
export const checkAvailableVoucher = (voucher?: Voucher): boolean => {
  const { status, expiryDate } = voucher || {};

  if (isExpired(expiryDate || "")) return false;

  return status === "available";
};

/**
 * Returns a random consolation message from the given list of messages.
 * If the `utm_content` query parameter is present, it will be used to select the list of messages.
 * Otherwise, the default list with key "other" will be used.
 *
 * @param {ConsolationMessages} consolationMessages - The list of messages.
 * @returns {string} A random consolation message.
 */
export const getRandomConsolationMessages = (
  consolationMessages: ConsolationMessages
): string => {
  const { utm_content } = getSearchParamsAsObject<Utm>();

  let consolationMessageList: string[] = consolationMessages["other"];

  if (utm_content) {
    Object.keys(consolationMessages).some((key) => {
      if (key.toLowerCase() === utm_content.toLowerCase()) {
        consolationMessageList = consolationMessages[key];
      }
    });
  }

  return (
    consolationMessageList[
      Math.floor(Math.random() * consolationMessageList.length)
    ] || LUCKY_MONEY_CONFIG.CONSOLATION_MESSAGE_DEFAULT
  );
};
