// React core
import React from "react";
import { createRoot } from "react-dom/client";
import * as Sen<PERSON> from "@sentry/react";

// Tailwind stylesheet
import "css/tailwind.scss";

// ZaUI stylesheet
import "zmp-ui/zaui.css";

// Your stylesheet
import "css/app.scss";

// Expose app configuration
import appConfig from "../app-config.json";

Sentry.init({
  // dsn: "https://<EMAIL>/4508538009878528",
  dsn: "https://<EMAIL>/13",
  integrations: [
    Sentry.browserTracingIntegration(),
    Sentry.replayIntegration(),
  ],
  // Tracing
  tracesSampleRate: 1.0, //  Capture 100% of the transactions
  // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
  enabled: process.env.NODE_ENV !== "development",
  // enabled: true,
  environment: process.env.NODE_ENV,
  tracePropagationTargets: ["localhost", /^https:\/\/yourserver\.io\/api/],
  // Session Replay
  replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
});

if (!window.APP_CONFIG) {
  window.APP_CONFIG = appConfig;
}

// Mount the app
import App from "components/app";
const root = createRoot(document.getElementById("app")!);
root.render(React.createElement(App));
