export const ZMA_ERROR_CODE = {
  ACTION_LIMIT: -203,
} as const;

export const API_ERROR_CODE = {
  NOT_ENOUGH_POINTS: 313,
  BAD_REQUEST: 400,
};

type ZMA_ERROR_CODE = (typeof ZMA_ERROR_CODE)[keyof typeof ZMA_ERROR_CODE];

export const ZMA_ERROR: Partial<
  Record<
    ZMA_ERROR_CODE,
    {
      code: ZMA_ERROR_CODE;
      message: string;
    }
  >
> = {
  [ZMA_ERROR_CODE.ACTION_LIMIT]: {
    code: ZMA_ERROR_CODE.ACTION_LIMIT,
    message:
      "Bạn đã gửi quá nhiều yêu cầu trong thời gian ngắn. Vui lòng khởi động lại ứng dụng và thử lại nhé!",
  },
};

export const API_ERROR = {
  [API_ERROR_CODE.NOT_ENOUGH_POINTS]: {
    code: API_ERROR_CODE.NOT_ENOUGH_POINTS,
    message:
      "Bạn không đủ điểm để đổi voucher này. <PERSON><PERSON>y tích lũy thêm điểm nhé!",
  },
};
