import React from 'react';
import { UserDrips } from 'components';

export type Route = {
  key: RouteKey;
  path: string;
  name: string;
  title: string;
  showBackButton: boolean;
  hideSafeArea?: boolean;
  navRightExtra?: React.ReactNode;
  pageCate?: string;
};

export const ROUTE_KEYS = {
  // Home
  HOME: 'HOME',

  // Account
  ACCOUNT: 'ACCOUNT',
  ACCOUNT_INFO: 'ACCOUNT_INFO',

  // About
  ABOUT: 'ABOUT',

  // Gift
  GIFT_FORM: 'GIFT_FORM',
  GIFT: 'GIFT',
  GIFT_DETAIL: 'GIFT-DETAIL',

  // Transaction History
  TRANSACTION_HISTORIES: 'TRANSACTION_HISTORIES',
  TRANSACTION_HISTORY: 'TRANSACTION_HISTORY',

  // Accumulate Points
  ACCUMULATE_POINTS: 'ACCUMULATE_POINTS',

  // Voucher Detail
  VOUCHER_DETAIL: 'VOUCHER-DETAIL',

  // Survey Order
  SURVEY_ORDER: 'SURVEY_ORDER',

  // Brand List
  BRAND_LIST: 'BRAND_LIST',

  // Reservation History
  RESERVATION_HISTORY: 'RESERVATION_HISTORY',

  // Terms and Conditions
  TERMS_AND_CONDITIONS: 'TERMS_AND_CONDITIONS',

  // Subscription
  SUBSCRIPTION: 'SUBSCRIPTION',

  // Restaurant
  RESTAURANT_DETAIL: 'RESTAURANT_DETAIL',
  RESTAURANT_GALLERY: 'RESTAURANT_GALLERY',
  RESTAURANT_OFFERS: 'RESTAURANT_OFFERS',

  // Brand Menu
  BRAND_MENUS: 'BRAND_MENUS',
  BRAND_MENU: 'BRAND_MENU',
} as const;

export type RouteKey = (typeof ROUTE_KEYS)[keyof typeof ROUTE_KEYS];

export const ROUTES: Record<RouteKey, Route> = {
  [ROUTE_KEYS.HOME]: {
    key: ROUTE_KEYS.HOME,
    path: '/',
    name: 'home',
    title: 'Trang Chủ',
    showBackButton: false,
  },
  [ROUTE_KEYS.GIFT_FORM]: {
    key: ROUTE_KEYS.GIFT_FORM,
    path: '/gift-form',
    name: 'gift-form',
    title: 'Highlands Coffee - Điền thông tin để nhận quà',
    showBackButton: true,
  },
  [ROUTE_KEYS.TRANSACTION_HISTORIES]: {
    key: ROUTE_KEYS.TRANSACTION_HISTORIES,
    path: '/transaction-histories',
    name: 'transaction-histories',
    title: 'Lịch sử giao dịch',
    showBackButton: true,
    pageCate: 'history_transaction',
  },
  [ROUTE_KEYS.TRANSACTION_HISTORY]: {
    key: ROUTE_KEYS.TRANSACTION_HISTORY,
    path: '/transaction-histories/:transactionId',
    name: 'transaction-history-detail',
    title: 'Chi tiết lịch sử tích điểm',
    showBackButton: true,
    pageCate: 'history_transaction_detail',
  },
  [ROUTE_KEYS.ACCUMULATE_POINTS]: {
    key: ROUTE_KEYS.ACCUMULATE_POINTS,
    path: '/accumulate-points',
    name: 'accumulate-points',
    title: 'QR Tích điểm',
    showBackButton: true,
    pageCate: 'qr_earn_point',
  },
  [ROUTE_KEYS.GIFT]: {
    key: ROUTE_KEYS.GIFT,
    path: '/gift',
    name: 'gift',
    title: 'Quà Tặng',
    showBackButton: true,
    navRightExtra: <UserDrips />,
    pageCate: 'list_scheme',
  },
  [ROUTE_KEYS.GIFT_DETAIL]: {
    key: ROUTE_KEYS.GIFT_DETAIL,
    path: '/gift/:giftId',
    name: 'gift-detail',
    title: 'Quà Tặng',
    showBackButton: true,
    navRightExtra: <UserDrips />,
    pageCate: 'gift_detail',
  },
  [ROUTE_KEYS.ACCOUNT]: {
    key: ROUTE_KEYS.ACCOUNT,
    path: '/account',
    name: 'account',
    title: 'Trang cá nhân',
    showBackButton: true,
    pageCate: 'my_account',
  },
  [ROUTE_KEYS.ACCOUNT_INFO]: {
    key: ROUTE_KEYS.ACCOUNT_INFO,
    path: '/account/info',
    name: 'account-info',
    title: 'Thông tin tài khoản',
    showBackButton: true,
  },
  [ROUTE_KEYS.VOUCHER_DETAIL]: {
    key: ROUTE_KEYS.VOUCHER_DETAIL,
    path: '/vouchers/:voucherId',
    name: 'voucher-detail',
    title: 'Quà tặng của tôi',
    showBackButton: true,
    pageCate: 'edit_account',
  },
  [ROUTE_KEYS.SURVEY_ORDER]: {
    key: ROUTE_KEYS.SURVEY_ORDER,
    path: '/survey-order',
    name: 'survey-order',
    title: 'Khảo sát',
    showBackButton: true,
  },
  [ROUTE_KEYS.BRAND_LIST]: {
    key: ROUTE_KEYS.BRAND_LIST,
    path: '/brand-list',
    name: 'brand-list',
    title: 'Thương hiệu áp dụng',
    showBackButton: true,
  },
  [ROUTE_KEYS.RESERVATION_HISTORY]: {
    key: ROUTE_KEYS.RESERVATION_HISTORY,
    path: '/reservation-history',
    name: 'reservation-history',
    title: 'Lịch sử đặt bàn',
    showBackButton: true,
  },
  [ROUTE_KEYS.TERMS_AND_CONDITIONS]: {
    key: ROUTE_KEYS.TERMS_AND_CONDITIONS,
    path: '/terms-and-conditions',
    name: 'terms-and-conditions',
    title: 'Điều khoản sử dụng',
    showBackButton: true,
  },
  [ROUTE_KEYS.ABOUT]: {
    key: ROUTE_KEYS.ABOUT,
    path: '/about',
    name: 'about',
    title: 'Về Golden SpoonS',
    showBackButton: true,
  },
  [ROUTE_KEYS.SUBSCRIPTION]: {
    key: ROUTE_KEYS.SUBSCRIPTION,
    path: '/subscription',
    name: 'subscription',
    title: 'Gói Không giới hạn của tôi',
    showBackButton: true,
  },
  [ROUTE_KEYS.RESTAURANT_DETAIL]: {
    key: ROUTE_KEYS.RESTAURANT_DETAIL,
    path: '/restaurant/:restaurantId',
    name: 'restaurant-detail',
    title: 'Thông tin nhà hàng',
    showBackButton: true,
  },
  [ROUTE_KEYS.RESTAURANT_GALLERY]: {
    key: ROUTE_KEYS.RESTAURANT_GALLERY,
    path: '/restaurant/:restaurantId/gallery',
    name: 'restaurant-gallery',
    title: 'Thông tin nhà hàng',
    showBackButton: true,
  },
  [ROUTE_KEYS.RESTAURANT_OFFERS]: {
    key: ROUTE_KEYS.RESTAURANT_GALLERY,
    path: '/restaurant/:restaurantId/offer',
    name: 'restaurant-offers',
    title: 'Ưu đãi nhà hàng',
    showBackButton: true,
  },
  [ROUTE_KEYS.BRAND_MENU]: {
    key: ROUTE_KEYS.BRAND_MENU,
    path: '/brand/:brandId/menu/:menuId',
    name: 'brand-menu',
    title: 'Chi tiết menu',
    showBackButton: true,
  },
  [ROUTE_KEYS.BRAND_MENUS]: {
    key: ROUTE_KEYS.BRAND_MENUS,
    path: '/brand/:brandId/menu',
    name: 'brand-menus',
    title: 'Hot menu',
    showBackButton: true,
  },
};
