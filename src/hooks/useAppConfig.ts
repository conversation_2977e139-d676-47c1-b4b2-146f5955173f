import { APP_CONFIG, LUCKY_MONEY_CONFIG } from "constant";
import dayjs from "dayjs";
import { useGetAppSettings } from "queries";
import { useMemo } from "react";

const { AFFILIATE, MIX_AND_MATCH } = APP_CONFIG.GAMES;

// Variables
const searchPrams = new URLSearchParams(window.location.search);
const env = searchPrams.get("env");

export const useAppConfig = () => {
  const { data: appSettingsData, isLoading } = useGetAppSettings();
  const { games, globals } = appSettingsData?.data || {};
  const { affiliate, mixAndMatch, luckyMoney } = games || {};
  const { systemErrorMessages } = globals || {};

  const isLuckyMoneyGameValid = useMemo(() => {
    const { luckyMoney } = games || {};

    return dayjs().isBefore(
      dayjs(luckyMoney?.eventEndDate || LUCKY_MONEY_CONFIG.END_DATE || "")
    );
  }, [games]);

  const gameValidation = useMemo(() => {
    const gameValidation: any = {};

    const getMessage = ({ startDate, endDate, endDateDefaultMessage }) => {
      return dayjs().isBefore(dayjs(startDate))
        ? systemErrorMessages?.gameNotReady ||
        APP_CONFIG.SYSTEM_ERROR_MESSAGES.gameNotReady
        : systemErrorMessages?.gameEnd ||
        endDateDefaultMessage ||
        APP_CONFIG.SYSTEM_ERROR_MESSAGES.gameEnd;
    };

    {
      gameValidation.luckyMoney = {
        isValid: isLuckyMoneyGameValid,
        endDate: luckyMoney?.eventEndDate || LUCKY_MONEY_CONFIG.END_DATE || "",
      };
    }

    {
      const affiliateStartDate =
        affiliate?.eventStartDate || AFFILIATE.START_DATE;
      const affiliateEndDate = affiliate?.eventEndDate || AFFILIATE.END_DATE;

      gameValidation.affiliate = {
        isValid:
          dayjs().isBefore(dayjs(affiliateEndDate)) &&
          dayjs().isAfter(dayjs(affiliateStartDate)),
        endDate: affiliateEndDate,
        startDate: affiliateStartDate,
        notAvailableMessage: getMessage({
          startDate: affiliateStartDate,
          endDate: affiliateEndDate,
          endDateDefaultMessage: APP_CONFIG.SYSTEM_ERROR_MESSAGES.gameEnd,
        }),
      };
    }

    {
      const mixAndMatchStartDate =
        mixAndMatch?.eventStartDate || MIX_AND_MATCH.START_DATE;
      const mixAndMatchEndDate =
        mixAndMatch?.eventEndDate || MIX_AND_MATCH.END_DATE;

      gameValidation.mixAndMatch = {
        isValid:
          `${env}`.toLowerCase() === "testing" // Only validate in testing environment
            ? true
            : dayjs().isBefore(dayjs(mixAndMatchEndDate)) &&
            dayjs().isAfter(dayjs(mixAndMatchStartDate)),
        endDate: mixAndMatchEndDate,
        startDate: mixAndMatchStartDate,
        notAvailableMessage: getMessage({
          startDate: mixAndMatchStartDate,
          endDate: mixAndMatchEndDate,
          endDateDefaultMessage: APP_CONFIG.GAMES.MIX_AND_MATCH.GAME_END_MSG,
        }),
      };
    }

    return gameValidation;
  }, [
    systemErrorMessages?.gameNotReady,
    systemErrorMessages?.gameEnd,
    isLuckyMoneyGameValid,
    luckyMoney?.eventEndDate,
    affiliate?.eventStartDate,
    affiliate?.eventEndDate,
    mixAndMatch?.eventStartDate,
    mixAndMatch?.eventEndDate,
  ]);

  return {
    code: appSettingsData?.code,
    appSettings: appSettingsData?.data,
    isLoading,
    isLuckyMoneyGameValid,
    gameValidation,
  };
};
