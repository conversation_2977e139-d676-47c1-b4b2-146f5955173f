// Libraries
import { useLocalStorage } from "usehooks-ts";
import isEqual from "fast-deep-equal";
import { SHA256 } from "crypto-js";

// Hooks
import { useUserInfo } from "./useUserInfo";
import { useDeepCompareEffect } from "./useDeepCompareEffect";

// Constants
import { LOCAL_STORAGE_KEY } from "constant";
import { callCdpEvent, formatEventDateTime, formatEventGender } from "utils";

export const useSyncCustomer = () => {
  const { loyaltyCustomer, userInfo } = useUserInfo();
  const [cachedLoyaltyCustomer, setCachedLoyaltyCustomer] = useLocalStorage(
    LOCAL_STORAGE_KEY.LOYALTY_CUSTOMER,
    loyaltyCustomer
  );

  useDeepCompareEffect(() => {
    if (loyaltyCustomer && !isEqual(loyaltyCustomer, cachedLoyaltyCustomer)) {
      const {
        customerName,
        gender,
        membershipLevel,
        membershipCard,
        availablePoints,
        addressDetailInfo,
        favoriteDish,
        favoriteStore,
        dateExpire,
        createdAt,
        dateOfBirth,
      } = loyaltyCustomer || {};

      const customerId = SHA256(userInfo?.phoneNumber).toString();

      // Call cdp event sync customer
      callCdpEvent({
        ec: "customer",
        ea: "sync",
        uId: userInfo?.id,
        dims: {
          customers: {
            customer_id: customerId,
            name: userInfo?.name || customerName,
            phone: userInfo?.phoneNumber,
            gender: formatEventGender(gender),
            birthday:
              formatEventDateTime(dateOfBirth, "DD/MM/YYYY") ||
              dateOfBirth ||
              "",
            membership_level: membershipLevel,
            membership_card: membershipCard,
            available_points: availablePoints,
            address_city: addressDetailInfo?.state,
            address_district: addressDetailInfo?.district,
            favorite_dish: favoriteDish || "",
            favorite_store: favoriteStore || "",
            expiry_date: formatEventDateTime(dateExpire, "DD/MM/YYYY"),
            loyalty_create_date: formatEventDateTime(createdAt),
          },
        },
        data: {},
      });

      // Set local storage
      setCachedLoyaltyCustomer(loyaltyCustomer);
    }
  }, [
    cachedLoyaltyCustomer,
    loyaltyCustomer,
    userInfo,
    setCachedLoyaltyCustomer,
  ]);
};
