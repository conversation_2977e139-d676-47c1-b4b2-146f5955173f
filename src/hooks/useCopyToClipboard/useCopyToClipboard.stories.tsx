import { useState } from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { useCopyToClipboard } from './index';
import { Button } from '../../components/ui/Button';

// Demo component to showcase the hook
const CopyToClipboardDemo = ({
  content,
  showToast,
  toastMessage,
  buttonText = 'Copy Text',
}: {
  content: string;
  showToast?: boolean;
  toastMessage?: string;
  buttonText?: string;
}) => {
  const { copyToClipboard, isCopied } = useCopyToClipboard({
    content,
    showToast,
    toastMessage,
  });

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', maxWidth: '400px' }}>
      <div style={{ padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <strong>Content to copy:</strong>
        <div style={{ marginTop: '8px', fontFamily: 'monospace', fontSize: '14px' }}>{content}</div>
      </div>

      <Button onClick={copyToClipboard} color={isCopied ? 'success' : 'primary'}>
        {isCopied ? 'Đã copy!' : buttonText}
      </Button>

      <div style={{ fontSize: '12px', color: '#666' }}>
        Status: {isCopied ? '✅ Copied' : '⏳ Ready to copy'}
      </div>
    </div>
  );
};

// Interactive demo with custom content
const InteractiveCopyDemo = () => {
  const [customContent, setCustomContent] = useState('Paste your text here...');
  const [showToast, setShowToast] = useState(true);
  const [toastMessage, setToastMessage] = useState('Đã sao chép');

  const { copyToClipboard, isCopied } = useCopyToClipboard({
    content: customContent,
    showToast,
    toastMessage,
  });

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', maxWidth: '500px' }}>
      <div>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
          Content to copy:
        </label>
        <textarea
          value={customContent}
          onChange={(e) => setCustomContent(e.target.value)}
          style={{
            width: '100%',
            minHeight: '100px',
            padding: '12px',
            border: '1px solid #ddd',
            borderRadius: '8px',
            resize: 'vertical',
          }}
        />
      </div>

      <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
        <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <input
            type="checkbox"
            checked={showToast}
            onChange={(e) => setShowToast(e.target.checked)}
          />
          Show toast notification
        </label>
      </div>

      {showToast && (
        <div>
          <label style={{ display: 'block', marginBottom: '8px' }}>Toast message:</label>
          <input
            type="text"
            value={toastMessage}
            onChange={(e) => setToastMessage(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #ddd',
              borderRadius: '8px',
            }}
          />
        </div>
      )}

      <Button onClick={copyToClipboard} color={isCopied ? 'success' : 'primary'} size="large">
        {isCopied ? '✅ Đã copy!' : '📋 Copy to clipboard'}
      </Button>

      <div
        style={{
          padding: '12px',
          backgroundColor: isCopied ? '#e8f5e8' : '#f0f0f0',
          borderRadius: '8px',
          fontSize: '14px',
        }}
      >
        <strong>Hook state:</strong> {isCopied ? 'Text copied successfully!' : 'Ready to copy'}
      </div>
    </div>
  );
};

const meta = {
  title: 'Hooks/useCopyToClipboard',
  component: CopyToClipboardDemo,
  parameters: {
    docs: {
      description: {
        component: `
Hook để copy text vào clipboard với các tính năng:
- Hỗ trợ modern Clipboard API và fallback cho browser cũ
- Tùy chọn hiển thị toast notification
- Tùy chỉnh message toast
- Trạng thái isCopied tự động reset sau 2 giây
        `,
      },
    },
  },
} satisfies Meta<typeof CopyToClipboardDemo>;

export default meta;

type Story = StoryObj<typeof meta>;

export const BasicUsage: Story = {
  args: {
    content: 'Hello Golden Gate Group!',
  },
};

export const WithoutToast: Story = {
  args: {
    content: 'This will copy silently without showing toast',
    showToast: false,
    buttonText: 'Copy silently',
  },
};

export const CustomToastMessage: Story = {
  args: {
    content: 'USER123456',
    showToast: true,
    toastMessage: 'Đã sao chép mã khách hàng',
    buttonText: 'Copy customer code',
  },
};

export const LongContent: Story = {
  args: {
    content: `
Golden Gate Group - Vietnam's restaurant chain pioneer with 40+ brands and 500+ restaurants.
Founded in 2005, serving 18 million customers annually across 42 provinces and cities.
Main culinary styles: Hot Pot, BBQ, Asian, European and Coffee shops.
Contact: <EMAIL> | Phone: +84 ***********
    `.trim(),
    toastMessage: 'Đã sao chép thông tin Golden Gate Group',
    buttonText: 'Copy company info',
  },
};

export const JsonContent: Story = {
  args: {
    content: JSON.stringify(
      {
        restaurant: 'Golden Gate BBQ',
        location: 'Ho Chi Minh City',
        cuisine: 'BBQ',
        rating: 4.8,
        specialties: ['Bulgogi', 'Grilled Beef', 'Korean BBQ'],
      },
      null,
      2,
    ),
    toastMessage: 'Đã sao chép dữ liệu JSON',
    buttonText: 'Copy JSON data',
  },
};

export const VoucherCode: Story = {
  args: {
    content: 'GOLDGATE2024-50OFF',
    toastMessage: 'Đã sao chép mã voucher',
    buttonText: '🎫 Copy voucher code',
  },
};

export const PhoneNumber: Story = {
  args: {
    content: '+84 ***********',
    toastMessage: 'Đã sao chép số điện thoại',
    buttonText: '📞 Copy phone',
  },
};

export const EmailAddress: Story = {
  args: {
    content: '<EMAIL>',
    toastMessage: 'Đã sao chép địa chỉ email',
    buttonText: '✉️ Copy email',
  },
};

export const RestaurantAddress: Story = {
  args: {
    content: '123 Nguyễn Huệ, Quận 1, TP. Hồ Chí Minh',
    toastMessage: 'Đã sao chép địa chỉ nhà hàng',
    buttonText: '📍 Copy address',
  },
};

export const InteractiveDemo: Story = {
  args: {
    content: '',
  },
  render: InteractiveCopyDemo,
  parameters: {
    docs: {
      description: {
        story: 'Demo tương tác cho phép bạn tùy chỉnh content, toast settings và test hook.',
      },
    },
  },
};

export const MultipleInstances: Story = {
  args: {
    content: '',
  },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <CopyToClipboardDemo
        content="Customer ID: GG123456"
        toastMessage="Đã copy mã khách hàng"
        buttonText="Copy Customer ID"
      />

      <CopyToClipboardDemo
        content="Table booking: 2024-09-09 19:00"
        toastMessage="Đã copy thông tin đặt bàn"
        buttonText="Copy booking info"
      />

      <CopyToClipboardDemo
        content="Voucher: WELCOME20"
        toastMessage="Đã copy mã giảm giá"
        buttonText="Copy voucher"
      />

      <CopyToClipboardDemo
        content="Secret data - no toast"
        showToast={false}
        buttonText="Copy quietly"
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Nhiều instances của hook hoạt động độc lập với nhau.',
      },
    },
  },
};

