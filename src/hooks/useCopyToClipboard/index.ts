import { useCallback, useState } from 'react';
import { Toast } from '@antscorp/ama-ui';

export function useCopyToClipboard({
  content,
  showToast = true,
  toastMessage = 'Đã sao chép',
}: {
  content: string;
  showToast?: boolean;
  toastMessage?: string;
}) {
  const [isCopied, setIsCopied] = useState(false);

  const copyToClipboard = useCallback(async () => {
    try {
      // Create a temporary textarea for copying text in mini app environment
      const textArea = document.createElement('textarea');
      
      textArea.value = content;
      textArea.style.position = 'fixed';
      textArea.style.left = '-9999px';
      textArea.style.top = '-9999px';
      textArea.style.opacity = '0';
      textArea.style.pointerEvents = 'none';
      textArea.readOnly = true;

      document.body.appendChild(textArea);

      // Select the text
      textArea.select();
      textArea.setSelectionRange(0, content.length);

      // Copy using document.execCommand (works in mini app WebView)
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (!successful) {
        throw new Error('Copy command failed');
      }

      setIsCopied(true);

      if (showToast) {
        Toast.show(toastMessage);
      }

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (error) {
      console.error('Failed to copy text to clipboard:', error);
      setIsCopied(false);
      
      // Show error toast if enabled
      if (showToast) {
        Toast.show('Không thể sao chép văn bản');
      }
    }
  }, [content, showToast, toastMessage]);

  return {
    isCopied,
    copyToClipboard,
  };
}
