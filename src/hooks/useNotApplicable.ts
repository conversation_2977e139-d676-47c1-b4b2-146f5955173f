// Libraries
import { useMemo } from "react";
import dayjs from "dayjs";

// Hooks
import { useAppConfig } from "./useAppConfig";

// Constants
import { LUCKY_MONEY_CONFIG } from "constant";

interface UseNotApplicableProps {
  scheme_id?: string;
}

export const useNotApplicable = (props: UseNotApplicableProps) => {
  const { scheme_id } = props;

  const { appSettings } = useAppConfig();

  // Memos
  const { isShowNotApplicableMessage, notApplicableMessage } = useMemo(() => {
    const notApplicableDates =
      appSettings?.games?.luckyMoney?.notApplicableDates ||
      LUCKY_MONEY_CONFIG.NOT_APPLICABLE_DATES ||
      [];
    const notApplicableSchemes =
      appSettings?.games?.luckyMoney?.notApplicableSchemes ||
      LUCKY_MONEY_CONFIG.NOT_APPLICABLE_SCHEMES ||
      [];

    return {
      isShowNotApplicableMessage:
        // isTodayInRange(notApplicableDates[0], notApplicableDates[1]) &&
        notApplicableSchemes?.includes(`${scheme_id}`),
      notApplicableMessage: `Không áp dụng dịp Lễ Tết từ ${dayjs(
        notApplicableDates[0]
      ).format("DD/MM")} - đến hết ${dayjs(notApplicableDates[1]).format(
        "DD/MM"
      )}`,
    };
  }, [
    appSettings?.games?.luckyMoney?.notApplicableDates,
    appSettings?.games?.luckyMoney?.notApplicableSchemes,
    scheme_id,
  ]);

  return {
    isShowNotApplicableMessage,
    notApplicableMessage,
  };
};
