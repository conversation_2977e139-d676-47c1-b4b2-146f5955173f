// Libraries
import { SHA256 } from "crypto-js";

// Hooks
import { useDeepCompareEffect } from "./useDeepCompareEffect";
import { useUserInfo } from "./useUserInfo";

// Utils
import { callCdpEvent, dayjs, formatEventDateTime } from "utils";

// Queries
import { useCreateLoyaltyCustomer } from "queries";

export const useRegisterLoyaltyCustomer = () => {
  const { userInfo, isRegistered } = useUserInfo();

  // Queries
  const { mutateAsync: createLoyaltyCustomer } = useCreateLoyaltyCustomer({});

  // Handle call cdp event identify and register customer loyalty
  useDeepCompareEffect(() => {
    if (userInfo && userInfo?.phoneNumber && userInfo?.name) {
      const customerId = SHA256(userInfo?.phoneNumber).toString();

      callCdpEvent({
        ec: "user",
        ea: "identify",
        uId: userInfo.id,
        dims: {
          users: {
            user_id: userInfo.id,
            identify_event: "allow_miniapp",
            identify_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            id_by_oa: userInfo.idByOA,
            name: userInfo.name,
            phone: userInfo.phoneNumber,
          },
          customers: {
            name: userInfo.name,
            zalo_name: userInfo.name,
            phone: userInfo.phoneNumber,
            customer_id: customerId,
            zalo_uid: userInfo.id,
            id_by_oa: userInfo.idByOA,
          },
        },
        data: {
          identify_id: customerId,
          identify_event: "allow_miniapp",
        },
      });

      (async () => {
        if (!isRegistered) {
          const { data: loyaltyCustomer } = await createLoyaltyCustomer({
            data: {
              customerName: `${userInfo.name}`,
              phoneNumber: userInfo.phoneNumber,
            },
          });

          // Call Cdp event sign up
          callCdpEvent({
            uId: userInfo.id,
            ea: "sign_up",
            ec: "user",
            dims: {
              customers: {
                customer_id: customerId,
                name: userInfo?.name,
                phone: userInfo?.phoneNumber,
                ...(loyaltyCustomer?.createdAt && {
                  loyalty_create_date: formatEventDateTime(
                    loyaltyCustomer?.createdAt
                  ),
                }),
              },
            },
            data: {
              customer_phone: userInfo?.phoneNumber,
              customer_name: userInfo?.name,
            },
          });
        }
      })();
    }
  }, [createLoyaltyCustomer, isRegistered, userInfo]);
};
