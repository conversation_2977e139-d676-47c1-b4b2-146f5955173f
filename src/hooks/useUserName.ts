import { useMemo } from 'react';
import { useUserInfo } from './useUserInfo';

export type Nationality = 'VN' | 'US' | 'CN' | 'JP' | 'KR' | 'OTHER';

export interface UserNameInfo {
  firstName: string;
  lastName: string;
  fullName: string;
  displayName: string;
}

interface UseUserNameProps {
  nationality?: Nationality;
  citizenship?: Nationality;
}

export const useUserName = ({ nationality = 'VN' }: UseUserNameProps = {}): UserNameInfo => {
  const { userInfo, loyaltyCustomer } = useUserInfo();

  const userNameInfo = useMemo((): UserNameInfo => {
    // Ưu tiên sử dụng firstName và lastName từ loyaltyCustomer nếu có
    if (loyaltyCustomer?.firstName && loyaltyCustomer?.lastName) {
      return {
        firstName: loyaltyCustomer.firstName,
        lastName: loyaltyCustomer.lastName,
        fullName: `${loyaltyCustomer.firstName} ${loyaltyCustomer.lastName}`.trim(),
        displayName: `${loyaltyCustomer.firstName} ${loyaltyCustomer.lastName}`.trim(),
      };
    }

    // Nếu không có firstName/lastName, parse từ customerName
    if (loyaltyCustomer?.customerName) {
      const parsed = parseNameByNationality(loyaltyCustomer.customerName, nationality);
      return {
        firstName: parsed.firstName,
        lastName: parsed.lastName,
        fullName: loyaltyCustomer.customerName,
        displayName: loyaltyCustomer.customerName,
      };
    }

    // Fallback về userInfo.name
    if (userInfo?.name) {
      const parsed = parseNameByNationality(userInfo.name, nationality);
      return {
        firstName: parsed.firstName,
        lastName: parsed.lastName,
        fullName: userInfo.name,
        displayName: userInfo.name,
      };
    }

    // Default values
    return {
      firstName: 'Chưa cập nhật',
      lastName: 'Chưa cập nhật',
      fullName: 'Chưa cập nhật',
      displayName: 'Chưa cập nhật',
    };
  }, [loyaltyCustomer, userInfo, nationality]);

  return userNameInfo;
};

/**
 * Parse tên theo quốc tịch
 * @param fullName Tên đầy đủ
 * @param nationality Quốc tịch
 * @returns Object chứa firstName và lastName
 */
function parseNameByNationality(
  fullName: string,
  nationality: Nationality,
): { firstName: string; lastName: string } {
  if (!fullName || fullName.trim() === '') {
    return { firstName: 'Chưa cập nhật', lastName: 'Chưa cập nhật' };
  }

  const trimmedName = fullName.trim();
  const nameParts = trimmedName.split(/\s+/).filter((part) => part.length > 0);

  if (nameParts.length === 0) {
    return { firstName: 'Chưa cập nhật', lastName: 'Chưa cập nhật' };
  }

  if (nameParts.length === 1) {
    // Chỉ có 1 từ - coi như là firstName
    return { firstName: nameParts[0], lastName: 'Chưa cập nhật' };
  }

  switch (nationality) {
    case 'VN':
      return parseVietnameseName(nameParts);
    case 'US':
    case 'OTHER':
      return parseWesternName(nameParts);
    case 'CN':
      return parseChineseName(nameParts);
    case 'JP':
      return parseJapaneseName(nameParts);
    case 'KR':
      return parseKoreanName(nameParts);
    default:
      return parseVietnameseName(nameParts);
  }
}

/**
 * Parse tên người Việt Nam
 * Quy tắc: Họ (1-2 từ) + Tên (1-3 từ)
 * Ví dụ: "Nguyễn Văn A" -> Họ: "Nguyễn Văn", Tên: "A"
 * Ví dụ: "Trần Thị B" -> Họ: "Trần Thị", Tên: "B"
 * Ví dụ: "Lê C" -> Họ: "Lê", Tên: "C"
 */
function parseVietnameseName(nameParts: string[]): { firstName: string; lastName: string } {
  if (nameParts.length === 2) {
    // 2 từ: Họ + Tên
    return { firstName: nameParts[1], lastName: nameParts[0] };
  }

  if (nameParts.length === 3) {
    // 3 từ: Họ + Tên đệm + Tên
    // Kiểm tra xem từ thứ 2 có phải là tên đệm không (Thị, Văn, Thành, etc.)
    const middleName = nameParts[1];
    const commonMiddleNames = ['Thị', 'Văn', 'Thành', 'Đức', 'Minh', 'Hoàng', 'Xuân', 'Thu'];

    if (commonMiddleNames.includes(middleName)) {
      // Họ + Tên đệm + Tên
      return { firstName: nameParts[2], lastName: `${nameParts[0]} ${nameParts[1]}` };
    } else {
      // Họ + Tên (2 từ) + Tên
      return { firstName: nameParts[2], lastName: `${nameParts[0]} ${nameParts[1]}` };
    }
  }

  if (nameParts.length >= 4) {
    // 4+ từ: Họ (1-2 từ) + Tên đệm (1-2 từ) + Tên
    // Lấy 1-2 từ cuối làm tên, phần còn lại làm họ
    const firstName = nameParts.slice(-1).join(' ');
    const lastName = nameParts.slice(0, -1).join(' ');
    return { firstName, lastName };
  }

  // Fallback: từ cuối là tên, phần còn lại là họ
  const firstName = nameParts[nameParts.length - 1];
  const lastName = nameParts.slice(0, -1).join(' ');
  return { firstName, lastName };
}

/**
 * Parse tên người phương Tây
 * Quy tắc: First Name + Last Name
 */
function parseWesternName(nameParts: string[]): { firstName: string; lastName: string } {
  if (nameParts.length === 2) {
    return { firstName: nameParts[0], lastName: nameParts[1] };
  }

  if (nameParts.length >= 3) {
    // First Name + Middle Name(s) + Last Name
    const firstName = nameParts[0];
    const lastName = nameParts[nameParts.length - 1];
    return { firstName, lastName };
  }

  return { firstName: nameParts[0], lastName: 'Chưa cập nhật' };
}

/**
 * Parse tên người Trung Quốc
 * Quy tắc: Họ (1 từ) + Tên (1-2 từ)
 */
function parseChineseName(nameParts: string[]): { firstName: string; lastName: string } {
  if (nameParts.length === 2) {
    return { firstName: nameParts[1], lastName: nameParts[0] };
  }

  if (nameParts.length >= 3) {
    const firstName = nameParts.slice(1).join(' ');
    const lastName = nameParts[0];
    return { firstName, lastName };
  }

  return { firstName: nameParts[0], lastName: 'Chưa cập nhật' };
}

/**
 * Parse tên người Nhật
 * Quy tắc: Họ + Tên
 */
function parseJapaneseName(nameParts: string[]): { firstName: string; lastName: string } {
  if (nameParts.length === 2) {
    return { firstName: nameParts[1], lastName: nameParts[0] };
  }

  if (nameParts.length >= 3) {
    const firstName = nameParts.slice(1).join(' ');
    const lastName = nameParts[0];
    return { firstName, lastName };
  }

  return { firstName: nameParts[0], lastName: 'Chưa cập nhật' };
}

/**
 * Parse tên người Hàn
 * Quy tắc: Họ + Tên
 */
function parseKoreanName(nameParts: string[]): { firstName: string; lastName: string } {
  if (nameParts.length === 2) {
    return { firstName: nameParts[1], lastName: nameParts[0] };
  }

  if (nameParts.length >= 3) {
    const firstName = nameParts.slice(1).join(' ');
    const lastName = nameParts[0];
    return { firstName, lastName };
  }

  return { firstName: nameParts[0], lastName: 'Chưa cập nhật' };
}
