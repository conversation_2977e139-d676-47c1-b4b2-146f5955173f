// Libraries
import { APP_CONFIG, TIER } from "constant";
import { isEmpty } from "lodash-es";
import {
  useGetLoyaltyCustomerDetail,
  useGetUserInfo,
  useGetUserSetting,
  useGetZaloInfo,
} from "queries";
import { useMemo } from "react";
import { useDebounceValue } from "usehooks-ts";

// Utils
import { formatVietnamesePhoneNumber } from "utils";

// Hooks
import { useAppConfig } from "./useAppConfig";

// Schemas
import { Tier } from "schemas";

interface UserInfoProps {}

export const useUserInfo = (props?: UserInfoProps) => {
  const {
    data: userInfoData,
    isLoading: isUserInfoLoading,
    isFetching,
  } = useGetUserInfo({});
  const { data: userSetting, isLoading: isUserSettingLoading } =
    useGetUserSetting({});
  const { data: zaloInfoData } = useGetZaloInfo({
    options: {
      enabled: !!userSetting?.authSetting?.["scope.userPhonenumber"],
    },
  });
  const { appSettings } = useAppConfig();

  // Variables
  const formattedPhoneNumber = formatVietnamesePhoneNumber(
    zaloInfoData?.data?.number || ""
  );
  const { tierList = Object.values(TIER) } =
    appSettings?.globals?.loyalty || {};

  // Queries
  const {
    data: loyaltyCustomerDetailData,
    isLoading: isLoadingGetLoyaltyCustomerDetail,
    refetch: refetchLoyaltyCustomerDetail,
    isRefetching,
  } = useGetLoyaltyCustomerDetail({
    args: {
      customerId: formattedPhoneNumber /* || APP_CONFIG.TEST_PHONE */,
    },
    options: {
      enabled: !!formattedPhoneNumber,
    },
  });

  // Memos
  const loyaltyCustomer = useMemo(() => {
    if (loyaltyCustomerDetailData?.code !== 200) {
      return null;
    }

    return loyaltyCustomerDetailData?.data;
  }, [loyaltyCustomerDetailData?.code, loyaltyCustomerDetailData?.data]);

  const isRegistered = useMemo(() => {
    if (
      (((userSetting?.authSetting?.["scope.userInfo"] &&
        userSetting?.authSetting?.["scope.userPhonenumber"]) ||
        !!zaloInfoData?.data?.number) &&
        !isEmpty(loyaltyCustomer)) ||
      process.env.NODE_ENV === "development"
    ) {
      return true;
    }

    return false;
  }, [userSetting, zaloInfoData?.data?.number, loyaltyCustomer]);

  const [isRegisteredDebounced] = useDebounceValue(isRegistered, 500);
  const isUserLoading =
    isUserInfoLoading ||
    isFetching ||
    isLoadingGetLoyaltyCustomerDetail ||
    isUserSettingLoading;
  // const [isLoadingDebounced] = useDebounceValue(
  //   isUserInfoLoading ||
  //     isFetching ||
  //     isLoadingGetLoyaltyCustomerDetail ||
  //     isUserSettingLoading,
  //   500
  // );

  const memberTier: Tier = useMemo(() => {
    const tier = tierList?.find(
      (tier) => tier.key === loyaltyCustomer?.membershipLevel
    );

    return isUserLoading
      ? TIER.MEMBER
      : tier ||
          TIER[`${loyaltyCustomer?.membershipLevel}`.toUpperCase()] ||
          TIER.MEMBER;
  }, [isUserLoading, loyaltyCustomer?.membershipLevel, tierList]);

  // Next Member Tier
  const nextMemberTier: Tier | undefined = useMemo(() => {
    const currentTierIndex =
      tierList?.findIndex((tier) => tier.key === memberTier.key) || 0;

    return tierList?.[currentTierIndex + 1] as Tier;
  }, [memberTier.key, tierList]);

  const userInfo = useMemo(() => {
    return {
      ...userInfoData?.userInfo,
      phoneNumber: formatVietnamesePhoneNumber(
        zaloInfoData?.data?.number || ""
      ) /* ||
        APP_CONFIG.TEST_PHONE */,
    };
  }, [userInfoData?.userInfo, zaloInfoData?.data?.number]);

  return {
    userInfo,
    loyaltyCustomer,
    isRegistered: isRegisteredDebounced,
    userSetting,
    memberTier,
    nextMemberTier,
    isLoading: isUserLoading,
    isLoyaltyCustomerRefetching: isRefetching,
    refetchLoyaltyCustomerDetail,
  };
};
