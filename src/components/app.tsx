import React, { Suspense } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>nackbar<PERSON><PERSON>ider } from 'zmp-ui';
import { RecoilRoot } from 'recoil';

// Components
import { Layout } from './layout';
import { ConfigProvider } from '@antscorp/ama-ui-v1';
import { ConfigProvider as MobileConfigProvider } from '@antscorp/ama-ui';

// Constants
import { THEME } from 'constant';

// Providers
import { Providers } from 'providers';

// var _portalId = "564892334"; // Portal HIGHLANDS COFFEE
// var _propId = "565018506";
// var _cdp365Analytics = {
//   default_event: 0,
// };
// (window as any)._portalId = _portalId;
// (window as any)._propId = _propId;
// (window as any)._cdp365Analytics = _cdp365Analytics;

// var _ATM_TRACKING_ASSOCIATE_UTM = 1; // https://www.highlandscoffee.com.vn/
// (function () {
//   var w = window as any;
//   if (w.web_event) return;
//   var a: any = (w.web_event = function () {
//     // eslint-disable-next-line prefer-rest-params
//     a.queue.push(arguments);
//   });
//   a.propId = _propId;
//   a.portal_id = _portalId;
//   a.portalId = _portalId;
//   a._portalId = _portalId;
//   a.track = a;
//   a.queue = [];
//   var e = document.createElement("script");
//   // eslint-disable-next-line @typescript-eslint/no-unused-expressions
//   (e.type = "text/javascript"),
//     (e.async = !0),
//     (e.src = "//st-a.cdp.asia/insight.js");
//   const t = document.getElementsByTagName("script")[0] as any;
//   t.parentNode.insertBefore(e, t);
// })();

window.React = React;

const MyApp = () => {
  return (
    <RecoilRoot>
      <App>
        <Providers>
          <MobileConfigProvider>
            <ConfigProvider theme={THEME}>
              <SnackbarProvider>
                <ZMPRouter>
                  <Suspense>
                    <Layout />
                  </Suspense>
                </ZMPRouter>
              </SnackbarProvider>
            </ConfigProvider>
          </MobileConfigProvider>
        </Providers>
      </App>
    </RecoilRoot>
  );
};
export default MyApp;
