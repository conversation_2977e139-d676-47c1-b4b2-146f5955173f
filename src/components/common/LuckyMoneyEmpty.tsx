// Libraries
import { motion } from "motion/react";
import React, { memo, useCallback } from "react";
import styled from "styled-components";

// Assets
import luckyMoneyEmptyBg from "assets/images/backgrounds/lucky-money-empty.png";
import { Button, Text, useNavigate } from "zmp-ui";

interface LuckyMoneyEmptyProps {}

const LuckyMoneyEmptyWrapper = styled(motion.div)`
  height: 100%;
  width: 100%;
  background: url(${luckyMoneyEmptyBg}) no-repeat top center / cover;

  .empty-description {
    position: relative;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    color: var(--color-primary);
    line-height: 30px;
  }

  .receive-button {
    font-family: "Red Rose", sans-serif;
    font-weight: 700;
    font-size: 17px;
    background-color: var(--color-primary);
  }
`;

export const LuckyMoneyEmpty: React.FC<LuckyMoneyEmptyProps> = memo(() => {
  const navigate = useNavigate();

  // Handlers
  const onClickReceiveLuckyMoney = useCallback(() => {
    navigate("/games/lucky-money");
  }, [navigate]);

  return (
    <LuckyMoneyEmptyWrapper
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="flex items-center"
    >
      <div className="relative flex flex-col items-center justify-center px-5 gap-10 top-10">
        <Text className="empty-description">
          Úi! Bạn chưa có Voucher. Tham gia Lì xì Highlands để nhận thêm quà
          nha!
        </Text>
        <Button className="receive-button" onClick={onClickReceiveLuckyMoney}>
          Nhận lì xì ngay
        </Button>
      </div>
    </LuckyMoneyEmptyWrapper>
  );
});

LuckyMoneyEmpty.displayName = "LuckyMoneyEmpty";
