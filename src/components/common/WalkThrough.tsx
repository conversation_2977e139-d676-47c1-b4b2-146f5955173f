// SplashScreen.js
import React, { useCallback } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";

// Images
import walkThrough1 from "assets/images/walkthrough/walkthrough_1.svg";
import walkThrough2 from "assets/images/walkthrough/walkthrough_2.svg";
import walkThrough3 from "assets/images/walkthrough/walkthrough_3.svg";
import walkThrough4 from "assets/images/walkthrough/walkthrough_4.svg";
import walkThrough5 from "assets/images/walkthrough/walkthrough_5.svg";

import { Swiper, SwiperSlide } from "components/ui";
import { Button } from "zmp-ui";
import { useImmer } from "use-immer";
import { useAppConfig, useRequestZaloPermissions } from "hooks";
import { useCheckUser } from "queries";
import { Modal } from "@antscorp/ama-ui";
import { APP_CONFIG, ZMA_ERROR } from "constant";
import { closeApp } from "zmp-sdk";
import { useRecoilState } from "recoil";
import { onboardingState } from "state";

interface SplashScreenProps {}

const WalkThroughWrapper = styled(motion.div)`
  position: absolute;
  z-index: 9998;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #ffffff;
  padding: 24px 0px 24px 0px;
  text-align: center;
  width: 100%;

  .skip-button {
    font-size: 16px;
    padding: 50px 20px 25px 20px;
    font-weight: 500;
    color: var(--color-text-secondary);
    justify-self: right;
    cursor: pointer;
  }

  .next-button {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    animation: none !important;
    height: 50px;
    border-radius: 6.24px;
    margin-bottom: 24px;
    flex-shrink: 0;
  }

  .swiper-pagination {
    position: relative;
    margin-top: 45px;
    .swiper-pagination-bullet {
      background-color: var(--color-disabled);
      opacity: 1;
    }

    .swiper-pagination-bullet-active {
      background-color: var(--color-main-primary);
    }
  }
  .swiper-wrapper {
    .swiper-slide {
      width: 100%;
      padding: 0 25px;

      img {
        justify-self: center;
        width: 100%;
        height: auto;
        margin-bottom: 70px;
      }

      .title {
        width: 100%;
        font-size: 21px;
        font-weight: 600;
        white-space: pre-line;
        padding-bottom: 11px;
      }

      .subtitle {
        width: 100%;
        color: var(--color-text-secondary);
      }

      &.swiper-slide-active {
        z-index: 20;
      }
    }
  }
`;

const walkThroughBanners = [
  {
    image: walkThrough1,
    title: "Tích lũy tới 15%",
    subtitle:
      "Đặt hàng từ các thương hiệu, nhà hàng nổi tiếng trên khắp cả nước.",
  },
  {
    image: walkThrough2,
    title: "Ưu đãi độc quyền",
    subtitle: "Golden SpoonS dành riêng rất nhiều ưu đãi dành cho khách hàng.",
  },
  {
    image: walkThrough3,
    title: "Đặt bàn dễ dàng",
    subtitle:
      "Đến từ Golden Gate Group, Là tập đoàn với hơn 15+ năm kinh nghiệm trong F&B.",
  },
  {
    image: walkThrough4,
    title: "Món ngon tại gia \n Chuẩn vị nhà hàng",
    subtitle:
      "Tận hưởng dịch vụ toàn diện, sẽ làm bạn và người thân hài lòng nhất.",
  },
  {
    image: walkThrough5,
    title: "Chào mừng bạn",
    subtitle:
      "Bạn cần trợ giúp, chúng tôi luôn ở đây để sẵn sàng giúp đỡ bạn mọi lúc mọi nơi.",
  },
];

type TState = {
  swiperInstance: any;
  activeIndex: number;
  isShowRequestPermissionMessage?: boolean;
  isShowLimitRequestMessage?: boolean;
  isModalOpen?: boolean;
};
const initialState: TState = {
  swiperInstance: null,
  activeIndex: 0,
  isShowRequestPermissionMessage: false,
  isShowLimitRequestMessage: false,
  isModalOpen: false,
};

export const WalkThrough: React.FC<SplashScreenProps> = () => {
  // States
  const [state, setState] = useImmer<TState>(initialState);
  const {
    // allocatedVoucher,
    swiperInstance,
    activeIndex,
    isShowLimitRequestMessage,
    isModalOpen,
    isShowRequestPermissionMessage,
  } = state;

  // Queries
  const { mutateAsync: checkUser, isPending: isCheckUserPending } =
    useCheckUser({});

  // Hooks
  const [_, setOnboarding] = useRecoilState(onboardingState);
  const {
    appSettings,
    gameValidation,
    isLoading: isLoadingAppConfig,
  } = useAppConfig();
  const { systemErrorMessages } = appSettings?.globals || {};
  const { requestPermissionMessage } = appSettings?.games?.affiliate || {};
  const { requestZaloPermissions } = useRequestZaloPermissions({
    onFail({ step, message, error }) {
      if (["flowOA", "allowPhone"].includes(step)) {
        setState((draft) => {
          draft.isShowRequestPermissionMessage = true;
        });
      }

      // If state code equal -203 it means user request so many times, then show limit Request message
      if (error.code === -203) {
        setState((draft) => {
          draft.isShowLimitRequestMessage = true;
          draft.isShowRequestPermissionMessage = false;
        });
      }

      // setState((draft) => {
      //   draft.isGameLoading = false;
      // });
    },
    async onFinish() {
      checkUser().then(({ data }) => {
        setState((draft) => {
          if (!data?.status) {
            // setAffiliate((prev) => ({
            //   ...prev,
            //   currentScreen: "coupon-selection",
            // }));
            setOnboarding((prev) => ({
              ...prev,
              isShowWalkThrough: false,
            }));
          } else {
            draft.isModalOpen = true;
          }
          // draft.isGameLoading = false;
          draft.isShowRequestPermissionMessage = false;
        });
      });
    },
  });

  // Handlers
  const onClickComplete = useCallback(
    (isSkipped?: boolean) => {
      setState((draft) => {
        // draft.isGameLoading = true;
      });

      if (activeIndex === walkThroughBanners.length - 1 || isSkipped) {
        requestZaloPermissions();
        setOnboarding((prev) => ({
          ...prev,
          isShowWalkThrough: false,
        }));
      } else {
        swiperInstance.slideNext();
      }
    },
    [
      requestZaloPermissions,
      setState,
      setOnboarding,
      activeIndex,
      swiperInstance,
    ]
  );

  return (
    <WalkThroughWrapper
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ delay: 0, duration: 0.5, ease: "easeInOut" }}
    >
      <motion.div
        className="skip-button"
        style={{
          visibility:
            activeIndex === walkThroughBanners.length - 1
              ? "hidden"
              : "visible",
        }}
        onClick={() => onClickComplete(true)}
      >
        Bỏ qua
      </motion.div>
      <Swiper
        onSwiper={(swiper) => {
          setState((draft) => {
            draft.swiperInstance = swiper;
          });
        }}
        onSlideChange={(swiper) => {
          setState((draft) => {
            draft.activeIndex = swiper.activeIndex;
          });
        }}
        className="w-full rounded-[18px]"
        pagination={{ clickable: true }}
        // autoplay={{
        //   delay: 5000,
        //   disableOnInteraction: false,
        //   pauseOnMouseEnter: true,
        // }}
        speed={1000}
      >
        {walkThroughBanners?.map((banner, index) => {
          return (
            <SwiperSlide key={index}>
              <motion.img
                src={banner?.image || walkThrough1}
                alt="banner"
                // className="w-full h-auto aspect-[3.1] object-cover object-center"
              />
              <motion.div className="title">{banner.title}</motion.div>
              <motion.div className="subtitle">{banner.subtitle}</motion.div>
            </SwiperSlide>
          );
        })}
      </Swiper>
      <Button className="next-button" onClick={() => onClickComplete()}>
        {activeIndex === walkThroughBanners.length - 1
          ? "Hoàn tất"
          : "Tiếp tục"}
      </Button>

      <Modal
        title="Thông báo"
        getContainer={document.body}
        content={
          <div className="text-center">
            {systemErrorMessages?.limitRequest || ZMA_ERROR["-203"]?.message}
          </div>
        }
        closeOnMaskClick={false}
        onClose={() => closeApp()}
        visible={isShowLimitRequestMessage}
        actions={[
          {
            key: "close",
            text: "Đóng",
            primary: true,
            onClick: () => closeApp(),
          },
        ]}
      />

      <Modal
        title="Điều khoản"
        getContainer={document.body}
        content={
          <div
            className="text-center"
            dangerouslySetInnerHTML={{
              __html:
                requestPermissionMessage ||
                APP_CONFIG.GAMES.AFFILIATE.REQUEST_PERMISSION_MSG,
            }}
          />
        }
        closeOnMaskClick={false}
        visible={isShowRequestPermissionMessage}
        actions={[
          {
            key: "start",
            text: "Bắt đầu",
            primary: true,
            onClick: () => {
              requestZaloPermissions();
            },
          },
        ]}
      />

      <Modal
        title="Thông báo"
        getContainer={document.body}
        content={
          <div
            className="text-center"
            dangerouslySetInnerHTML={{
              __html: gameValidation.affiliate.notAvailableMessage,
            }}
          />
        }
        onClose={() => closeApp()}
        visible={!gameValidation.affiliate.isValid && !isLoadingAppConfig}
        actions={[
          {
            key: "start",
            text: "Quà tặng của tôi",
            primary: true,
            // onClick: () => onClickMyVoucher(),
          },
        ]}
      />
    </WalkThroughWrapper>
  );
};
