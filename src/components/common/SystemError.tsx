// Libraries
import React, { useCallback, useEffect } from "react";
import { useImmer } from "use-immer";
import {
  closeApp,
  getAppInfo,
  getSystemInfo,
  getUserInfo,
  onNetworkStatusChange,
} from "zmp-sdk/apis";

// Components
import { SystemNotificationModal } from "components/ui";

// Utils
import { getOSInfo } from "utils";

// Hooks
import { useAppConfig, useUserInfo } from "hooks";

// Constants
import { APP_CONFIG } from "constant";

interface SystemErrorProps {}

const MESSAGES_TYPE = {
  NETWORK_ERROR: "networkError",
  OS_NOT_SUPPORTED: "osNotSupported",
  UNDER_CONSTRUCTION: "underConstruction",
  INVALID_REGION: "invalidRegion",
  BANNED_USER: "bannedUser",
};

const initialState = {
  type: null,
  errorMessage: null,
  showError: false,
};

export const SystemError: React.FC<SystemErrorProps> = (props) => {
  const { ...restProps } = props;

  const [state, setState] = useImmer<{
    type: string | null;
    errorMessage: string | null;
    showError: boolean;
  }>(initialState);

  const { systemErrorMessages, osSupported, isUnderConstruction, bannedUsers } =
    useAppConfig()?.appSettings?.globals || {};
  const { userInfo } = useUserInfo();
  const { os, version } = getOSInfo();
  const { showError, errorMessage } = state;

  // Handlers
  const checkOS = useCallback(() => {
    if (
      os === "iOS" &&
      version &&
      version < (osSupported?.ios?.minVersion || 15)
    ) {
      setState((draft) => {
        // draft.showError = true;
        draft.type = MESSAGES_TYPE.OS_NOT_SUPPORTED;
        draft.errorMessage =
          systemErrorMessages?.osNotSupported ||
          APP_CONFIG.SYSTEM_ERROR_MESSAGES.osNotSupported;
      });
    }

    if (
      os === "Android" &&
      version &&
      version < (osSupported?.android?.minVersion || 10)
    ) {
      setState((draft) => {
        draft.showError = true;
        draft.type = MESSAGES_TYPE.OS_NOT_SUPPORTED;
        draft.errorMessage =
          systemErrorMessages?.osNotSupported ||
          APP_CONFIG.SYSTEM_ERROR_MESSAGES.osNotSupported;
      });
    }
  }, [
    os,
    version,
    osSupported?.ios?.minVersion,
    osSupported?.android?.minVersion,
    systemErrorMessages?.osNotSupported,
    setState,
  ]);

  const checkIsUnderConstruction = useCallback(() => {
    if (isUnderConstruction) {
      setState((draft) => {
        draft.showError = true;
        draft.errorMessage =
          systemErrorMessages?.underConstruction ||
          APP_CONFIG.SYSTEM_ERROR_MESSAGES.underConstruction;
      });
    }
  }, [isUnderConstruction, systemErrorMessages?.underConstruction, setState]);

  const checkLocation = useCallback(() => {
    getUserInfo({
      success({ userInfo }) {
        if (!userInfo.id) {
          setState((draft) => {
            draft.showError = true;
            draft.type = MESSAGES_TYPE.INVALID_REGION;
            draft.errorMessage =
              systemErrorMessages?.regionNotSupported ||
              APP_CONFIG.SYSTEM_ERROR_MESSAGES.regionNotSupported;
          });
        }
      },
    });
  }, [systemErrorMessages?.regionNotSupported, setState]);

  useEffect(() => {
    checkOS();

    checkIsUnderConstruction();

    checkLocation();
  }, [checkIsUnderConstruction, checkLocation, checkOS]);

  useEffect(() => {
    onNetworkStatusChange(function (data) {
      const { isConnected, networkType } = data;
      const isDisconnected =
        [/* "unknown", */ "none"].includes(networkType) || !isConnected;

      if (!state.type || state.type === MESSAGES_TYPE.NETWORK_ERROR) {
        setState((draft) => {
          draft.showError = isDisconnected;
          draft.type = MESSAGES_TYPE.NETWORK_ERROR;
          draft.errorMessage = isDisconnected
            ? systemErrorMessages?.networkError ||
              APP_CONFIG.SYSTEM_ERROR_MESSAGES.networkError
            : "";
        });
      }
    });
  }, [setState, state.type, systemErrorMessages?.networkError]);

  useEffect(() => {
    if (bannedUsers?.some((userId) => userId === userInfo?.id)) {
      setState((draft) => {
        draft.showError = true;
        draft.type = MESSAGES_TYPE.BANNED_USER;
        draft.errorMessage =
          systemErrorMessages?.bannedUser ||
          APP_CONFIG.SYSTEM_ERROR_MESSAGES.bannedUser;
      });
    }
  }, [bannedUsers, setState, systemErrorMessages?.bannedUser, userInfo]);

  return (
    <SystemNotificationModal
      showCloseButton={false}
      visible={showError}
      title="Thông báo"
      maskClosable={false}
      onClose={() => {
        closeApp();
      }}
      content={
        <div
          dangerouslySetInnerHTML={{
            __html: errorMessage || "",
          }}
        ></div>
      }
    />
  );
};
