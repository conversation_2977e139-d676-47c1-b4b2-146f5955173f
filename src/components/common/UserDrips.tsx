// Libraries
import React, { memo } from "react";
import clsx from "clsx";

// Components
import { SpaceProps } from "@antscorp/ama-ui";

// Icons
import { DripsTag } from "./Drips";

// Hooks
import { useUserInfo } from "hooks";

interface UserDripsProps extends SpaceProps {
  ghost?: boolean;
}

export const UserDrips: React.FC<UserDripsProps> = memo(
  ({ className, ghost = false, ...props }) => {
    const { isRegistered, loyaltyCustomer } = useUserInfo();
    const { availablePoints } = loyaltyCustomer || {};

    return (
      isRegistered && (
        <DripsTag
          drips={availablePoints || 0}
          className={clsx(className, {
            "bg-white rounded-[500px] px-[9.6px] py-1 text-text-primary":
              !ghost,
          })}
          {...props}
        />
      )
    );
  }
);

UserDrips.displayName = "UserDrips";
