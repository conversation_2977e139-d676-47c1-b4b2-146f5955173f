import React, { FC, useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router';
import styled from 'styled-components';
import { BottomNavigation } from 'zmp-ui';
import { useRecoilValue } from 'recoil';
import { openChat } from 'zmp-sdk/apis';

// Hooks
import { useUserInfo, useVirtualKeyboardVisible } from 'hooks';

// Types
import { MenuItem } from 'types';

// Icons
import {
  AccountIcon,
  AccumulatePointsIcon,
  ChatIcon,
  GiftIcon,
  HistoryIcon,
  HomeIcon,
  ListIcon,
} from 'components/icons';
import { CircleUserRoundIcon } from 'lucide-react';

// State
import { userState } from 'state';

// Constants
import { APP_CONFIG } from 'constant';
import { respond } from 'utils';

/**
 * Styled components
 */
const StyledBottomNavigation = styled(BottomNavigation)`
  /* background-color: var(--color-background); */
  --zaui-safe-area-inset-bottom: 10px;
  border-top: 1px solid var(--color-disabled);

  .zaui-bottom-navigation-content {
    height: calc(var(--zaui-safe-area-inset-bottom) + 80px);
    /* gap: 30px; */
    padding: 0 20px;
    padding-bottom: calc(var(--zaui-safe-area-inset-bottom));

    /* background-color: var(--color-main-primary); */
    justify-content: space-between;

    .zaui-bottom-navigation-item {
      min-width: 70px;
      padding: 10px 0px;
      flex: unset !important;
      gap: 4px;
      color: var(--color-text-secondary);

      .zaui-bottom-navigation-item-icon {
        border-radius: var(--adm-radius-s);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;

        svg {
          transition: color 0.2s ease-in-out;
        }
      }

      .zaui-bottom-navigation-item-label {
        font-size: 12px;
        font-weight: 500;
        text-transform: capitalize;
        transition: color 0.2s ease-in-out;
      }

      &.zaui-bottom-navigation-item-active {
        color: var(--color-primary);
        /* .zaui-bottom-navigation-item-icon {
          background-color: var(--color-background-menu-item);
        } */

        .zaui-bottom-navigation-item-label {
          /* font-weight: 700; */
        }
      }
    }

    &::after {
      display: none;
    }
  }

  ${respond.xs`
    .zaui-bottom-navigation-item-label {
      font-size: 13px !important;
    }
  `}
`;

/**
 * Navigation configuration
 */
const NAVIGATION_TABS: Record<string, MenuItem & { key?: string }> = {
  '/': {
    label: 'Trang chủ',
    icon: <HomeIcon />,
  },
  '/accumulate-points': {
    label: 'Tích điểm',
    icon: <AccumulatePointsIcon />,
  },
  '/gift': {
    label: 'Lịch sử',
    icon: <HistoryIcon />,
  },
  '/account': {
    key: 'account',
    label: 'Tài khoản',
    icon: <AccountIcon />,
  },
};

/**
 * Routes that should not display bottom navigation
 */
export const NO_BOTTOM_NAVIGATION_PAGES = ['/search', '/category', '/result'];

/**
 * Export type for tab keys
 */
export type TabKeys = keyof typeof NAVIGATION_TABS;

/**
 * Bottom Navigation Component for the app
 */
export const Navigation: FC = () => {
  const keyboardVisible = useVirtualKeyboardVisible();
  const navigate = useNavigate();
  const location = useLocation();
  const { userInfo } = useUserInfo();

  /**
   * Determine if navigation should be hidden based on current route
   */
  const shouldHideNavigation = useMemo(() => {
    return NO_BOTTOM_NAVIGATION_PAGES.includes(location.pathname);
  }, [location]);

  /**
   * Handle navigation item click
   * Opens chat for chat tab, otherwise navigates to the tab's path
   */
  const handleNavigationItemClick = (tab?: MenuItem & { key?: string; path: string }) => {
    if (userInfo && tab?.key === 'chat') {
      openChat({
        type: 'oa',
        id: APP_CONFIG.OA_ID,
        message: 'Xin chào Highlands',
        success: () => console.log('open chat success'),
        fail: () => console.log('open chat fail'),
      });
    } else {
      navigate(tab?.path || '/');
    }
  };

  // Hide navigation when on specified pages or when keyboard is visible
  if (shouldHideNavigation || keyboardVisible) {
    return null;
  }

  return (
    <StyledBottomNavigation
      id="footer"
      activeKey={location.pathname}
      className="z-50 shrink-0 shadow-2xl"
    >
      {Object.keys(NAVIGATION_TABS).map((path: TabKeys) => {
        const { key, label, icon, activeIcon } = NAVIGATION_TABS[path];
        // Determine if the current tab is active based on the current path
        const isActive = path !== '/' && location.pathname.startsWith(path);

        return (
          <BottomNavigation.Item
            key={path}
            className={isActive ? 'zaui-bottom-navigation-item-active' : ''}
            label={label}
            icon={icon}
            activeIcon={activeIcon}
            onClick={() => {
              handleNavigationItemClick({
                ...NAVIGATION_TABS[path],
                key,
                path,
              });
            }}
          />
        );
      })}
    </StyledBottomNavigation>
  );
};
