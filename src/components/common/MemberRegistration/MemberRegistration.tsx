// Libraries
import { SHA256 } from "crypto-js";
import { AnimatePresence, motion } from "motion/react";
import React, { memo, useCallback, useEffect, useMemo } from "react";
import styled from "styled-components";
import { useImmer } from "use-immer";
import { authorize, closeApp, followOA, getUserInfo } from "zmp-sdk/apis";
import { useDebounceValue } from "usehooks-ts";
import { matchPath, useLocation } from "react-router-dom";

// Components
import {
  Button,
  Card,
  CardProps,
  Modal,
  SpinLoading,
  SpinLoadingProps,
} from "@antscorp/ama-ui";

// Constants
import {
  APP_CONFIG,
  EVENT_CONFIG,
  QUERY_KEY,
  ROUTES,
  ZMA_ERROR,
} from "constant";

// Hooks
import { useAppConfig, useDeepCompareEffect, useUserInfo } from "hooks";

// Services
import { zaloServices } from "services";

// Utils
import { useQueryClient } from "@tanstack/react-query";
import {
  callCdpEvent,
  formatEventDateTime,
  formatPhoneNumberTo84,
  formatVietnamesePhoneNumber,
  trackFacebookPixel,
} from "utils";

// Icons
import { SuccessIcon } from "components/icons";
import dayjs from "dayjs";
import { useCheckNewUser, useCreateLoyaltyCustomer } from "queries";
import { ConditionalRenderer } from "../ConditionalRender";

interface MemberRegistrationFormProps extends CardProps {
  /** Flag to indicate if the user is a really new user */
  isReallyNewUser?: boolean;
  /** Text for register button */
  registerText?: string;
  /** Callback function when register /*/
  onRegister?: () => void;
  /** Callback function when register success */
  onRegisterSuccess?: (userInfo: any) => void;
  /** Callback function when register fail */
  onRegisterFail?: (error: any) => void;
}

interface MemberRegistrationProps extends MemberRegistrationFormProps {
  children?: React.ReactNode;
  loadingProps?: SpinLoadingProps;
  onRegisterSuccess?: (userInfo: any) => void;
}

const MemberRegistrationWrapper = styled(Card)`
  --adm-card-body-padding-block: 15px;
  --adm-card-padding-inline: 20px;
  box-shadow: 0px 3px 9px 0px #002e5926;

  .member-registration {
    &__title {
      color: var(--color-main-primary);
      font-family: "Red Rose";
      font-weight: 500;
      font-size: 16px;
      text-align: center;
    }

    &__description {
    }
  }
`;

export const MemberRegistrationForm: React.FC<MemberRegistrationFormProps> =
  memo((props) => {
    const {
      registerText = "Đăng ký",
      isReallyNewUser,
      onRegister,
      onRegisterSuccess,
      onRegisterFail,
      ...restProps
    } = props;

    // Hooks
    const queryClient = useQueryClient();
    const { userInfo } = useUserInfo();
    const location = useLocation();

    // Memo
    const textContent = useMemo(() => {
      return {
        title: isReallyNewUser
          ? "Bạn chưa là thành viên"
          : "Chương trình thành viên",
        message: isReallyNewUser
          ? "Đăng ký thành viên để tận hưởng ngàn ưu đãi đặc quyền chỉ dành riêng cho bạn <3"
          : "Tận hưởng ngàn ưu đãi đặc quyền chỉ dành riêng cho bạn <3",
        buttonText: isReallyNewUser ? registerText : "Tham gia ngay",
      };
    }, [isReallyNewUser, registerText]);

    // Handlers
    const handleRequestPermissions = useCallback(async () => {
      const handleRefreshUserInfo = async () => {
        const { userInfo } = await getUserInfo({
          autoRequestPermission: false,
        });

        setTimeout(() => {
          queryClient.setQueryData([QUERY_KEY.GET_USER_INFO], {
            userInfo,
          });
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.GET_USER_SETTING],
            exact: false,
          });
        }, 500);

        // Call Cdp allow info
        callCdpEvent({
          uId: userInfo.id,
          data: {
            page_type: "allow_info",
            page_cate: EVENT_CONFIG.APP_LOYALTY,
          },
        });

        // Track facebook pixel event for AllowInfoSharing
        trackFacebookPixel({
          eventName: "AllowInfoSharing",
          params: {
            uid: userInfo.id,
          },
        });
      };

      if (!userInfo?.phoneNumber) {
        await authorize({
          scopes: ["scope.userInfo", "scope.userPhonenumber"],
          success: async () => {
            await handleRefreshUserInfo();
          },
          fail: (error) => {
            onRegisterFail?.(error);
          },
        });
      } else {
        await handleRefreshUserInfo();
      }
    }, [userInfo?.phoneNumber, queryClient, onRegisterFail]);

    const onClickRegister = useCallback(async () => {
      onRegister?.();

      const matchRoute = Object.values(ROUTES).find((route) =>
        matchPath({ path: route.path, end: true }, location.pathname)
      );

      // Track Facebook Pixel Register Start event
      trackFacebookPixel({
        eventName: "RegisterStart",
        params: {
          uid: userInfo?.id,
        },
      });

      // Track click register event
      callCdpEvent({
        uId: userInfo?.id,
        ea: "click",
        ec: "banner",
        data: {
          position: "register",
          page_type: matchRoute?.pageCate || "home",
          page_cate: EVENT_CONFIG.APP_LOYALTY,
          index: 0,
          destination_url: "",
          image_url: "",
        },
      });

      if (!userInfo?.followedOA) {
        await followOA({
          id: APP_CONFIG.OA_ID,
          success() {
            // Call Cdp flow OA
            callCdpEvent({
              uId: userInfo?.id,
              data: {
                page_type: "follow_oa",
                page_cate: EVENT_CONFIG.APP_LOYALTY,
              },
            });

            // Track facebook pixel event for following OA
            trackFacebookPixel({
              eventName: "FollowOA",
              params: {
                uid: userInfo?.id,
              },
            });

            handleRequestPermissions();
          },
          fail(error) {
            onRegisterFail?.(error);
          },
        });
      } else {
        handleRequestPermissions();
      }
    }, [
      userInfo?.id,
      userInfo?.followedOA,
      location.pathname,
      onRegister,
      handleRequestPermissions,
      onRegisterFail,
    ]);

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <MemberRegistrationWrapper
          bodyClassName="flex flex-col items-center"
          {...restProps}
        >
          <div className="member-registration__title">{textContent.title}</div>
          <p className="member-registration__description text-center mt-1 mb-5">
            {textContent.message}
          </p>

          <Button
            color="primary"
            shape="rounded"
            className="w-[230px]"
            onClick={onClickRegister}
          >
            {textContent.buttonText}
          </Button>
        </MemberRegistrationWrapper>
      </motion.div>
    );
  });
MemberRegistrationForm.displayName = "MemberRegistrationForm";

export const MemberRegistration: React.FC<MemberRegistrationProps> = ({
  children,
  loadingProps,
  onRegisterSuccess,
  ...restProps
}) => {
  const { appSettings } = useAppConfig();
  const {
    isRegistered,
    isLoading: isUserLoading,
    userInfo,
    loyaltyCustomer,
    isLoyaltyCustomerRefetching,
  } = useUserInfo();

  // Queries
  const { mutateAsync: createLoyaltyCustomer } = useCreateLoyaltyCustomer({});
  const { data: checkNewUserData } = useCheckNewUser();

  // Variables
  const { systemErrorMessages } = appSettings?.globals || {};

  // Memo
  const isReallyNewUser = useMemo(
    () => checkNewUserData?.data?.status && !loyaltyCustomer,
    [checkNewUserData?.data?.status, loyaltyCustomer]
  );
  const [state, setState] = useImmer({
    isLoading: false,
    visibleSuccessModal: false,
    limitRequestVisible: false,
    isClickRegister: false,
  });
  const { isLoading, limitRequestVisible, isClickRegister } = state;

  const [debounceLoading] = useDebounceValue(
    isLoading || isUserLoading || isLoyaltyCustomerRefetching,
    1000
  );

  // Effects
  useDeepCompareEffect(() => {
    (async () => {
      if (isClickRegister && userInfo.phoneNumber) {
        const customerId = SHA256(userInfo.phoneNumber).toString();
        const externalId = SHA256(
          formatPhoneNumberTo84(userInfo.phoneNumber)
        ).toString();

        // Call Cdp event identify
        callCdpEvent({
          ec: "user",
          ea: "identify",
          uId: userInfo.id,
          dims: {
            users: {
              user_id: userInfo.id,
              identify_event: "allow_miniapp",
              identify_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
              id_by_oa: userInfo.idByOA,
              name: userInfo.name,
              phone: userInfo.phoneNumber,
            },
            customers: {
              name: userInfo.name,
              zalo_name: userInfo.name,
              phone: userInfo.phoneNumber,
              customer_id: customerId,
              zalo_uid: userInfo.id,
              id_by_oa: userInfo.idByOA,
            },
          },
          data: {
            identify_id: customerId,
            identify_event: "allow_miniapp",
          },
        });

        const { data: loyaltyCustomer } = await createLoyaltyCustomer({
          data: {
            customerName: `${userInfo.name}`,
            phoneNumber: userInfo.phoneNumber,
          },
        });

        // Call Cdp event sign up
        callCdpEvent({
          uId: userInfo.id,
          ea: "sign_up",
          ec: "user",
          dims: {
            customers: {
              customer_id: customerId,
              name: userInfo?.name,
              phone: userInfo.phoneNumber,
              ...(loyaltyCustomer?.createdAt && {
                loyalty_create_date: formatEventDateTime(
                  loyaltyCustomer?.createdAt
                ),
              }),
            },
          },
          data: {
            customer_phone: userInfo.phoneNumber,
            customer_name: userInfo?.name,
          },
        });

        if (loyaltyCustomer?.customerId) {
          // Track Facebook Pixel Register Success event
          trackFacebookPixel({
            eventName: "RegisterSuccess",
            params: {
              uid: userInfo.id,
              external_id: externalId,
            },
          });
        }

        // Show success modal
        if (isReallyNewUser) {
          toggleSuccessModal();
        }

        onRegisterSuccess?.({
          ...userInfo,
          phoneNumber: userInfo.phoneNumber,
          customerId,
        });

        setState((draft) => {
          draft.isClickRegister = false;
          draft.isLoading = false;
        });
      }
    })();
  }, [isClickRegister, isReallyNewUser, userInfo]);

  // Handlers
  const toggleSuccessModal = useCallback(() => {
    setState((draft) => {
      draft.visibleSuccessModal = !draft.visibleSuccessModal;
    });
  }, [setState]);

  const onRegister = useCallback(() => {
    setState((draft) => {
      draft.isClickRegister = true;
      draft.isLoading = true;
    });
  }, [setState]);

  const onRegisterFail = useCallback(
    (error) => {
      // Handle limit request
      if (error.code === -203) {
        setState((draft) => {
          draft.limitRequestVisible = true;
        });
      }

      setState((draft) => {
        draft.isLoading = false;
      });
    },
    [setState]
  );

  // Render loading state
  if (debounceLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="flex flex-col h-full w-full items-center justify-center"
      >
        <SpinLoading
          color="primary"
          style={{ "--size": "48px" }}
          {...loadingProps}
        />
      </motion.div>
    );
  }

  // If user is already a member, return children
  if (isRegistered) {
    return (
      <>
        {children}

        <Modal
          visible={state.visibleSuccessModal}
          header={<SuccessIcon size={46} className="text-success" />}
          bodyClassName="!pt-[30px]"
          closeOnMaskClick
          getContainer={document.body}
          actions={[
            {
              key: "close",
              text: "Đóng",
              primary: true,
              onClick: toggleSuccessModal,
            },
          ]}
          content={
            <div className="text-center mt-5">
              Bạn đã đăng ký thành viên thành công
            </div>
          }
          onClose={toggleSuccessModal}
        />
      </>
    );
  }

  // Render MemberRegistrationForm
  return (
    <AnimatePresence>
      <MemberRegistrationForm
        onRegister={onRegister}
        onRegisterFail={onRegisterFail}
        isReallyNewUser={isReallyNewUser}
        {...restProps}
      />

      <Modal
        title="Thông báo"
        getContainer={document.body}
        content={
          <div className="text-center">
            {systemErrorMessages?.limitRequest || ZMA_ERROR["-203"]?.message}
          </div>
        }
        closeOnMaskClick={false}
        onClose={() => closeApp()}
        visible={limitRequestVisible}
        actions={[
          {
            key: "close",
            text: "Đóng",
            primary: true,
            onClick: () => closeApp(),
          },
        ]}
      />
    </AnimatePresence>
  );
};
