import {
  <PERSON><PERSON>r<PERSON>lock,
  <PERSON>rrorBlockProps,
  PullToRefresh,
  SpinLoading,
} from "@antscorp/ama-ui";
import clsx from "clsx";
import React, { ReactNode } from "react";

// Define props interface for type safety
interface ConditionalRendererProps {
  isLoading?: boolean; // Indicates if the content is currently loading
  isEmpty?: boolean; // Indicates if there is no data to display
  children?: ReactNode; // The content to render when not loading or empty
  emptyProps?: ErrorBlockProps; // Props to pass to the empty state component
  loadingProps?: React.HTMLAttributes<HTMLDivElement>; // Props to pass to the loading state component
  loadingRender?: ReactNode; // The content to render while loading
  onRefresh?: () => Promise<void>; // Callback to refresh data
}

/**
 * A component that conditionally renders loading state, empty state, or children based on props.
 * @param {ConditionalRendererProps} props - The props to control rendering behavior
 * @returns {JSX.Element} - The rendered content
 */
export const ConditionalRenderer: React.FC<ConditionalRendererProps> = ({
  isLoading,
  isEmpty,
  children,
  emptyProps,
  loadingProps,
  loadingRender,
  onRefresh,
}) => {
  // Render loading state
  if (isLoading) {
    return loadingRender ? (
      loadingRender
    ) : (
      <div
        className={clsx(
          loadingProps?.className,
          "loading flex h-full w-full justify-center items-center py-4"
        )}
        {...loadingProps}
        aria-label="Loading"
      >
        <SpinLoading color="primary" style={{ "--size": "48px" }} />
      </div>
    );
  }

  // Render empty state
  if (isEmpty) {
    return (
      <div
        className="empty flex h-full w-full justify-center items-center"
        aria-label="Empty content"
      >
        <PullToRefresh threshold={40} onRefresh={onRefresh}>
          <ErrorBlock status="empty" {...emptyProps} />
        </PullToRefresh>
      </div>
    );
  }

  // Render children when no conditions are met
  return <>{children}</>;
};
