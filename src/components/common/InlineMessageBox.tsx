// Libraries
import React from "react";
import { HTMLMotionProps, motion } from "motion/react";
import { Button } from "@antscorp/ama-ui";
import clsx from "clsx";

interface InlineMessageBoxProps extends HTMLMotionProps<"div"> {
  message?: React.ReactNode;
  children?: React.ReactNode;
}

export const InlineMessageBox: React.FC<InlineMessageBoxProps> = (props) => {
  const { message, children, className, ...restProps } = props;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={clsx(
        className,
        "flex flex-col items-center justify-center mt-24"
      )}
      {...restProps}
    >
      <div className="flex flex-col items-center justify-center gap-4">
        <span
          className="text-main-primary text-center text-sm"
          dangerouslySetInnerHTML={{ __html: `${message || ""}` }}
        />
        {children}
      </div>
    </motion.div>
  );
};
