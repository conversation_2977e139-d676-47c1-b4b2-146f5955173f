// Libraries
import React from "react";

// Components
import { SelectPicker, SelectPickerProps } from "components/ui";

// Constants
import { TIME_RANGE_KEYS, TIME_RANGE_OPTIONS } from "./constants";

interface DateFilterProps extends SelectPickerProps {}

export const DateFilter: React.FC<DateFilterProps> = (props) => {
  const { options = TIME_RANGE_OPTIONS, ...restProps } = props;

  return (
    <SelectPicker
      defaultValue={TIME_RANGE_KEYS.LAST_7_DAYS}
      options={TIME_RANGE_OPTIONS}
      {...restProps}
    />
  );
};
