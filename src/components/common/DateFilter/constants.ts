import dayjs from "dayjs";

export const TIME_RANGE_KEYS = {
  TODAY: "today",
  YESTERDAY: "yesterday",
  LAST_7_DAYS: "last_7_days",
  LAST_30_DAYS: "last_30_days",
  THIS_WEEK: "this_week",
  THIS_MONTH: "this_month",
  LAST_3_MONTHS: "last_3_months",
  LAST_6_MONTHS: "last_6_months",
  THIS_YEAR: "this_year",
  CUSTOM: "custom",
  ALL: "all",
};

export const TIME_RANGE_OPTIONS = [
  {
    key: TIME_RANGE_KEYS.LAST_7_DAYS,
    value: TIME_RANGE_KEYS.LAST_7_DAYS,
    label: "7 ngày gần nhất",
    timeRange: [
      dayjs().subtract(6, "day").startOf("day").valueOf(),
      dayjs().endOf("day").valueOf(),
    ],
  },
  {
    key: TIME_RANGE_KEYS.LAST_30_DAYS,
    value: TIME_RANGE_KEYS.LAST_30_DAYS,
    label: "30 ngày gần nhất",
    timeRange: [
      dayjs().subtract(29, "day").startOf("day").valueOf(),
      dayjs().endOf("day").valueOf(),
    ],
  },
  {
    key: TIME_RANGE_KEYS.THIS_WEEK,
    value: TIME_RANGE_KEYS.THIS_WEEK,
    label: "Tuần này",
    timeRange: [
      dayjs().startOf("week").valueOf(),
      dayjs().endOf("week").valueOf(),
    ],
  },
  {
    key: TIME_RANGE_KEYS.THIS_MONTH,
    value: TIME_RANGE_KEYS.THIS_MONTH,
    label: "Tháng này",
    timeRange: [
      dayjs().startOf("month").valueOf(),
      dayjs().endOf("month").valueOf(),
    ],
  },
  {
    key: TIME_RANGE_KEYS.LAST_3_MONTHS,
    value: TIME_RANGE_KEYS.LAST_3_MONTHS,
    label: "3 tháng gần nhất",
    timeRange: [
      dayjs().subtract(2, "month").startOf("month").valueOf(),
      dayjs().endOf("month").valueOf(),
    ],
  },
];
