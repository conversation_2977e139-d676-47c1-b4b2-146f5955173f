// Libraries
import clsx from "clsx";
import { VolumeUpIcon } from "components/icons";
import { VolumeOffIcon } from "lucide-react";
import React, { useCallback } from "react";
import styled from "styled-components";
import { useLocalStorage } from "usehooks-ts";
import { Howler } from "howler";

interface VolumeControlProps extends React.HTMLAttributes<HTMLButtonElement> {}

const VolumeButton = styled.button`
  position: absolute;
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #00000051;
  top: var(--header-padding-top);
  left: 24px;
  color: #ffffff;
  transition: all 0.3s ease-in-out;
  z-index: 10000;
`;

export const VolumeControl: React.FC<VolumeControlProps> = (props) => {
  const { className, ...restProps } = props;
  const [isVolumeOn, setIsVolumeOn] = useLocalStorage("isVolumeOn", true);

  const onClickVolume = useCallback(() => {
    Howler.ctx.resume();

    if (isVolumeOn) {
      Howler.mute(true);
    } else {
      Howler.mute(false);
    }

    setIsVolumeOn(!isVolumeOn);
  }, [isVolumeOn, setIsVolumeOn]);

  return (
    <VolumeButton
      className={clsx(className, {
        "opacity-50": !isVolumeOn,
      })}
      {...restProps}
      onClick={onClickVolume}
    >
      {isVolumeOn ? <VolumeUpIcon /> : <VolumeOffIcon />}
    </VolumeButton>
  );
};
