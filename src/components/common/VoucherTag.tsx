// Libraries
import React, { useCallback } from "react";
import styled, { css } from "styled-components";

// Schemas
import { Voucher } from "schemas";

// Utils
import {
  checkAvailableVoucher,
  countdownDateExpiration,
  isExpired,
} from "utils";

interface VoucherTagProps extends React.HTMLAttributes<HTMLDivElement> {
  voucher?: Voucher;
}

const StyledVoucherTag = styled.div<{ $isAvailable: boolean }>`
  display: flex;
  align-items: center;
  min-height: 17px;
  font-size: 12px;
  color: #9d6e26;
  border-radius: 999px;
  padding: 0px 10px;
  background-color: #fff6c5;
  width: fit-content;

  ${({ $isAvailable }) =>
    !$isAvailable &&
    css`
      color: #000000;
      background-color: #ebebeb;
    `}
`;

export const VoucherTag: React.FC<VoucherTagProps> = ({
  voucher,
  ...restOfProps
}) => {
  const { status, expiryDate, utime } = voucher || {};
  const isAvailable = checkAvailableVoucher(voucher);

  const renderContent = useCallback(() => {
    if (status === "used") {
      return `Đã sử dụng ${countdownDateExpiration(utime || "")}`;
    }

    if (status === "expired" || isExpired(expiryDate || "")) {
      return `Hết hạn sử dụng`;
    }

    if (status === "available") {
      return `Hết hạn sau ${countdownDateExpiration(expiryDate || "")}`;
    }

    return "Không khả dụng";
  }, [status, expiryDate, utime]);

  return (
    <StyledVoucherTag
      $isAvailable={isAvailable}
      dangerouslySetInnerHTML={{ __html: renderContent() }}
      {...restOfProps}
    ></StyledVoucherTag>
  );
};
