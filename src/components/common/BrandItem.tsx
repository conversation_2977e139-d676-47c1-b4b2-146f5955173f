// Libraries
import React from "react";
import styled, { CSSProperties } from "styled-components";

// Components
import {
  <PERSON><PERSON>,
  Card,
  CardProps,
  Ellipsis,
  Image,
  Skeleton,
} from "@antscorp/ama-ui";
import { CoffeeFinIcon } from "components/icons";

// Schemas
import { Scheme } from "schemas";
import { useResponsive } from "hooks";

interface BrandItemProps extends CardProps {
  scheme?: Partial<Scheme>;
  width?: CSSProperties["width"];
  height?: CSSProperties["height"];
  skeleton?: boolean;
  titleFontSize?: CSSProperties["fontSize"];
  descriptionFontSize?: CSSProperties["fontSize"];
}

interface styledCardProps {
  titleFontSize?: CSSProperties["fontSize"];
  descriptionFontSize?: CSSProperties["fontSize"];
}

const StyledCard = styled(Card)<styledCardProps>`
  --adm-card-padding-inline: 10px;
  /* --adm-card-body-padding-block: 10px; */

  flex-shrink: 0;
  border: 1px solid #e8e8e8;

  .brand {
    justify-items: center;
    align-items: center;
    text-align: center;
    display: flex;
    flex-direction: column;

    &__image {
      border-radius: 6.24px;
      width: 52%;
      max-width: 55px;
      object-fit: contain;
      aspect-ratio: 1;
      object-position: top center;
      place-content: center;
      background-color: var(--color-background-image);
      padding: 5%;

      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }

    &__info {
      font-weight: 500;
      font-size: ${(props) => `${props?.titleFontSize}` || "12px"};
      /* font-size: 15px; */
      padding-top: 13px;
      display: flex;
      flex-direction: column;
      gap: 3px;

      .description {
        /* font-size: 12px; */
        font-weight: 400;
        font-size: ${(props) => `${props?.descriptionFontSize}` || "10px"};
        color: var(--color-text-secondary);
      }
    }
  }

  .adm-card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-items: center;
    gap: 12px;
  }

  .skeleton {
    &__image {
      --border-radius: 4px;
      width: 100%;
      aspect-ratio: 1.45;
      height: auto;
    }
  }
`;

export const BrandItem: React.FC<BrandItemProps> = (props) => {
  const {
    width,
    height,
    style,
    scheme,
    skeleton,
    titleFontSize = "12px",
    descriptionFontSize = "10px",
    ...restProps
  } = props;
  const {
    partner,
    image = "",
    name = "",
    point_redeem,
    description = "",
  } = scheme || {};

  // Hooks
  const { isLargeMobile } = useResponsive();

  return (
    <StyledCard
      {...restProps}
      style={{
        width,
        height,
        ...style,
      }}
      titleFontSize={titleFontSize}
      descriptionFontSize={descriptionFontSize}
    >
      {skeleton ? (
        <Skeleton className="skeleton__image" animated />
      ) : (
        <div className="brand">
          <div className="brand__image">
            <Image src={image} />
          </div>
          <div className="brand__info">
            <Ellipsis direction="end" rows={2} content={`${name || ""}`} />
            <Ellipsis
              direction="end"
              className="description"
              rows={2}
              content={`${description || ""}`}
            />
          </div>
        </div>
      )}
    </StyledCard>
  );
};
