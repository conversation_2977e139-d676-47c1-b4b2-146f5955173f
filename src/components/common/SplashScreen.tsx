// SplashScreen.js
import React, { useEffect } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";

// Images
import logo from "assets/images/logos/golden-spoons-logo.png";
import textLogo from "assets/images/logos/golden-spoons-text-logo.svg";
import { useRecoilState } from "recoil";
import { onboardingState } from "state";

interface SplashScreenProps {}

const SplashScreenWrapper = styled(motion.div)`
  display: flex;
  position: absolute;
  z-index: 9999;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #ffffff;
  padding: 24px 16px;
  text-align: center;
  font-size: 12px;
  font-family: "Manrope", sans-serif;

  .logo {
    width: 125px;
    height: 125px;
    /* border-radius: 31px; */
    margin-bottom: 21px;
    margin-top: 187px;
    justify-self: center;
  }

  .title {
    width: 125px;
    font-weight: 500;
    margin-bottom: 25px;
    justify-self: center;
  }

  .subtitle {
    color: var(--color-text-secondary);
    margin-top: 8px;
  }

  .footer {
    color: #8894a0;
  }
`;

export const SplashScreen: React.FC<SplashScreenProps> = () => {
  // Hooks
  const [_, setOnboarding] = useRecoilState(onboardingState);
  useEffect(() => {
    const timeout = setTimeout(() => {
      setOnboarding((prev) => ({
        ...prev,
        isShowSplash: false,
      }));
    }, 1500);

    return () => clearTimeout(timeout);
  }, []);

  return (
    <SplashScreenWrapper
      initial={{ opacity: 1 }}
      animate={{ opacity: 0 }}
      exit={{ opacity: 0 }}
      transition={{ delay: 1.5, duration: 0.5, ease: "easeInOut" }}
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.3 }}
      >
        <motion.img
          src={logo}
          alt="Logo"
          className="logo"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{
            delay: 0.2,
            type: "spring",
            stiffness: 100,
            duration: 0.1,
          }}
        />
        <motion.img
          src={textLogo}
          alt="Logo"
          className="title"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{
            delay: 0.2,
            type: "spring",
            stiffness: 100,
            duration: 0.1,
          }}
        />
        <motion.p
          className="subtitle"
          initial={{ opacity: 0 }}
          animate={{ opacity: [1, 0.6, 1] }}
          transition={{ delay: 0.5, repeat: Infinity, duration: 1.2 }}
        >
          Đang cập nhật dữ liệu ...
        </motion.p>
      </motion.div>

      <motion.div
        className="footer"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        Copyright© 2021 Golden Gate Group. All rights reserved
      </motion.div>
    </SplashScreenWrapper>
  );
};
