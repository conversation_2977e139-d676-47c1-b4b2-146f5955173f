// Libraries
import { useUserInfo } from "hooks";
import React from "react";
import styled from "styled-components";
import { getGreeting } from "utils";
import { GetUserInfoReturns } from "zmp-sdk";
import { Avatar } from "zmp-ui";

interface UserGreetingProps extends React.HTMLAttributes<HTMLDivElement> {}

const StyledUserGreeting = styled.div<{ $color?: string }>`
  --text-color: ${({ $color }) => $color || "#DFD5C8"};

  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-color);
  gap: 6px;

  .user-greeting {
    &__avatar {
      border: 1px solid rgba(128, 109, 88, 1);
    }

    &__username {
      color: var(--text-color);
      font-weight: 700;
      font-size: 16px;
      line-height: 18.75px;
      letter-spacing: 0%;
    }
  }
`;

export const UserGreeting: React.FC<UserGreetingProps> = (props) => {
  const { ...restProps } = props;

  const { userInfo } = useUserInfo();

  return (
    <StyledUserGreeting {...restProps}>
      <Avatar
        className="user-greeting__avatar"
        size={38}
        src={userInfo?.avatar}
      />
      <div className="flex flex-col">
        <span>{getGreeting().message}</span>
        <span className="user-greeting__username">
          {userInfo?.name || "Guest"}
        </span>
      </div>
    </StyledUserGreeting>
  );
};
