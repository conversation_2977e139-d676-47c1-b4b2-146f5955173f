// Libraries
import React, { memo, useEffect } from "react";
import styled from "styled-components";
import { AnimatePresence, motion, Variants } from "motion/react";

// Assets
import summerGameMenuImg from "assets/images/mix-and-match/menu.webp";
import {
  useAppConfig,
  useDelay,
  useNavigateWithSearch,
  useUserInfo,
} from "hooks";
import { CloseIcon } from "components";
import { useRecoilState } from "recoil";
import { loyaltyState } from "state";

// Utils
import { callCdpEvent } from "utils";

// Constants
import { EVENT_CONFIG, ROUTES } from "constant";
import { matchPath, useLocation } from "react-router-dom";

interface SummerGamePopupProps {}

const SummerGameMenuWrapper = styled(motion.div)`
  position: absolute;
  z-index: 999;
  bottom: calc(var(--zaui-safe-area-inset-bottom) + 100px);
  right: 20px;
  width: fit-content;

  .close-button {
    width: 30px;
    height: 30px;
    border-radius: 100%;
    position: absolute;
    right: 0;
    top: 0;
    background-color: #f5c545;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }
`;

const animateVariants: Variants = {
  bounce: {
    y: [0, -10, 0],
    opacity: 1,
    x: "0%",
    transition: {
      delay: 2,
      duration: 1,
      ease: "easeInOut",
      repeat: Infinity,
      repeatType: "reverse",
      repeatDelay: 2,
    },
  },
};

export const SummerGamePopup: React.FC<SummerGamePopupProps> = memo((props) => {
  const { ...restProps } = props;
  const [{ isOpenSummerGamePopup }, setLoyaltyState] =
    useRecoilState(loyaltyState);
  const { gameValidation, isLoading: isAppConfigLoading } = useAppConfig();
  const { userInfo } = useUserInfo();
  const navigate = useNavigateWithSearch();
  const location = useLocation();

  // Variables
  const { mixAndMatch } = gameValidation || {};

  useEffect(() => {
    const isHomePage = matchPath(
      { path: ROUTES.HOME.path, end: true },
      location.pathname
    );

    // Check if the user is on the home page then open the popup
    if (isHomePage) {
      setLoyaltyState((prev) => ({
        ...prev,
        isOpenSummerGamePopup: true,
      }));
    }
  }, [location.pathname, setLoyaltyState]);

  const onClickSummerSticky = () => {
    const matchRoute = Object.values(ROUTES).find((route) =>
      matchPath({ path: route.path, end: true }, location.pathname)
    );

    // Call cdp event tracking
    callCdpEvent({
      uId: userInfo?.id,
      ea: "click",
      ec: "banner",
      data: {
        position: "summer_sticky",
        page_type: matchRoute?.pageCate || "home",
        page_cate: EVENT_CONFIG.MIX_AND_MATCH,
        index: 0,
        destination_url: "",
        image_url: "",
      },
    });

    navigate("/games/mix-and-match");
  };

  return (
    <AnimatePresence>
      {isOpenSummerGamePopup && mixAndMatch.isValid && !isAppConfigLoading && (
        <SummerGameMenuWrapper
          initial={{ opacity: 0, x: "50%" }}
          animate={"bounce"}
          whileInView={{
            opacity: 1,
            x: "0%",
            transition: { duration: 0.5, delay: 0 },
          }}
          exit={{
            opacity: 0,
            x: "50%",
            transition: { duration: 0.5, delay: 0 },
          }}
          variants={animateVariants}
          {...restProps}
        >
          <img
            src={summerGameMenuImg}
            alt=""
            width={180}
            className="object-contain"
            onClick={onClickSummerSticky}
          />
          <div
            className="close-button"
            onClick={() => {
              setLoyaltyState((prev) => ({
                ...prev,
                isOpenSummerGamePopup: false,
              }));
            }}
          >
            <CloseIcon />
          </div>
        </SummerGameMenuWrapper>
      )}
    </AnimatePresence>
  );
});

SummerGamePopup.displayName = "SummerGamePopup";
