// Libraries
import React, { useMemo } from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { lighten, darken, getLuminance } from "polished";

// Components
import { Card, Ellipsis, ProgressBar, Space } from "@antscorp/ama-ui";
import { Avatar } from "zmp-ui";
import { DripsTag } from "./Drips";

// Constants
import { ROUTES, TIER } from "constant";

// Hooks
import { useAppConfig, useResponsive, useUserInfo } from "hooks";
import { BeanIcon } from "components/icons";
import { respond } from "utils";

// Assets
import Noise from "assets/images/others/noise.webp";

interface MembershipCardProps {
  children?: React.ReactNode;
  backgroundColor?: string;
}

const StyledMembershipCard = styled(Card)<{ backgroundColor?: string }>`
  --adm-card-padding-inline: 0px;
  --adm-card-body-padding-block: 0px;

  position: relative;
  /* box-shadow: 0px 3px 9px 0px #002e5926; */
  background-position: top center;
  background-size: cover;
  background-repeat: no-repeat;
  /* overflow: hidden; */

  .member-card {
    position: relative;
    height: 145px;
    padding: 16px;

    ${({ backgroundColor = "#e4b653" }) => {
      const luminance = getLuminance(backgroundColor);
      const lightenAmount = luminance < 0.02 ? 0.15 : 0;
      const adjustedColor = lighten(lightenAmount, backgroundColor);

      return `
      background-image: linear-gradient(
      to right, ${adjustedColor}, ${backgroundColor});
    `;
    }}

    background-blend-mode: overlay;
    background-size: auto;
    z-index: 1;
    overflow: hidden;
    border-radius: 11px;

    &::before {
      content: "";
      position: absolute;
      inset: 0;

      background-image: url(${Noise});
      background-size: cover;

      background-repeat: no-repeat;
      opacity: 0.17;
      z-index: 0;
    }
  }

  .shadow {
    position: absolute;
    top: 5px;
    left: 0;
    right: 0;
    height: 100%;
    width: 93%;
    border-radius: 11px;

    ${({ backgroundColor = "#e4b653" }) => {
      const luminance = getLuminance(backgroundColor);
      const darkenAmount =
        luminance > 0.7 ? 0.45 : luminance > 0.5 ? 0.37 : 0.1;
      const adjustedColor = darken(darkenAmount, backgroundColor);

      return `
      background-color: ${adjustedColor};
    `;
    }}
    z-index: 0;
    justify-self: center;
  }

  .diamond-deco {
    position: absolute;
    width: 195px;
    height: 195px;
    background: rgba(254, 254, 254, 0.24);
    transform: rotate(45deg);
    border-radius: 30px;
    z-index: 0;
    right: -60px;
    bottom: -60px;
    opacity: 0.4;

    &.layer1 {
      right: -70px;
    }

    &.layer2 {
      right: -120px;
    }

    &.layer3 {
      background: rgba(254, 254, 254, 0.5);
      right: -170px;
    }
  }
`;

export const MembershipCard: React.FC<MembershipCardProps> = (props) => {
  const { children = null, backgroundColor = "#17345B" } = props;

  return (
    <StyledMembershipCard
      backgroundColor={backgroundColor}
      bodyClassName="flex flex-col justify-center h-full"
    >
      <div className="member-card">
        <div className="diamond-deco layer1" />
        <div className="diamond-deco layer2" />
        <div className="diamond-deco layer3" />

        {children}
      </div>
      <div className="shadow" />
    </StyledMembershipCard>
  );
};
