// Libraries
import React, { Component, ReactNode } from "react";
import { closeApp } from "zmp-sdk/apis";

// Components
import { SystemNotificationModal } from "components/ui";
import { Button, Text } from "zmp-ui";

// Hooks
import { useAppConfig } from "hooks";

// Constants
import { APP_CONFIG } from "constant";

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

const ErrorBoundaryContent = () => {
  const { appSettings } = useAppConfig();
  const { systemErrorMessages } = appSettings?.globals || {};

  return (
    <>
      <SystemNotificationModal
        showCloseButton={false}
        visible={true}
        maskClosable={false}
        onClose={() => {
          closeApp();
        }}
        title="Thông báo"
        content={
          <div className="flex flex-col gap-5 items-center">
            <Text className="text-center">
              {systemErrorMessages?.maintenance ||
                APP_CONFIG.SYSTEM_ERROR_MESSAGES.maintenance}
            </Text>
          </div>
        }
      />
    </>
  );
};

export class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }

  resetErrorBoundary = (): void => {
    this.setState({ hasError: false, error: null });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      return this.props.fallback || <ErrorBoundaryContent />;
    }

    return this.props.children;
  }
}
