// Libraries
import React from "react";
import styled from "styled-components";
import clsx from "clsx";

// Components
import { Space, SpaceProps } from "@antscorp/ama-ui";
import { CoffeeFinIcon } from "components/icons";

// Types
import { IconProps } from "components/icons/types";

export interface DripsTagProps extends SpaceProps {
  drips: number;
  dripsIconProps?: IconProps;
  color?: string;
  backgroundColor?: string;
}

const StyledDripsTag = styled(Space)`
  width: fit-content;
  background-color: #fff;
  border-radius: 1000px;
`;

export const DripsTag: React.FC<DripsTagProps> = ({
  className,
  drips = 0,
  dripsIconProps,
  color = "var(--color-brown-primary)",
  backgroundColor = "#fff",
  ...props
}) => {
  return (
    <StyledDripsTag
      align="center"
      className={clsx(className, {
        "px-2.5 py-1": true,
      })}
      style={{ "--gap": "7px", color, backgroundColor }}
      {...props}
    >
      <CoffeeFinIcon
        {...dripsIconProps}
        color={dripsIconProps?.color || color}
        width={dripsIconProps?.width || 15.22}
        height={dripsIconProps?.height || 16.85}
      />
      <span className="drips-text font-medium">{drips}</span>
    </StyledDripsTag>
  );
};
