// Libraries
import { motion } from 'motion/react';
import React, { memo, useCallback, useMemo } from 'react';
import styled, { css } from 'styled-components';
import { Avatar } from 'zmp-ui';
import clsx from 'clsx';

// Hooks
import { useNavigateWithSearch, useUserInfo } from 'hooks';

// Components
import { ProgressBar } from '@antscorp/ama-ui';
import { CoffeeFinIcon } from 'components/icons';

// Constants
import { ROUTES } from 'constant';

// Utils
import { formatVietnameseCurrency, respond } from 'utils';
import { MemberRegistration } from 'components/common';

interface MemberCardProps {
  /* Check if ui is sticky or inline */
  sticky?: boolean;
}

const MemberCardWrapper = styled(motion.div)<{ $sticky: boolean }>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* align-items: center; */
  /* justify-content: space-between; */
  background-color: #fff;
  padding: 10px 20px;
  width: 100%;
  gap: 4px;

  ${({ $sticky }) =>
    !$sticky &&
    css`
      box-shadow: 3.01px 0px 15.07px 0px #0000000f;
      border-radius: 7px;
      min-height: 111px;
    `}

  .member-card {
    &__name {
      font-weight: 700;
      font-size: 14px;
      line-height: 110%;
      color: var(--color-text-brown);
    }

    &__tier {
      color: var(--color-text-secondary);
    }

    &__spend {
      font-weight: 500;
      font-size: 11px;
      line-height: 100%;
      color: #969696;
    }

    &__alert {
      font-weight: 500;
      font-size: 10px;
      color: #bd945c;
    }
  }
`;

const DripsTag = styled.div`
  display: flex;
  align-items: center;
  border-radius: 1108px;
  padding: 0px 12px;
  height: 31px;
  gap: 9px;
  font-size: 16px;
  background-color: var(--color-main-primary);
  color: #fff;
`;

export const MemberCard: React.FC<MemberCardProps> = memo(({ sticky }) => {
  const navigate = useNavigateWithSearch();
  const { userInfo, memberTier, nextMemberTier, loyaltyCustomer } = useUserInfo();
  const { avatar } = userInfo || {};
  const { spendingAmount = 0, availablePoints, remainingAmountUpgrade } = loyaltyCustomer || {};
  const { minSpend: nextMinSpend = 0 } = nextMemberTier || {};

  const progressPercent = useMemo(() => {
    if (!memberTier) {
      return 0;
    }

    if (!nextMemberTier) {
      return 100;
    }

    return Math.min((spendingAmount / nextMinSpend) * 100, 100);
  }, [memberTier, nextMemberTier, nextMinSpend, spendingAmount]);

  // Handlers
  const onClickTransactionHistory = useCallback(() => {
    navigate(ROUTES['TRANSACTION_HISTORIES'].path);
  }, [navigate]);

  return (
    <MemberCardWrapper $sticky={!!sticky}>
      <MemberRegistration className="!p-0 !text-text-primary !shadow-none">
        <div className="flex w-full items-center justify-between gap-4">
          <div className="flex flex-1 flex-col gap-2">
            <div className="flex items-center gap-2">
              <Avatar src={avatar} size={48} className="shrink-0" />
              <div className="flex flex-col">
                <div className="member-card__name line-clamp-2 xs:!text-base">{userInfo?.name}</div>
                <div className="member-card__tier">{memberTier?.description || ''}</div>
              </div>
            </div>
            {!sticky && (
              <>
                <ProgressBar
                  percent={progressPercent}
                  style={{
                    '--track-width': '3.52px',
                  }}
                  className="w-[80%]"
                />
                <div className="flex flex-col gap-1">
                  <div className="member-card__spend xs:!text-xs">
                    {`${formatVietnameseCurrency(spendingAmount, 'đ')} / ${formatVietnameseCurrency(
                      nextMemberTier?.minSpend || memberTier?.minSpend,
                      'đ',
                    )}`}
                  </div>
                  {/* {(remainingAmountUpgrade || 0) > 0 && (
                  <div className="member-card__alert xs:!text-[11px]">
                    {`Cần tiêu thêm ${formatVietnameseCurrency(
                      remainingAmountUpgrade || 0,
                      " VNĐ"
                    )}  để thăng hạng `}
                    <span className="capitalize">{nextMemberTier?.name}!</span>
                  </div>
                )} */}
                </div>
              </>
            )}
          </div>
          <div
            className="flex shrink-0 flex-col items-center gap-1.5"
            onClick={onClickTransactionHistory}
          >
            <DripsTag
              className={clsx({
                '!h-7': sticky,
              })}
            >
              <CoffeeFinIcon width={!sticky ? '19px' : '15px'} height={!sticky ? '21px' : '17px'} />
              <span>{availablePoints || 0}</span>
            </DripsTag>
            <div className="text-text-secondary">Lịch sử giao dịch</div>
          </div>
        </div>
        {/* {(remainingAmountUpgrade || 0) > 0 && (
          <div className="member-card__alert xs:!text-[11px]">
            {`Cần tiêu thêm ${formatVietnameseCurrency(
              remainingAmountUpgrade || 0,
              " VNĐ"
            )}  để thăng hạng `}
            <span className="capitalize">{nextMemberTier?.name}!</span>
          </div>
        )} */}
      </MemberRegistration>
    </MemberCardWrapper>
  );
});

MemberCard.displayName = 'MemberCard';
