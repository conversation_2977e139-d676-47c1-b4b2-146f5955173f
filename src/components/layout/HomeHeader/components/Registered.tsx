// Libraries
import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import styled, { css } from 'styled-components';
import clsx from 'clsx';
import { motion } from 'motion/react';

// Components
import { Avatar } from 'zmp-ui';
import { UserDrips } from 'components/common';

// Utils
import { getGreeting } from 'utils';

// Constants
import { ROUTES, TIER } from 'constant';

// Hooks
import { useUserInfo } from 'hooks';
import { Ellipsis } from '@antscorp/ama-ui';

interface RegisteredProps {
  /** Whether to show level label, default is true */
  showLevelLabel?: boolean;
  sticky?: boolean;
}

const StyledDripsTag = styled(UserDrips)<{ $sticky?: boolean }>`
  ${({ $sticky }) =>
    !$sticky &&
    css`
      padding: 5px 10px;

      .coffee-fin-icon {
        width: 22px;
        height: 24.36px;
      }

      .drips-text {
        font-size: 16px;
      }
    `}
`;

const RegisteredWrapper = styled(motion.div)`
  position: relative;
  z-index: 10;
  color: var(--header-text-color);

  .registered {
    &__avatar {
      border: 1px solid rgba(128, 109, 88, 1);
    }

    &__tier-title {
      font-family: OceanSansStd;
      font-weight: 800;
      font-size: 11px;
    }

    &__info-group {
      display: flex;
      flex-direction: column;
      margin-top: 15px;
      min-height: 68px;
      gap: 15px;
    }

    &__group-top {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 6px;
      max-width: 60vw;
    }

    &__group-bottom {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      width: fit-content;
      gap: 5px;
    }

    &__username {
      font-family: 'Red Rose', serif;
      font-weight: 500;
      font-size: 16px;
      line-height: 20px;
    }
  }
`;

export const Registered: React.FC<RegisteredProps> = (props) => {
  const { showLevelLabel = true, sticky = false } = props;

  // Hooks
  const navigate = useNavigate();
  const { loyaltyCustomer, userInfo, memberTier } = useUserInfo();

  // Handlers
  const onClickTransactionHistory = useCallback(() => {
    navigate(ROUTES['TRANSACTION_HISTORIES'].path);
  }, [navigate]);

  return (
    <RegisteredWrapper initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      {showLevelLabel && <span className="registered__tier-title">{memberTier.title}</span>}
      <div
        className={clsx('registered__info-group', {
          '!flex-row items-center': sticky,
        })}
      >
        <div className="registered__group-top">
          <Avatar className="registered__avatar shrink-0" size={38} src={userInfo?.avatar} />
          <div className="flex flex-col">
            <span>{getGreeting().message}</span>
            <Ellipsis
              rows={2}
              content={`${userInfo?.name || loyaltyCustomer?.customerName || 'Guest'}`}
              className="registered__username"
            />
          </div>
        </div>
        <div
          className={clsx('registered__group-bottom', {
            'items-center': sticky,
          })}
          onClick={onClickTransactionHistory}
        >
          <StyledDripsTag $sticky={sticky} />
          <span className="text-sm">Lịch sử giao dịch</span>
        </div>
      </div>
    </RegisteredWrapper>
  );
};
