// Libraries
import React, { memo } from "react";
import { motion } from "motion/react";
import styled from "styled-components";

interface StickyHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  backgroundColor?: string;
  textColor?: string;
}

const StickyHeaderWrapper = styled(motion.div)<{
  $backgroundColor?: string;
  $textColor?: string;
}>`
  --header-text-color: ${({ $textColor }) =>
    $textColor || "var(--color-text-primary)"};

  position: fixed;
  width: 100%;
  max-width: var(--max-width);
  top: 0;
  z-index: 100;
  background-color: ${({ $backgroundColor }) =>
    $backgroundColor || "var(--color-main-primary)"};
  color: var(--header-text-color);
  padding-top: var(--zaui-safe-area-inset-top, 0px);
  box-shadow: 3.01px 0px 15.07px 0px #0000000f;
`;

export const StickyHeader = memo((props: StickyHeaderProps) => {
  const { backgroundColor, textColor, children } = props;

  return (
    <StickyHeaderWrapper
      $backgroundColor={backgroundColor}
      $textColor={textColor}
      initial={{ opacity: 0, y: "-100%" }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: "-100%" }}
      transition={{
        duration: 0.3,
        ease: "easeInOut",
      }}
    >
      {children}
    </StickyHeaderWrapper>
  );
});

StickyHeader.displayName = "StickyHeader";
