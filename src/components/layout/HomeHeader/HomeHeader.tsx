// Libraries
import { AnimatePresence, motion } from "motion/react";
import React, { memo, useEffect } from "react";
import styled from "styled-components";
import { useImmer } from "use-immer";

// Hooks
import { useScroll, useUserInfo } from "hooks";

// Components
import { StickyHeader } from "./components";
import { Avatar } from "zmp-ui";

// Constants
import { TIER } from "constant";

// Icons
import { HeaderLogoV2 } from "components/icons";

// Utils
import { respond } from "utils";

// Assets
import Banner from "assets/images/backgrounds/goldenspoon_banner.webp";
import Logo from "assets/images/logos/highlands-logo.png";

interface HomeHeaderProps {}

interface HomeHeaderWrapper {
  $backgroundColor?: string;
  $textColor?: string;
}

// const HomeHeaderWrapper = styled(motion.div)<{
//   $backgroundColor?: string;
//   $textColor?: string;
// }>`
//   --header-text-color: ${({ $textColor }) =>
//     $textColor || "var(--color-text-primary)"};

//   position: relative;
//   width: 100%;
//   height: calc(312px + var(--zaui-safe-area-inset-top, 0px));
//   /* aspect-ratio: 1.06; */
//   flex-shrink: 0;
//   margin-bottom: -110px;
//   /* padding: 30px 20px 0px; */
//   padding: var(--header-padding-top) 20px 0px;
//   background-color: ${({ $backgroundColor }) =>
//     $backgroundColor || "var(--color-main-primary)"};
//   z-index: 1;
//   overflow: hidden;

//   .home-header {
//     &__logo {
//       position: relative;
//       width: 160px;
//       height: auto;
//       z-index: 1;
//     }

//     &__background {
//       position: absolute;
//       width: 100%;
//       height: auto;
//       bottom: -100%;
//       left: 0;
//     }
//   }
// `;

const HomeHeaderWrapper = styled(motion.div)<HomeHeaderWrapper>`
  --padding-top: var(--header-padding-top);
  --header-height: calc(179px + var(--padding-top));

  position: relative;
  width: 100%;
  height: var(--header-height);
  background: ${(p) => p.$backgroundColor || TIER[0].backgroundColor};
  color: ${(p) => p.$textColor || TIER[0].textColor};
  padding: var(--padding-top) 0px 0px 24px;
  margin-bottom: 65px;
  transition: color 0.3s ease-in-out;

  .home-header {
    &__text-logo {
      position: relative;
      font-family: "Red Rose";
      font-weight: 700;
      font-size: 26px;
      line-height: 100%;
      text-transform: uppercase;
      z-index: 2;
    }

    &__glass-effect {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      aspect-ratio: 1;
      opacity: 0.2;
      clip-path: polygon(0 0, 90% 0, 30% 100%, 0% 100%);
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.5) 0%,
        rgba(255, 255, 255, 0) 100%
      );
    }

    &__tier-text {
      position: relative;
      font-family: "Red Rose";
      font-weight: 700;
      font-size: 14px;
      /* line-height: 100%; */
      text-transform: uppercase;
      z-index: 2;
    }

    &__logo {
      position: absolute;
      right: 0;
      top: var(--padding-top);
    }

    &__member {
      position: absolute;
      z-index: 1;
      left: 0;
      bottom: -65px;
      width: 100%;
      padding: 0px 24px;
      display: flex;
      justify-content: center;
    }
  }

  ${respond.xs`
    .home-header {
      &__text-logo {
        font-size: 28px;
      }


      &__tier-text {
        font-size: 16px;
      }
    }
  `}
`;

const StickyHeaderWrapper = styled(motion.div)`
  width: 100%;
  height: 50px;
  align-content: center;
  font-size: 17px;
  font-weight: 500;
  padding-left: 17px;
`;

const HeaderWrapper = styled(motion.div)`
  width: 100%;
  height: 100%;
  padding-top: 50px;
  justify-items: center;

  .zaui-avatar {
    width: 32px;
    height: 32px;
    background: var(--color-background);
    color: var(--color-text-primary);
    font-size: 12px;
    font-family: "Manrope", sans-serif;
    font-weight: bold;
  }

  .zaui-avatar-group .zaui-avatar-group-item {
    border: none;
  }

  .zaui-avatar-group-item-max-counter {
    border: 1px solid var(--color-border) !important;
    width: 40px;
    border-radius: 100px;
    z-index: 10;
  }

  .member-count {
    color: var(--color-text-secondary);
    font-size: 12px;
    padding-top: 8px;
  }
`;

export const HomeHeader: React.FC<HomeHeaderProps> = memo((props) => {
  // State
  const [state, setState] = useImmer({
    showHeaderSticky: false,
  });
  const { isRegistered, memberTier, isLoading } = useUserInfo();

  // Ref
  const headerRef = React.useRef<HTMLDivElement>(null);

  const { scrollY } = useScroll({
    target: document.querySelector("#layout-content-wrapper") as HTMLElement,
  });

  useEffect(() => {
    const headerHeight = headerRef?.current?.offsetHeight || 0;

    if (headerHeight && scrollY > 0 && scrollY >= headerHeight) {
      setState((draft) => {
        draft.showHeaderSticky = true;
      });
    } else {
      setState((draft) => {
        draft.showHeaderSticky = false;
      });
    }
  }, [scrollY, setState]);

  return (
    <>
      <HeaderWrapper>
        <motion.img src={Banner}></motion.img>

        <Avatar.Group
          maxCounter={4}
          total={76}
          horizontal
          style={{ paddingTop: "20px" }}

          // onCounterClick={() => alert("Clicked!")}
        >
          <Avatar size={32} src={Logo} />
          <Avatar size={32} src={Logo} />
          <Avatar size={32} src={Logo} />
          <Avatar size={32} src={Logo} />
        </Avatar.Group>
        <motion.div className="member-count">
          76 người đã là thành viên của Golden SpoonS
        </motion.div>
      </HeaderWrapper>

      {/* Sticky Header */}
      <AnimatePresence>
        {/* {state.showHeaderSticky && isRegistered && ( */}
        <StickyHeader backgroundColor="#fff">
          <StickyHeaderWrapper>Golden SpoonS</StickyHeaderWrapper>
          {/* <MemberCard sticky /> */}
        </StickyHeader>
        {/* )} */}
      </AnimatePresence>
    </>
  );
});

HomeHeader.displayName = "HomeHeader";
