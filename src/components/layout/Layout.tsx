// Libraries
import * as Sentry from '@sentry/react';
import { useQueryClient } from '@tanstack/react-query';
import clsx from 'clsx';
import React, { lazy, Suspense, useCallback, useEffect, useMemo } from 'react';
import { AnimatePresence } from 'motion/react';

import { matchPath, Outlet, Route, Routes, useLocation, useNavigate } from 'react-router-dom';
import { io, Socket } from 'socket.io-client';
import styled from 'styled-components';
import { getAccessToken } from 'zmp-sdk';

// Hooks
import {
  useDeepCompareEffect,
  useInitFacebookPixel,
  useReceivePostMessage,
  useReconnectSound,
  useUserInfo,
} from 'hooks';

// Components
import { ErrorBoundary, Navigation, SystemError, SplashScreen, WalkThrough } from '../common';
import { ComingSoon, NavBar } from '../ui';

// Pages
import AccumulatePointsPage from 'pages/accumulate-points';
import Error from 'pages/error';
import Form from 'pages/form';
import AffiliatePage from 'pages/games/affiliate';
import MixAndMatchPage from 'pages/games/mix-and-match';
import GiftPage from 'pages/gift';
import GiftDetailPage from 'pages/gift-detail';
import GiftForm from 'pages/gift-form';
import HomePage from 'pages/home';
import SurveyOrderPage from 'pages/survey-order';

// Icons
import { SuccessIcon } from 'components/icons';

// Vouchers
import VoucherDetailPage from 'pages/vouchers/voucher-detail';

// Constants
import { APP_CONFIG, QUERY_KEY, ROUTE_KEYS, ROUTES } from 'constant';

// Components
import { Modal } from '@antscorp/ama-ui';

// Utils
import { callCdpEvent } from 'utils';
import { useRecoilState } from 'recoil';
import { onboardingState } from 'state';
import BrandListPage from 'pages/brand-list';
import BrandDetailPage from 'pages/brand-list/brand-detail';

// Lazy
const AccountPage = lazy(() => import('pages/account'));
const AccountInfoPage = lazy(() => import('pages/account-info'));
const TransactionHistoriesPage = lazy(() => import('pages/transaction-histories'));
const TransactionHistoryPage = lazy(() => import('pages/transaction-history'));
const RestaurantPage = lazy(() => import('pages/restaurant'));
const RestaurantGalleryPage = lazy(() => import('pages/restaurant-gallery'));
const RestaurantOffersPage = lazy(() => import('pages/restaurant-offers'));
const BrandMenu = lazy(() => import('pages/brand-menu'));
const BrandMenus = lazy(() => import('pages/brand-menus'));

interface LayoutProps {}

export const LayoutWrapper = styled.div`
  .zaui-routes {
    height: 100%;
  }

  .layout-content {
    position: relative;
    display: flex;
    flex-direction: column;
    background-color: var(--color-background);
    height: 100%;
    /* overflow: auto; */
  }
`;

const HIDDEN_NAVIGATION_PAGES = [
  '/games/lucky-money',
  '/games/mix-and-match',
  '/vouchers',
  '/error',
  '/gift-form',
  '/transaction-histories',
  '/games',
  '/account/update-account',
  '/brand-list',
  ROUTES[ROUTE_KEYS.ACCOUNT_INFO].path,
  ROUTES[ROUTE_KEYS.TRANSACTION_HISTORIES].path,
  ROUTES[ROUTE_KEYS.TRANSACTION_HISTORY].path,
];

const HIDDEN_ACTION_BAR_PAGES = ['/games/lucky-money', '/error', '/gift-form', '/games'];

const HIDDEN_SYSTEM_ERROR_PAGES = ['/error'];

const SHOW_REDEEM_SUCCESS_MODAL_PAGES = ['/accumulate-points'];

export const Layout: React.FC<LayoutProps> = () => {
  // Hooks
  const [onboarding] = useRecoilState(onboardingState);

  const queryClient = useQueryClient();

  const location = useLocation();
  const navigate = useNavigate();

  const { userInfo, loyaltyCustomer, isLoading, refetchLoyaltyCustomerDetail } = useUserInfo();

  useReconnectSound();
  useInitFacebookPixel();
  useReceivePostMessage();

  // Effects
  useEffect(() => {
    if (userInfo?.id && !isLoading) {
      callCdpEvent({
        ea: 'launch',
        ec: 'app',
        uId: userInfo?.id,
        data: {
          customer_phone: userInfo?.phoneNumber,
        },
      });
    }
  }, [userInfo?.id, userInfo?.phoneNumber, isLoading]);

  useEffect(() => {
    if (userInfo) {
      Sentry.setUser({
        ...userInfo,
        id: userInfo.id,
        username: userInfo.name,
        avatar: userInfo.avatar,
      });
    }
  }, [userInfo]);

  // Effect to handle socket reload user info
  useDeepCompareEffect(() => {
    let socket: Socket | null = null;

    (async () => {
      if (userInfo) {
        const accessToken = await getAccessToken();

        socket = io(`${APP_CONFIG.SOCKET_URL}`, {
          transports: ['websocket'],
          query: {
            network_id: APP_CONFIG.PORTAL_ID,
            phone_number: userInfo?.phoneNumber,
          },
          auth: {
            token: `Bearer ${accessToken || APP_CONFIG.DEV_ACCESS_TOKEN}`,
          },
        });

        // Reload voucher code
        socket.on(`zma_code_${APP_CONFIG.PORTAL_ID}`, async ({ id }) => {
          if (matchPath({ path: '/vouchers/:voucherId', end: true }, location.pathname)) {
            if (location.pathname.includes(`${id || ''}`)) {
              queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.VOUCHER_LIST],
                exact: false,
              });
              queryClient.invalidateQueries({
                queryKey: [
                  QUERY_KEY.VOUCHER_DETAIL,
                  {
                    id: `${id || ''}`,
                  },
                ],
              });
            }
          }
        });

        socket.on(`zma_${APP_CONFIG.PORTAL_ID}_${userInfo?.phoneNumber}`, async () => {
          const { data } = await refetchLoyaltyCustomerDetail();
          const customer = data?.data;

          queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.GET_TRANSACTION_LIST],
            exact: false,
          });

          // If customer available points changed and current page is in SHOW_REDEEM_SUCCESS_MODAL_PAGES
          // then show redeem success modal
          if (
            customer?.availablePoints !== loyaltyCustomer?.availablePoints &&
            SHOW_REDEEM_SUCCESS_MODAL_PAGES.some((path) =>
              matchPath({ path, end: true }, location.pathname),
            )
          ) {
            Modal.clear();
            Modal.show({
              header: <SuccessIcon size={46} className="text-success" />,
              content: (
                <div className="mt-5 text-center">
                  Tích điểm thành công! <br /> Hãy tận hưởng những ưu đãi từ Highlands bạn nhé
                </div>
              ),
              closeOnMaskClick: true,
              closeOnAction: true,
              actions: [
                {
                  key: 'gift',
                  text: 'Xem Quà Tặng',
                  primary: true,
                  onClick: () => {
                    navigate(ROUTES.GIFT.path);
                  },
                },
                {
                  key: 'close',
                  text: 'Đóng',
                },
              ],
            });
          }
        });
      }
    })();

    return () => {
      socket?.disconnect();
      socket?.off(`zma_${APP_CONFIG.PORTAL_ID}_${userInfo?.phoneNumber}`);
    };
  }, [
    location.pathname,
    loyaltyCustomer,
    navigate,
    queryClient,
    refetchLoyaltyCustomerDetail,
    userInfo,
  ]);

  useEffect(() => {
    setTimeout(() => {
      navigate('/brand/:brandId/menu');
    }, 300);
  }, [navigate]);

  // Memos
  const isHiddenNavigation = useMemo(() => {
    return HIDDEN_NAVIGATION_PAGES.some((path) => location.pathname.startsWith(path));
  }, [location.pathname]);

  const isHiddenNavBar = useMemo(() => {
    return HIDDEN_ACTION_BAR_PAGES.some((path) => location.pathname.startsWith(path));
  }, [location.pathname]);

  const isHiddenSystemError = useMemo(() => {
    return HIDDEN_SYSTEM_ERROR_PAGES.some((path) => location.pathname.startsWith(path));
  }, [location.pathname]);

  const isHomePage = useMemo(() => {
    return location.pathname === '/';
  }, [location.pathname]);

  // const isShowGamePopup = useMemo(() => {
  //   return SHOW_GAME_POPUP_PAGES.some((path) => matchPath({ path, end: true }, location.pathname));
  // }, [location.pathname]);

  const routeInfo = useMemo(() => {
    return Object.values(ROUTES).find((route) => {
      return matchPath({ path: route.path, end: true }, location.pathname);
    });
  }, [location.pathname]);

  // Renders
  const renderHeader = useCallback(() => {
    if (location.pathname === '/') {
      return null;
    }

    if (!isHiddenNavBar) {
      return (
        <NavBar
          // title={routeInfo?.title}
          showBackButton={routeInfo?.showBackButton}
          onBack={() => {
            navigate(-1);
          }}
          {...(!!routeInfo?.navRightExtra && {
            right: routeInfo.navRightExtra,
          })}
        >
          {routeInfo?.title || ''}
        </NavBar>
      );
    }

    return null;
  }, [
    location.pathname,
    isHiddenNavBar,
    routeInfo?.title,
    routeInfo?.showBackButton,
    routeInfo?.navRightExtra,
    navigate,
  ]);

  return (
    <ErrorBoundary>
      <LayoutWrapper className="relative flex h-screen w-full max-w-[460px] flex-col">
        <AnimatePresence>{onboarding.isShowSplash && <SplashScreen />}</AnimatePresence>
        <AnimatePresence>{onboarding.isShowWalkThrough && <WalkThrough />}</AnimatePresence>
        <div
          id="layout-content-wrapper"
          className="hide-scrollbar flex h-full flex-col overflow-auto bg-background"
        >
          {renderHeader()}
          <div
            className={clsx('layout-content', {
              'hide-scrollbar overflow-auto': !isHomePage,
            })}
          >
            <Routes>
              <Route path="/" element={<HomePage></HomePage>}></Route>

              <Route path="/error" element={<Error></Error>}></Route>

              <Route path="/form" element={<Form></Form>}></Route>

              <Route path="/survey-order" element={<SurveyOrderPage />}></Route>

              <Route path="/games" element={<Outlet />}>
                <Route index element={<MixAndMatchPage />} />
                <Route path="mix-and-match" element={<MixAndMatchPage />} />
                <Route path="affiliate" element={<AffiliatePage />} />
              </Route>

              <Route path={ROUTES.BRAND_LIST.path} element={<BrandListPage />}>
                <Route path=":brandId" element={<BrandDetailPage />} />
              </Route>

              <Route path="/vouchers" element={<Outlet />}>
                <Route index element={<ComingSoon />} />
                <Route path=":voucherId" element={<VoucherDetailPage />} />
              </Route>

              <Route path="/gift-form" element={<GiftForm />}></Route>

              <Route path="/accumulate-points" element={<AccumulatePointsPage />} />

              <Route path="/gift" element={<Outlet />}>
                <Route index element={<GiftPage />} />
                <Route path=":giftId" element={<GiftDetailPage />} />
              </Route>

              <Route
                path={ROUTES[ROUTE_KEYS.TRANSACTION_HISTORIES].path}
                element={<TransactionHistoriesPage />}
              />

              <Route
                path={ROUTES[ROUTE_KEYS.TRANSACTION_HISTORY].path}
                element={<TransactionHistoryPage />}
              />

              <Route path={ROUTES[ROUTE_KEYS.ACCOUNT].path} element={<AccountPage />} />

              <Route path={ROUTES[ROUTE_KEYS.ACCOUNT_INFO].path} element={<AccountInfoPage />} />

              <Route
                path={ROUTES[ROUTE_KEYS.RESTAURANT_DETAIL].path}
                element={<RestaurantPage />}
              />

              <Route
                path={ROUTES[ROUTE_KEYS.RESTAURANT_GALLERY].path}
                element={<RestaurantGalleryPage />}
              />

              <Route
                path={ROUTES[ROUTE_KEYS.RESTAURANT_OFFERS].path}
                element={<RestaurantOffersPage />}
              />

              <Route path={ROUTES[ROUTE_KEYS.BRAND_MENU].path} element={<BrandMenu />} />

              <Route path={ROUTES[ROUTE_KEYS.BRAND_MENUS].path} element={<BrandMenus />} />
            </Routes>
          </div>
        </div>

        <Suspense>
          {!isHiddenNavigation ? (
            <div className="w-full shrink-0">
              <Navigation />
            </div>
          ) : null}
        </Suspense>

        {/* {isShowGamePopup && <SummerGamePopup />} */}
      </LayoutWrapper>

      {!isHiddenSystemError && <SystemError />}
    </ErrorBoundary>
  );
};
