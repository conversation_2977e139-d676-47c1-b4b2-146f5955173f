import clsx from 'clsx';
import { platformServices } from 'services/platform';

export const PhoneNumber = ({
  phoneNumber,
  className,
}: {
  phoneNumber: string;
  className?: string;
}) => {
  const handleClick = () => {
    platformServices.contact.openPhoneCallAsync(phoneNumber);
  };

  return (
    <span
      className={clsx('font-semibold text-blue', className)}
      role="button"
      onClick={handleClick}
    >
      {phoneNumber}
    </span>
  );
};
