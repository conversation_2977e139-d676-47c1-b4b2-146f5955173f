// Libraries
import React, { forwardRef } from "react";
import {
  Swiper as ReactSwiper,
  SwiperProps as ReactSwiperProps,
} from "swiper/react";
import "swiper/css/pagination";

// Swiper modules
import { Autoplay, Pagination, Navigation, FreeMode } from "swiper/modules";

// Styles
import "swiper/css";

interface SwiperProps extends ReactSwiperProps {}

export const Swiper: React.FC<SwiperProps> = (props) => {
  const { ...restProps } = props;

  return (
    <ReactSwiper
      {...restProps}
      modules={[Autoplay, Pagination, Navigation, FreeMode]}
    />
  );
};

export { SwiperSlide } from "swiper/react";
