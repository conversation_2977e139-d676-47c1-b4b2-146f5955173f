import type { <PERSON>a, StoryObj } from '@storybook/react';
import { OfferCard } from './OfferCard';

const meta: Meta<typeof OfferCard> = {
  title: 'UI/OfferCard',
  component: OfferCard,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'OfferCard component displays a single restaurant promotional offer with hero image, G-Coin reward, and exchange button.',
      },
    },
  },
  argTypes: {
    gCoinReward: {
      control: { type: 'number' },
      description: 'G-Coin reward amount',
    },
    onExchange: {
      action: 'onExchange',
      description: 'Callback when exchange button is clicked',
    },
    onClick: {
      action: 'onClick',
      description: 'Callback when card is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof OfferCard>;

export const Default: Story = {
  args: {
    restaurantName: 'Gogi',
    title: 'Sumo Hà Nội tặng 100.000 suất buffet 0đ',
    expiryDate: '15/11/2021',
    gCoinReward: 30000,
    heroImage: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=140&fit=crop',
    onExchange: () => console.log('Exchange clicked'),
    onClick: () => console.log('Card clicked'),
  },
};

export const WithLogo: Story = {
  args: {
    restaurantName: 'KFC',
    restaurantLogo:
      'https://images.unsplash.com/photo-1606787366850-de6330128bfc?w=32&h=32&fit=crop',
    title: 'Combo gà rán giảm 50% cho khách hàng mới',
    expiryDate: '20/11/2021',
    gCoinReward: 15000,
    heroImage: 'https://images.unsplash.com/photo-1513639776629-7b61b0ac49cb?w=300&h=140&fit=crop',
    onExchange: () => console.log('Exchange clicked'),
    onClick: () => console.log('Card clicked'),
  },
};

export const HighReward: Story = {
  args: {
    restaurantName: 'Premium Restaurant',
    title: 'Buffet cao cấp miễn phí cho 100 khách hàng đầu tiên',
    expiryDate: '31/12/2021',
    gCoinReward: 100000,
    heroImage: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=300&h=140&fit=crop',
    onExchange: () => console.log('Exchange clicked'),
    onClick: () => console.log('Card clicked'),
  },
};
