import React from 'react';
import clsx from 'clsx';
import GCoinIcon from 'assets/svg/gcoin.svg?react';

export interface OfferCardProps {
  restaurantName: string;
  restaurantLogo?: string;
  title: string;
  expiryDate: string;
  gCoinReward: number;
  heroImage: string;
  className?: string;
  onExchange?: () => void;
  onClick?: () => void;
}

const RestaurantLogo: React.FC<{ logo?: string; name: string }> = ({ logo, name }) => {
  if (logo) {
    return (
      <div className="h-4 w-4 overflow-hidden rounded-full bg-white">
        <img src={logo} alt={name} className="h-full w-full object-cover" />
      </div>
    );
  }

  // Default logo with restaurant initials
  const initials = name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase();

  return (
    <div className="flex h-4 w-4 items-center justify-center rounded-full bg-black text-xs font-bold text-white">
      {initials}
    </div>
  );
};

export const OfferCard: React.FC<OfferCardProps> = ({
  restaurantName,
  restaurantLogo,
  title,
  expiryDate,
  gCoinReward,
  heroImage,
  className,
  onExchange,
  onClick,
}) => {
  return (
    <div className={clsx('flex cursor-pointer flex-col gap-3', className)} onClick={onClick}>
      {/* Hero Image */}
      <div className="relative h-[140px] w-full overflow-hidden rounded-md bg-gray-200">
        <img src={heroImage} alt={title} className="h-full w-full object-cover" />
      </div>

      {/* Content */}
      <div className="flex flex-col gap-2">
        {/* Restaurant Info & Title */}
        <div className="flex flex-col gap-1">
          {/* Restaurant Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <RestaurantLogo logo={restaurantLogo} name={restaurantName} />

              <span className="text-xs font-bold leading-[18px] text-black">{restaurantName}</span>
            </div>
          </div>

          {/* Offer Title */}
          <h3 className="text-text-primary font-medium leading-[22px]">{title}</h3>
        </div>

        {/* Bottom Section */}
        <div className="flex items-end justify-between">
          {/* Left: Expiry Date & G-Coin */}
          <div className="flex flex-col gap-1">
            <span className="text-xs leading-[18px] text-secondary">Đến {expiryDate}</span>

            <div className="flex items-center gap-1">
              <GCoinIcon className="h-4 w-4" />

              <span className="font-medium leading-[22px] text-black">
                {gCoinReward.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Right: Exchange Button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onExchange?.();
            }}
            className="hover:bg-primary/90 flex h-8 items-center justify-center rounded-full bg-primary px-4 py-1.5 transition-all"
          >
            <span className="text-14 font-semibold leading-[20px]">Đổi ngay</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default OfferCard;
