// Libraries
import clsx from "clsx";
import React from "react";

// components
import { Text } from "zmp-ui";

interface ComingSoonProps extends React.HTMLAttributes<HTMLDivElement> {}

export const ComingSoon: React.FC<ComingSoonProps> = (props) => {
  const { className, ...restProps } = props;

  return (
    <div
      className={clsx(
        "flex flex-col items-center justify-center h-full w-full  px-4",
        className
      )}
      {...restProps}
    >
      <Text className="!font-bold text-center">
        Tính năng đang phát triển, <br /> xin quý khách vui lòng quay lại sau.
      </Text>
    </div>
  );
};
