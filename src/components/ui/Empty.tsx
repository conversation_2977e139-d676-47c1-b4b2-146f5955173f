// Libraries
import React from "react";
import styled from "styled-components";
import { motion } from "motion/react";

// Assets
import emptyVoucher from "assets/images/backgrounds/empty-voucher.png";
import { Text } from "zmp-ui";

interface EmptyProps {
  icon?: string;
  description?: string;
}

const EmptyWrapper = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 30px;

  > .empty-description {
    color: #777777;
  }
`;

export const Empty: React.FC<EmptyProps> = (props) => {
  const {
    icon = emptyVoucher,
    description = "Bạn chưa có mã giảm giá nào.",
    ...restProps
  } = props;

  return (
    <EmptyWrapper
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{
        type: "spring",
        stiffness: 100,
        damping: 10,
        duration: 0.3,
      }}
      className="empty-ui"
    >
      <img src={icon} width={80} alt="" />
      <Text className="empty-description">{description}</Text>
    </EmptyWrapper>
  );
};
