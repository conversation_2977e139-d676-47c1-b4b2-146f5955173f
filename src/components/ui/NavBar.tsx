// Libraries
import React from "react";
import styled from "styled-components";

// Components
import {
  NavBar as AMANavbar,
  type NavBarProps as AMANavbarProps,
} from "@antscorp/ama-ui";

// Icons
import { ArrowLeftIcon, ChevronLeftIcon } from "lucide-react";

interface NavBarProps extends AMANavbarProps {
  showBackButton?: boolean;
}

const StyledNavBar = styled(AMANavbar)`
  --height: 44px;

  margin-top: var(--header-padding-top);
  flex-shrink: 0;
  background-color: #ffffff;

  .adm-nav-bar-back-arrow {
    display: flex;
    align-items: center;
  }

  .adm-nav-bar-title {
    font-weight: 500;
    font-size: 16px;
    color: var(--color-text-primary);
  }

  .adm-nav-bar-back-arrow {
    margin-right: 10px;
  }

  .back-title {
    font-family: "Red Rose";
    font-weight: 500;
    font-size: 20px;
  }
`;

export const NavBar: React.FC<NavBarProps> = (props) => {
  const { back, showBackButton = true, children, ...restProps } = props;

  return (
    <div className="bg-white">
      <StyledNavBar
        {...restProps}
        // back={<div className="back-title">{back}</div>}
        backIcon={
          showBackButton ? (
            <ChevronLeftIcon size={28} color="var(--color-text-primary)" />
          ) : (
            false
          )
        }
      >
        {children}
      </StyledNavBar>
    </div>
  );
};
