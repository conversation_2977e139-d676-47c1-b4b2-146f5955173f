import React from 'react';
import { Divider } from '@antscorp/ama-ui';
import StarIcon from 'assets/svg/star.svg?react';
import {
  FrownIcon,
  MehIcon,
  SmileIcon,
  LaughIcon,
  SmilePlusIcon,
  ChevronRightIcon,
} from 'lucide-react';
import clsx from 'clsx';

export interface ReviewItem {
  id: string;
  userName: string;
  phoneNumber: string;
  date: string;
  rating: number;
  reviewText: string;
  avatar?: string;
}

export interface RatingProps {
  title?: string;
  overallRating: number;
  totalReviews: number;
  reviews: ReviewItem[];
  className?: string;
  onViewAllReviews?: () => void;
}

const EmojiRating: React.FC<{ rating: number }> = ({ rating }) => {
  const emojis = [FrownIcon, MehIcon, SmileIcon, LaughIcon, SmilePlusIcon];

  return (
    <div className="flex gap-1.5">
      {emojis.map((Emoji, index) => {
        const isActive = index + 1 === rating;

        return (
          <div
            key={index}
            className="flex h-5 w-5 items-center justify-center rounded-full text-xs"
          >
            <Emoji
              className={clsx('cursor-pointer', isActive ? 'text-green' : 'text-border')}
              size={16}
            />
          </div>
        );
      })}
    </div>
  );
};

const UserAvatar: React.FC<{ avatar?: string; userName: string }> = ({ avatar, userName }) => {
  if (avatar) {
    return (
      <div className="h-12 w-12 overflow-hidden rounded-full bg-gray-200">
        <img src={avatar} alt={userName} className="h-full w-full object-cover" />
      </div>
    );
  }

  // Default avatar with user initials
  const initials = userName
    .split(' ')
    .map((name) => name.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase();

  return (
    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-sm font-medium text-white">
      {initials}
    </div>
  );
};

const ReviewCard: React.FC<{ review: ReviewItem }> = ({ review }) => {
  return (
    <div className="flex flex-col gap-3">
      <div className="flex gap-5">
        <UserAvatar avatar={review.avatar} userName={review.userName} />

        <div className="flex flex-1 flex-col gap-1">
          <h4 className="font-medium leading-[22px]">{review.userName}</h4>
          <p className="text-xs leading-[14px] text-secondary">{review.phoneNumber}</p>
        </div>

        <span className="text-xs leading-[18px] text-secondary">{review.date}</span>
      </div>

      <div className="flex items-center gap-1.5">
        <EmojiRating rating={review.rating} />
      </div>

      <p className="w-full leading-[22px] text-black">{review.reviewText}</p>
    </div>
  );
};

export const Rating: React.FC<RatingProps> = ({
  title = 'Đánh giá',
  overallRating,
  totalReviews,
  reviews,
  onViewAllReviews,
  className = '',
}) => {
  return (
    <div className={`flex flex-col gap-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-[17px] font-bold leading-[24px]">{title}</h3>

        <div className="flex items-center gap-1">
          <div className="flex items-center gap-1">
            <StarIcon className="h-4 w-4 text-primary" />

            <span className="font-bold leading-[22px] text-primary">
              {overallRating.toFixed(1)}
            </span>
          </div>

          <span className="font-medium leading-[22px]">({totalReviews} đánh giá)</span>
        </div>
      </div>

      {/* Reviews List */}
      <div className="flex flex-col">
        {reviews.map((review, index) => (
          <React.Fragment key={review.id}>
            <ReviewCard review={review} />

            {index < reviews.length - 1 && <Divider />}
          </React.Fragment>
        ))}
      </div>

      {/* View All Button */}
      {onViewAllReviews && (
        <button onClick={onViewAllReviews} className="flex items-center gap-1.5 py-2 text-blue">
          <span className="font-medium leading-[22px]">Xem tất cả đánh giá</span>

          <ChevronRightIcon className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default Rating;
