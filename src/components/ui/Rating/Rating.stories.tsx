import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Rating, ReviewItem } from './Rating';

const meta: Meta<typeof Rating> = {
  title: 'UI/Rating',
  component: Rating,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'Rating component displays user reviews with emoji-based ratings, user avatars, and overall rating summary.',
      },
    },
  },
  argTypes: {
    overallRating: {
      control: { type: 'range', min: 0, max: 5, step: 0.1 },
      description: 'Overall rating score (0-5)',
    },
    totalReviews: {
      control: { type: 'number' },
      description: 'Total number of reviews',
    },
    title: {
      control: 'text',
      description: 'Title of the rating section',
    },
    onViewAllReviews: {
      action: 'onViewAllReviews',
      description: 'Callback when "View all reviews" button is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Rating>;

const mockReviews: ReviewItem[] = [
  {
    id: '1',
    userName: '<PERSON><PERSON><PERSON>',
    phoneNumber: '0988 *** ***',
    date: '14/06/2021',
    rating: 5,
    reviewText:
      'Thái độ nhân viên nhiệt tình. Món ăn thì đa dạng. Tuy nhiên nước lẩu hơi cay với mình.',
  },
  {
    id: '2',
    userName: 'Nguyễn Mạnh Hưng',
    phoneNumber: '0988 *** ***',
    date: '13/06/2021',
    rating: 4,
    reviewText: 'Không gian đẹp, rộng rãi, thoải mái. Đồ ăn Ngon',
  },
  {
    id: '3',
    userName: 'Mỹ Linh',
    phoneNumber: '0988 *** ***',
    date: '13/06/2021',
    rating: 5,
    reviewText: 'Nhà hàng đẹp, nhiều góc chụp lên ảnh rất đẹp. Mình và bạn bè hay đi ăn.',
  },
];

export const Default: Story = {
  args: {
    title: 'Đánh giá',
    overallRating: 5.0,
    totalReviews: 59,
    reviews: mockReviews,
    onViewAllReviews: () => console.log('View all reviews clicked'),
  },
};

export const WithAvatars: Story = {
  args: {
    title: 'Đánh giá',
    overallRating: 4.8,
    totalReviews: 124,
    reviews: [
      {
        ...mockReviews[0],
        avatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      },
      {
        ...mockReviews[1],
        avatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      },
      {
        ...mockReviews[2],
        avatar:
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      },
    ],
    onViewAllReviews: () => console.log('View all reviews clicked'),
  },
};

export const LowRating: Story = {
  args: {
    title: 'Đánh giá',
    overallRating: 2.3,
    totalReviews: 15,
    reviews: [
      {
        id: '1',
        userName: 'Nguyễn Văn A',
        phoneNumber: '0988 *** ***',
        date: '10/06/2021',
        rating: 2,
        reviewText: 'Dịch vụ chưa tốt lắm, món ăn không đúng như mong đợi.',
      },
      {
        id: '2',
        userName: 'Trần Thị B',
        phoneNumber: '0988 *** ***',
        date: '09/06/2021',
        rating: 1,
        reviewText: 'Rất thất vọng về chất lượng phục vụ.',
      },
    ],
    onViewAllReviews: () => console.log('View all reviews clicked'),
  },
};

export const MixedRatings: Story = {
  args: {
    title: 'Đánh giá',
    overallRating: 3.7,
    totalReviews: 45,
    reviews: [
      {
        id: '1',
        userName: 'Phạm Minh C',
        phoneNumber: '0988 *** ***',
        date: '15/06/2021',
        rating: 5,
        reviewText: 'Tuyệt vời! Sẽ quay lại lần sau.',
      },
      {
        id: '2',
        userName: 'Lý Hoàng D',
        phoneNumber: '0988 *** ***',
        date: '14/06/2021',
        rating: 3,
        reviewText: 'Bình thường, không có gì đặc biệt.',
      },
      {
        id: '3',
        userName: 'Võ Thị E',
        phoneNumber: '0988 *** ***',
        date: '13/06/2021',
        rating: 2,
        reviewText: 'Cần cải thiện chất lượng dịch vụ.',
      },
    ],
    onViewAllReviews: () => console.log('View all reviews clicked'),
  },
};

export const SingleReview: Story = {
  args: {
    title: 'Đánh giá',
    overallRating: 4.5,
    totalReviews: 1,
    reviews: [mockReviews[0]],
    onViewAllReviews: () => console.log('View all reviews clicked'),
  },
};

export const NoViewAllButton: Story = {
  args: {
    title: 'Đánh giá',
    overallRating: 4.2,
    totalReviews: 28,
    reviews: mockReviews.slice(0, 2),
    // onViewAllReviews is undefined, so button won't show
  },
};

export const CustomTitle: Story = {
  args: {
    title: 'Nhận xét của khách hàng',
    overallRating: 4.6,
    totalReviews: 89,
    reviews: mockReviews,
    onViewAllReviews: () => console.log('View all reviews clicked'),
  },
};

export const EmptyReviews: Story = {
  args: {
    title: 'Đánh giá',
    overallRating: 0,
    totalReviews: 0,
    reviews: [],
    onViewAllReviews: () => console.log('View all reviews clicked'),
  },
};
