import React from 'react';
import clsx from 'clsx';
import { Form, Input as AMAInput, InputProps as AMAInputProps } from '@antscorp/ama-ui';
import styled from 'styled-components';

const { Item: AMAFormItem } = Form;

// Use source component props directly
type Status = 'error' | 'warning' | 'success' | 'default';

export type InputProps = AMAInputProps &
  Omit<React.ComponentProps<typeof AMAFormItem>, 'children'> & {
    status?: Status;
    inputClassName?: string;
    prefixIcon?: React.ReactNode;
    suffixIcon?: React.ReactNode;
  };

const StyledFormItem = styled(AMAFormItem)<{ status?: Status }>`
  &.adm-form-item {
    border: none;
    padding: 0;

    .adm-list-item-content-main {
      padding: 8px 0px;

      .adm-form-item-feedback-error {
        margin-top: 8px;
        font-size: 12px;
        line-height: 18px;
      }
    }

    .adm-form-item-label {
      display: inline-block;
      font-size: var(--adm-font-size-main);
      line-height: 18px;
      font-weight: 500;
      color: var(--color-text-primary);
      margin-bottom: 8px;
      padding: 0;
      font-family: var(--adm-font-family);

      .adm-form-item-required-asterisk {
        left: unset;
        right: -0.6em;
        color: var(--color-error);
      }
    }

    .adm-form-item-child {
      position: relative;

      .adm-input {
        --placeholder-color: #99a3ad;
        --font-size: 15px;
        --background-color: var(--color-background);
        border: 1px solid
          ${({ status }) => (status ? `var(--color-${status})` : 'var(--color-grey-secondary)')};
        border-radius: 6px;
        padding: 12px 16px;
        font-weight: 500;

        &.has-prefix {
          padding-left: 46px;
        }

        &.has-suffix {
          padding-right: 46px;
        }

        .adm-input-element {
          min-height: 22px;
          line-height: 22px;
        }

        ::placeholder {
          color: var(--placeholder-color);
          font-size: var(--font-size);
          font-weight: 400;
        }
      }

      .input-prefix-icon,
      .input-suffix-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        color: var(--color-text-primary);
        z-index: 1;
        pointer-events: none;

        svg {
          width: 20px;
          height: 20px;
        }
      }

      .input-prefix-icon {
        left: 16px;
      }

      .input-suffix-icon {
        right: 16px;
      }
    }
  }
`;

export const FormItem = StyledFormItem;

export const Input: React.FC<InputProps> = ({
  // Form.Item props
  label,
  name,
  required,
  rules,
  status,
  className,
  dependencies,
  shouldUpdate,
  trigger,
  validateTrigger,
  valuePropName,
  getValueFromEvent,
  normalize,
  preserve,
  getValueProps,
  // Icon props
  prefixIcon,
  suffixIcon,
  // All remaining props go to Input
  ...inputProps
}) => {
  const inputClassName = clsx(
    {
      'has-prefix': prefixIcon,
      'has-suffix': suffixIcon,
    },
    inputProps.inputClassName,
  );

  return (
    <StyledFormItem
      label={label}
      name={name}
      required={required}
      rules={rules}
      status={status}
      className={className}
      dependencies={dependencies}
      shouldUpdate={shouldUpdate}
      trigger={trigger}
      validateTrigger={validateTrigger}
      valuePropName={valuePropName}
      getValueFromEvent={getValueFromEvent}
      normalize={normalize}
      preserve={preserve}
      getValueProps={getValueProps}
    >
      {prefixIcon && <div className="input-prefix-icon">{prefixIcon}</div>}
      <AMAInput {...inputProps} className={inputClassName} />
      {suffixIcon && <div className="input-suffix-icon">{suffixIcon}</div>}
    </StyledFormItem>
  );
};
