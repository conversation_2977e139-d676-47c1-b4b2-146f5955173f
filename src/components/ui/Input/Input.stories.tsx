import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Grid, Form } from '@antscorp/ama-ui';
import { Search, Mail, Eye, EyeOff, User, Phone, Lock, Calendar, ChevronDown } from 'lucide-react';
import { Input } from './Input';
import { Button } from '../Button';
import '../../../css/app.scss';

const meta = {
  title: 'ui/Input',
  component: Input,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'Input component wraps Form.Item and Input from ama-ui with custom styling that follows project theme.',
      },
    },
  },
  argTypes: {},
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Họ',
    placeholder: 'Nguyễn Hữu',
    required: true,
  },
};

export const WithIcon: Story = {
  args: {
    label: 'Tìm kiếm',
    placeholder: '<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm',
    prefixIcon: <Search />,
  },
};

export const WithPrefixIcon: Story = {
  args: {
    label: 'Email',
    type: 'email',
    placeholder: '<EMAIL>',
    prefixIcon: <Mail />,
    required: true,
  },
};

export const WithSuffixIcon: Story = {
  args: {
    label: 'Mật khẩu',
    type: 'password',
    placeholder: 'Nhập mật khẩu',
    suffixIcon: <Eye />,
    required: true,
  },
};

export const WithBothIcons: Story = {
  args: {
    label: 'Tìm kiếm nâng cao',
    placeholder: 'Nhập từ khóa',
    prefixIcon: <Search />,
    suffixIcon: <ChevronDown />,
  },
};

export const WithoutRequired: Story = {
  args: {
    label: 'Tên',
    placeholder: 'Lượng',
  },
};

export const Email: Story = {
  args: {
    label: 'Email',
    status: 'warning',
    type: 'email',
    placeholder: '<EMAIL>',
    required: true,
  },
};

export const Password: Story = {
  args: {
    label: 'Mật khẩu',
    type: 'password',
    status: 'error',
    placeholder: 'Nhập mật khẩu',
    required: true,
  },
};

export const PhoneNumber: Story = {
  args: {
    label: 'Số điện thoại',
    type: 'tel',
    placeholder: '0987 654 321',
    required: true,
  },
};

export const DateOfBirth: Story = {
  args: {
    label: 'Ngày sinh',
    placeholder: '12/12/1993',
    required: true,
  },
};

export const Disabled: Story = {
  args: {
    label: 'Trường vô hiệu hóa',
    placeholder: 'Không thể nhập',
    disabled: true,
  },
};

export const WithValue: Story = {
  args: {
    label: 'Họ và tên',
    value: 'Nguyễn Văn An',
    placeholder: 'Nhập họ và tên',
  },
};

export const CustomerRegistrationForm: Story = {
  render: () => (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      <h3 style={{ marginBottom: '20px', color: 'var(--color-text-primary)' }}>
        Đăng ký thành viên
      </h3>
      <Input label="Họ" placeholder="Nguyễn Hữu" required name="firstName" />
      <Input label="Tên" placeholder="Lượng" required name="lastName" />
      <Input label="Ngày sinh" placeholder="12/12/1993" required name="dateOfBirth" />
      <Input
        label="Email"
        type="email"
        placeholder="<EMAIL>"
        required
        name="email"
      />
      <Input label="Số điện thoại" type="tel" placeholder="0987 654 321" required name="phone" />
    </div>
  ),
};

export const AllInputTypes: Story = {
  render: () => (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      <h3 style={{ marginBottom: '20px', color: 'var(--color-text-primary)' }}>Các loại input</h3>
      <Input label="Text" type="text" placeholder="Văn bản thông thường" name="text" />
      <Input label="Email" type="email" placeholder="<EMAIL>" name="email" />
      <Input label="Password" type="password" placeholder="Nhập mật khẩu" name="password" />
      <Input label="Number" type="number" placeholder="123456" name="number" />
      <Input label="Telephone" type="tel" placeholder="0987 654 321" name="tel" />
    </div>
  ),
};

export const ValidationStates: Story = {
  render: () => (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      <h3 style={{ marginBottom: '20px', color: 'var(--color-text-primary)' }}>Trạng thái input</h3>
      <Input label="Bình thường" placeholder="Input bình thường" name="normal" />
      <Input label="Bắt buộc" placeholder="Trường bắt buộc" required name="required" />
      <Input label="Vô hiệu hóa" placeholder="Không thể nhập" disabled name="disabled" />
      <Input label="Có giá trị" defaultValue="Đã có nội dung" name="withValue" />
    </div>
  ),
};

export const IconVariations: Story = {
  render: () => (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      <h3 style={{ marginBottom: '20px', color: 'var(--color-text-primary)' }}>
        Input với các loại icon
      </h3>

      <Input
        label="Tìm kiếm"
        placeholder="Tìm kiếm sản phẩm"
        prefixIcon={<Search />}
        name="search"
      />

      <Input
        label="Email"
        type="email"
        placeholder="<EMAIL>"
        prefixIcon={<Mail />}
        required
        name="email"
      />

      <Input
        label="Họ tên"
        placeholder="Nguyễn Văn A"
        prefixIcon={<User />}
        required
        name="fullName"
      />

      <Input
        label="Số điện thoại"
        type="tel"
        placeholder="0987 654 321"
        prefixIcon={<Phone />}
        required
        name="phone"
      />

      <Input
        label="Mật khẩu"
        type="password"
        placeholder="Nhập mật khẩu"
        prefixIcon={<Lock />}
        suffixIcon={<EyeOff />}
        required
        name="password"
      />

      <Input
        label="Ngày sinh"
        placeholder="dd/mm/yyyy"
        prefixIcon={<Calendar />}
        required
        name="birthDate"
      />
    </div>
  ),
};

function ValidationErrorsComponent() {
  const [form] = Form.useForm();

  // Set initial values and trigger validation errors
  React.useEffect(() => {
    form.setFieldsValue({
      firstName: 'Nguyễn Hữu',
      lastName: 'Lượng',
      dateOfBirth: '12/12/2020',
      email: '<EMAIL>',
    });

    // Trigger validation to show errors
    form.validateFields().catch(() => {
      // Expected to catch validation errors
    });
  }, [form]);

  return (
    <div style={{ maxWidth: '500px', margin: '0 auto', padding: '20px' }}>
      <h3 style={{ marginBottom: '20px', color: 'var(--color-text-primary)' }}>
        Trạng thái lỗi validation
      </h3>

      <Form form={form} layout="vertical">
        <Grid columns={2} gap={16}>
          <Grid.Item>
            <Input label="Họ" placeholder="Nguyễn Hữu" required name="firstName" />
          </Grid.Item>
          <Grid.Item>
            <Input label="Tên" placeholder="Lượng" required name="lastName" />
          </Grid.Item>
        </Grid>

        <Input
          label="Ngày sinh"
          placeholder="dd/mm/yyyy"
          required
          name="dateOfBirth"
          rules={[
            {
              required: true,
              message: 'Vui lòng nhập ngày sinh',
            },
            {
              validator: (_, value) => {
                if (value === '12/12/2020') {
                  return Promise.reject('Độ tuổi cho phép đăng ký tài khoản từ 13 đến 90');
                }
                return Promise.resolve();
              },
            },
          ]}
        />

        <Input
          label="Email"
          placeholder="<EMAIL>"
          type="email"
          required
          name="email"
          rules={[
            {
              required: true,
              message: 'Vui lòng nhập email',
            },
            {
              validator: (_, value) => {
                if (value === '<EMAIL>') {
                  return Promise.reject('Email không đúng định dạng');
                }
                return Promise.resolve();
              },
            },
          ]}
        />
      </Form>
    </div>
  );
}

export const ValidationErrors: Story = {
  render: () => <ValidationErrorsComponent />,
};

function RegistrationFormWithErrorsComponent() {
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('Form submitted:', values);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Form validation failed:', errorInfo);
  };

  // Set initial values and trigger validation errors
  React.useEffect(() => {
    form.setFieldsValue({
      firstName: 'Nguyễn Hữu',
      lastName: 'Lượng',
      dateOfBirth: '12/12/2020',
      email: '<EMAIL>',
    });

    // Trigger validation to show errors
    setTimeout(() => {
      form.validateFields().catch(() => {
        // Expected to catch validation errors
      });
    }, 100);
  }, [form]);

  return (
    <div
      style={{
        maxWidth: '500px',
        margin: '0 auto',
        padding: '10px',
        backgroundColor: 'var(--color-background)',
        borderRadius: '8px',
      }}
    >
      <h3
        style={{
          marginBottom: '20px',
          color: 'var(--color-text-primary)',
          textAlign: 'center',
        }}
      >
        Đăng ký tài khoản - Có lỗi validation
      </h3>

      <Form form={form} layout="vertical" onFinish={onFinish} onFinishFailed={onFinishFailed}>
        {/* First row - First name and Last name */}
        <Grid columns={2} gap={16}>
          <Grid.Item>
            <Input label="Họ" placeholder="Nguyễn Hữu" required name="firstName" />
          </Grid.Item>
          <Grid.Item>
            <Input label="Tên" placeholder="Lượng" required name="lastName" />
          </Grid.Item>
        </Grid>

        {/* Date of birth with error */}
        <Input
          label="Ngày sinh"
          placeholder="dd/mm/yyyy"
          required
          name="dateOfBirth"
          rules={[
            {
              required: true,
              message: 'Vui lòng nhập ngày sinh',
            },
            {
              validator: (_, value) => {
                if (value === '12/12/2020') {
                  return Promise.reject('Độ tuổi cho phép đăng ký tài khoản từ 13 đến 90');
                }
                return Promise.resolve();
              },
            },
          ]}
        />

        {/* Email with error */}
        <Input
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          required
          name="email"
          rules={[
            {
              required: true,
              message: 'Vui lòng nhập email',
            },
            {
              validator: (_, value) => {
                if (value === '<EMAIL>') {
                  return Promise.reject('Email không đúng định dạng');
                }
                return Promise.resolve();
              },
            },
          ]}
        />

        {/* Action button */}
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Button size="large" color="primary" type="submit">
            Đăng ký ngay
          </Button>
        </div>
      </Form>
    </div>
  );
}

export const RegistrationFormWithErrors: Story = {
  render: () => <RegistrationFormWithErrorsComponent />,
};
