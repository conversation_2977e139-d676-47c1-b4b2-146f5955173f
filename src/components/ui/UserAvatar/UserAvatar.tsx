import { Avatar, type AvatarProps } from '@antscorp/ama-ui';
import baseProfileAvatar from 'assets/images/others/default_avatar.webp';
import styled from 'styled-components';

export type UserAvatarProps = Omit<AvatarProps, 'src'> & {
  src?: string;
};

const StyledAvatar = styled(Avatar)`
  &.adm-image.adm-avatar {
    border-radius: 1000px;
  }
`;

export const UserAvatar = (props: UserAvatarProps) => {
  const { src = baseProfileAvatar, ...restProps } = props;

  return <StyledAvatar {...restProps} src={src} />;
};
