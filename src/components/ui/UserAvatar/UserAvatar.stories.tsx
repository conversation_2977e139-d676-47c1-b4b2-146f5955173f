import { Meta, StoryObj } from '@storybook/react';
import { UserAvatar } from './UserAvatar';

const meta = {
  title: 'ui/UserAvatar',
  component: UserAvatar,
} satisfies Meta<typeof UserAvatar>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    src: 'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YXZhdGFyfGVufDB8fDB8fHww',
  },
};

export const WithoutSrc: Story = {};
