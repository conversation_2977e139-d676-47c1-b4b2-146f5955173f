import React from 'react';
import { Modal as AMAModal, ModalProps as AMAModalProps } from '@antscorp/ama-ui';
import styled from 'styled-components';

export interface ModalProps extends AMAModalProps {}

const StyledModal = styled(AMAModal)`
  .adm-modal-content {
    overflow: visible;
  }

  .adm-modal-body {
    padding: 30px 24px;
    text-align: center;
  }

  .adm-modal-header {
    text-align: center;
    
    .adm-modal-title {
      font-weight: 700;
      font-size: 20px;
    }
  }

  .adm-modal-content {
    font-size: 15px;
    line-height: 24px;
    max-height: 500px;
    overflow: auto;
  }
`;

export const Modal: React.FC<ModalProps> = (props) => {
  return <StyledModal {...props} />;
};