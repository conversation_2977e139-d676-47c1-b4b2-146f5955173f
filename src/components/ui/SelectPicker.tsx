// Libraries
import { ChevronDownIcon } from "lucide-react";
import React, { memo, ReactNode, useCallback, useMemo } from "react";
import styled from "styled-components";
import { useImmer } from "use-immer";

// Icons
import { Button, ButtonProps, Picker, PickerProps } from "@antscorp/ama-ui";

// Types
import { PickerValue } from "antd-mobile/es/components/picker-view";

export type SelectPickerItem = {
  label: ReactNode;
  value: string | number;
  key?: string | number;
};

export interface SelectPickerProps {
  pickerProps?: Omit<
    PickerProps,
    "columns" | "value" | "defaultValue" | "onChange"
  >;
  buttonProps?: ButtonProps;
  value?: string;
  defaultValue?: string | number;
  onChange?: (value: string) => void;
  options?: SelectPickerItem[];
}

const ButtonPicker = styled(Button)`
  --adm-button-border-color: #00000033;
  font-size: 12px;
`;

export const SelectPicker: React.FC<SelectPickerProps> = memo((props) => {
  const { value, defaultValue, options, pickerProps, buttonProps, onChange } =
    props;

  // State
  const [state, setState] = useImmer({
    visible: false,
    internalValue: value || defaultValue,
  });

  // Variables
  const { internalValue, visible } = state;
  const displayValue = value !== undefined ? value : internalValue;

  // Memo
  const currentOption = useMemo(() => {
    return options?.find((option) => option.value === displayValue);
  }, [displayValue, options]);

  // Handlers
  const toggleActionVisible = useCallback(() => {
    setState((draft) => {
      draft.visible = !draft.visible;
    });
  }, [setState]);

  const onConfirmValue = useCallback(
    (value: PickerValue[]) => {
      setState((draft) => {
        draft.internalValue = value[0] as string;
      });

      onChange?.(value[0] as string);
    },
    [onChange, setState]
  );

  return (
    <>
      <ButtonPicker
        shape="rounded"
        icon={<ChevronDownIcon size={16} />}
        iconPosition="right"
        onClick={toggleActionVisible}
      >
        {currentOption?.label}
      </ButtonPicker>

      <Picker
        {...pickerProps}
        columns={[options || []]}
        visible={visible}
        onClose={() => {
          setState((draft) => {
            draft.visible = false;
          });
        }}
        value={[displayValue || ""]}
        onConfirm={onConfirmValue}
      />
    </>
  );
});

SelectPicker.displayName = "SelectPicker";
