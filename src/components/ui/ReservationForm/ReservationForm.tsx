import React from 'react';
import { Form } from '@antscorp/ama-ui';
import { ChatIcon, EmailIcon, UserBadgeIcon } from 'components/icons';
import { Input, FormItem } from '../Input';
import { NumberPickerField } from '../NumberPickerField';
import { TagSelector } from '../TagSelector';
import { ReservationTimeField } from '../ReservationTimeField';

interface ReservationFormProps {
  onSubmit?: (values: ReservationFormValues) => void;
  initialValues?: Partial<ReservationFormValues>;
}

export interface ReservationFormValues {
  reservationTime: Date;
  numberOfPeople: number;
  fullName: string;
  phoneNumber: string;
  email: string;
  notes: string;
  selectedTags: string[];
}

export const ReservationForm: React.FC<ReservationFormProps> = ({
  onSubmit,
  initialValues = {},
}) => {
  const [form] = Form.useForm<ReservationFormValues>();

  const quickTags = ['Có trẻ em', '<PERSON>i<PERSON><PERSON> sinh nhật', '<PERSON>àn gần cửa sổ'];

  const handleSubmit = (values: ReservationFormValues) => {
    onSubmit?.(values);
  };

  return (
    <div className="flex flex-col gap-4">
      <Form form={form} onFinish={handleSubmit} initialValues={initialValues}>
        {/* Thời gian đặt bàn */}
        <FormItem
          name="reservationTime"
          label="Thời gian đặt bàn"
          required
          rules={[{ required: true, message: 'Vui lòng chọn thời gian đặt bàn' }]}
          initialValue={initialValues.reservationTime || new Date()}
        >
          <ReservationTimeField />
        </FormItem>

        {/* Số lượng người */}
        <FormItem
          name="numberOfPeople"
          label="Số lượng người"
          required
          rules={[{ required: true, message: 'Vui lòng chọn số lượng người' }]}
        >
          <NumberPickerField min={1} max={20} placeholder="Chọn số lượng người" />
        </FormItem>

        {/* Họ Tên */}
        <Input
          label="Họ Tên"
          name="fullName"
          required
          placeholder="Họ Tên của bạn"
          prefixIcon={<UserBadgeIcon size={24} />}
          rules={[{ required: true, message: 'Vui lòng điền đủ Họ Tên của bạn nhé' }]}
        />

        {/* Số điện thoại */}
        <Input
          label="Số điện thoại"
          name="phoneNumber"
          type="tel"
          required
          placeholder="0988522xxx"
          rules={[
            { required: true, message: 'SĐT chưa chính xác. Vui lòng nhập lại' },
            { pattern: /^[0-9]{10,11}$/, message: 'SĐT chưa chính xác. Vui lòng nhập lại' },
          ]}
        />

        {/* Email */}
        <Input
          label="Email"
          name="email"
          type="email"
          placeholder="<EMAIL>"
          prefixIcon={<EmailIcon size={24} />}
          rules={[{ type: 'email', message: 'Email không đúng định dạng' }]}
        />

        {/* Ghi chú */}
        <Input
          label="Ghi chú (Nếu có)"
          name="notes"
          placeholder="Nhập hoặc chọn nhanh ghi chú ..."
          prefixIcon={<ChatIcon size={24} />}
        />

        {/* Quick Tags */}
        <FormItem name="selectedTags" initialValue={initialValues.selectedTags || []}>
          <TagSelector options={quickTags} />
        </FormItem>
      </Form>
    </div>
  );
};
