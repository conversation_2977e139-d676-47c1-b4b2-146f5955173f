import type { Meta, StoryObj } from '@storybook/react';
import { ReservationForm } from './ReservationForm';

const meta = {
  title: 'ui/ReservationForm',
  component: ReservationForm,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'ReservationForm component for restaurant table reservations. Includes time selection, number of people, customer details, notes, and quick tag selection.',
      },
    },
  },
  argTypes: {
    onSubmit: { action: 'submitted' },
    initialValues: {
      control: 'object',
      description: 'Initial form values',
    },
  },
} satisfies Meta<typeof ReservationForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'Default reservation form with empty fields.',
      },
    },
  },
};

export const WithInitialValues: Story = {
  args: {
    initialValues: {
      reservationTime: new Date(),
      numberOfPeople: 4,
      fullName: 'Nguyễn Văn An',
      phoneNumber: '0988522123',
      email: '<EMAIL>',
      notes: '<PERSON><PERSON><PERSON> gần cửa sổ, có ghế trẻ em',
      selectedTags: ['Có trẻ em', '<PERSON>àn gần cửa sổ'],
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'Reservation form pre-filled with initial values and selected tags.',
      },
    },
  },
};

