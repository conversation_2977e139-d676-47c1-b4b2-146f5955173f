import React from 'react';

export interface TagSelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  options: string[];
}

export const TagSelector: React.FC<TagSelectorProps> = ({
  value = [],
  onChange,
  options,
}) => {
  const handleTagClick = (tag: string) => {
    const newValue = value.includes(tag)
      ? value.filter((t) => t !== tag)
      : [...value, tag];
    onChange?.(newValue);
  };

  return (
    <div className="flex flex-wrap gap-2.5">
      {options.map((tag) => (
        <div
          key={tag}
          className={`flex cursor-pointer items-center rounded-[54px] px-3 py-[7px] text-center font-roboto text-[15px] font-normal leading-[22px] text-black transition-colors ${
            value.includes(tag) ? 'bg-primary' : 'border border-NL800 bg-white hover:bg-gray-50'
          }`}
          onClick={() => handleTagClick(tag)}
        >
          {tag}
        </div>
      ))}
    </div>
  );
};