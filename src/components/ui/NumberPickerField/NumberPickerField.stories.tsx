import type { Meta, StoryObj } from '@storybook/react';
import { NumberPickerField } from './NumberPickerField';

const meta: Meta<typeof NumberPickerField> = {
  title: 'Components/UI/NumberPickerField',
  component: NumberPickerField,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Chọn số lượng người',
    min: 1,
    max: 20,
  },
};

export const WithInitialValue: Story = {
  args: {
    value: 4,
    placeholder: 'Chọn số lượng người',
    min: 1,
    max: 20,
  },
};

export const CustomRange: Story = {
  args: {
    placeholder: 'Chọn số lượng (2-50)',
    min: 2,
    max: 50,
  },
};