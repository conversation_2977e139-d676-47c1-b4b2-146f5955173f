import React, { useState, useEffect } from 'react';
import { Popup } from '@antscorp/ama-ui';
import { ChevronDown } from 'lucide-react';
import { UsersIcon, CloseIcon } from 'components/icons';
import { Button } from '../Button';
import clsx from 'clsx';

// Interface theo chuẩn Ant Design Mobile custom field
export interface NumberPickerFieldProps {
  value?: number;
  onChange?: (value: number) => void;
  placeholder?: string;
  min?: number;
  max?: number;
}

export const NumberPickerField: React.FC<NumberPickerFieldProps> = ({
  value,
  onChange,
  placeholder = 'Chọn số lượng người',
  min = 1,
  max = 20,
}) => {
  const [visible, setVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Sync inputValue with external value prop
  useEffect(() => {
    setInputValue(value ? value.toString() : '');
  }, [value]);

  // Sync inputValue with current value when popup opens
  const handleOpenPopup = () => {
    setVisible(true);
    setInputValue(value ? value.toString() : '');
  };

  // Predefined options based on design
  const quickOptions = [1, 2, 3, 4, 8, 10, 12, 16];

  const handleOptionSelect = (selectedValue: number) => {
    onChange?.(selectedValue);
    setInputValue(selectedValue.toString());
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleSave = () => {
    if (inputValue.trim()) {
      const numValue = parseInt(inputValue.trim());

      if (!isNaN(numValue) && numValue >= min && numValue <= max) {
        onChange?.(numValue);
      }
    }

    setVisible(false);
  };

  const displayText = value ? `${value} người` : placeholder;

  return (
    <>
      {/* Trigger Field */}
      <div
        className="relative flex min-h-12 cursor-pointer items-center gap-3 rounded-md border border-[#bfc6cc] bg-white px-4 py-3"
        onClick={handleOpenPopup}
      >
        <UsersIcon size={24} />
        <span
          className="flex-1 font-roboto text-[15px] font-normal leading-[22px]"
          style={{ color: value ? '#000' : '#99a3ad' }}
        >
          {displayText}
        </span>
        <ChevronDown size={24} className="pointer-events-none" />
      </div>

      {/* Bottom Sheet Popup */}
      <Popup
        visible={visible}
        position="bottom"
        closeOnSwipe
        className="rounded-t-[24px] bg-white"
        onClose={() => setVisible(false)}
      >
        <div className="flex flex-col gap-5 px-4 pb-6 pt-4">
          {/* Title Bar */}
          <div className="relative flex items-center justify-center">
            <h3 className="font-roboto text-[20px] font-semibold leading-[28px] text-NL300">
              Đặt bàn cho
            </h3>
            <button
              className="hover:bg-gray-100 absolute right-0 flex h-8 w-8 items-center justify-center rounded-full"
              onClick={() => setVisible(false)}
            >
              <CloseIcon size={20} className="text-[#001a33]" />
            </button>
          </div>

          {/* Divider */}
          <div className="h-px w-full bg-[#e9ebed]" />

          {/* Quick Options */}
          <div className="flex flex-wrap items-center gap-x-2 gap-y-3">
            {quickOptions.map((option) => (
              <Button
                key={option}
                color="default"
                className={clsx({
                  'border-GGG text-GGG': option === +inputValue,
                })}
                onClick={() => handleOptionSelect(option)}
              >
                {option} người
              </Button>
            ))}
          </div>

          {/* Custom Input */}
          <div className="flex items-center gap-3 rounded-md border border-NL800 bg-white px-4 py-3">
            <UsersIcon size={24} className="text-[#99a3ad]" />

            <input
              type="number"
              min={min}
              max={max}
              value={inputValue}
              onChange={handleInputChange}
              placeholder="Nhập số lượng người ..."
              className="flex-1 bg-transparent font-roboto font-normal leading-[22px] text-NL300 outline-none placeholder:text-NL700"
            />
          </div>

          {/* Save Button */}
          <Button size="large" className="font-semibold" onClick={handleSave}>
            Lưu lại
          </Button>
        </div>
      </Popup>
    </>
  );
};
