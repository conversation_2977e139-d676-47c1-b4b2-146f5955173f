// Libraries
import React, { ReactNode } from "react";
import ReactPullToRefresh from "react-simple-pull-to-refresh";

interface PullToRefreshProps {
  isPullable?: boolean;
  canFetchMore?: boolean;
  onRefresh: () => Promise<any>;
  onFetchMore?: () => Promise<any>;
  refreshingContent?: JSX.Element | string;
  pullingContent?: JSX.Element | string;
  children: JSX.Element;
  pullDownThreshold?: number;
  fetchMoreThreshold?: number;
  maxPullDownDistance?: number;
  resistance?: number;
  backgroundColor?: string;
  className?: string;
  icon?: ReactNode;
}

export const PullToRefresh: React.FC<PullToRefreshProps> = (props) => {
  const { ...restProps } = props;

  return <ReactPullToRefresh {...restProps} />;
};
