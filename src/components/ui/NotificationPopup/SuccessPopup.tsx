import React from 'react';
import { NotificationPopup, NotificationPopupProps } from './NotificationPopup';

export interface SuccessPopupProps extends Omit<NotificationPopupProps, 'type' | 'showSecondaryButton' | 'secondaryButtonText' | 'onSecondaryAction' | 'primaryButtonText'> {
  buttonText?: string;
  onConfirm?: () => void;
}

export const SuccessPopup: React.FC<SuccessPopupProps> = ({
  buttonText = 'Đồng ý',
  onConfirm,
  ...props
}) => {
  return (
    <NotificationPopup
      {...props}
      type="success"
      primaryButtonText={buttonText}
      onPrimaryAction={onConfirm}
      showSecondaryButton={false}
    />
  );
};