import React from 'react';
import { NotificationPopup, NotificationPopupProps } from './NotificationPopup';

export interface WarningPopupProps extends Omit<NotificationPopupProps, 'type' | 'showSecondaryButton' | 'primaryButtonText'> {
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export const WarningPopup: React.FC<WarningPopupProps> = ({
  confirmText = 'Xác nhận',
  cancelText = 'Hủy',
  onConfirm,
  onCancel,
  ...props
}) => {
  return (
    <NotificationPopup
      {...props}
      type="warning"
      primaryButtonText={confirmText}
      secondaryButtonText={cancelText}
      onPrimaryAction={onConfirm}
      onSecondaryAction={onCancel}
      showSecondaryButton={true}
    />
  );
};