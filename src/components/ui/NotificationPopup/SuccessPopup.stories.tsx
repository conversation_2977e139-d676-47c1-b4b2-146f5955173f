import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { SuccessPopup, SuccessPopupProps } from './SuccessPopup';
import { Button } from '@antscorp/ama-ui';

const meta: Meta<typeof SuccessPopup> = {
  title: 'UI/NotificationPopup/SuccessPopup',
  component: SuccessPopup,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'SuccessPopup is a specialized notification popup for displaying success messages with a green checkmark icon.',
      },
    },
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'Title of the success popup',
    },
    description: {
      control: 'text',
      description: 'Description text (supports line breaks)',
    },
    buttonText: {
      control: 'text',
      description: 'Text for the confirm button',
    },
    visible: {
      control: 'boolean',
      description: 'Whether the popup is visible',
    },
    onConfirm: {
      action: 'onConfirm',
      description: 'Callback when confirm button is clicked',
    },
    onClose: {
      action: 'onClose',
      description: 'Callback when popup is closed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof SuccessPopup>;

// Interactive wrapper for showing the popup
const InteractiveWrapper = (props: SuccessPopupProps) => {
  const [visible, setVisible] = useState(false);

  return (
    <div style={{ padding: '20px' }}>
      <Button onClick={() => setVisible(true)} type="button">
        Show Success Popup
      </Button>

      <SuccessPopup {...props} visible={visible} onClose={() => setVisible(false)} />
    </div>
  );
};

export const Default: Story = {
  args: {
    visible: true,
    title: 'Thành công',
    description: 'HSD: 05.10.2021 19:23\nĐiểm còn lại: 60.132 G-Coin',
  },
};

export const VoucherRedemption: Story = {
  args: {
    visible: true,
    title: 'Đổi ưu đãi thành công',
    description: 'Voucher Giảm giá 50% đã được thêm vào tài khoản\nHSD: 25.12.2024',
    buttonText: 'Sử dụng ngay',
  },
};

export const PointsEarned: Story = {
  args: {
    visible: true,
    title: 'Nhận điểm thành công',
    description: 'Bạn đã nhận được 1.500 G-Coin\nTổng điểm hiện tại: 15.250 G-Coin',
    buttonText: 'Tuyệt vời',
  },
};

export const OrderCompleted: Story = {
  args: {
    visible: true,
    title: 'Đặt bàn thành công',
    description:
      'Mã đặt bàn: #GG123456\nThời gian: 19:00 - 25/12/2024\nNhà hàng: Golden Gate Vincom Center',
    buttonText: 'Xem chi tiết',
  },
};

export const ProfileUpdated: Story = {
  args: {
    visible: true,
    title: 'Cập nhật thông tin thành công',
    description: 'Thông tin cá nhân của bạn đã được lưu.',
  },
};

export const WithoutDescription: Story = {
  args: {
    visible: true,
    title: 'Đã lưu thay đổi',
    description: '',
    buttonText: 'OK',
  },
};

export const CustomButton: Story = {
  args: {
    visible: true,
    title: 'Chúc mừng!',
    description: 'Bạn đã hoàn thành nhiệm vụ hàng ngày\nNhận được 500 G-Coin',
    buttonText: 'Nhận thưởng',
  },
};

export const Interactive: Story = {
  render: (args) => <InteractiveWrapper {...args} />,
  args: {
    title: 'Thao tác thành công',
    description: 'Dữ liệu đã được cập nhật thành công.',
  },
};
