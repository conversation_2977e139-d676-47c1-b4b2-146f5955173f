import React from 'react';
import { Modal, type ModalProps } from '../Modal';
import styled from 'styled-components';
import SuccessIcon from 'assets/svg/check.svg?react';
import WarningIcon from 'assets/svg/priority-high.svg?react';

export type NotificationPopupType = 'success' | 'warning';

export interface NotificationPopupProps extends Omit<ModalProps, 'children'> {
  type: NotificationPopupType;
  title?: string;
  description?: string;
  primaryButtonText?: string;
  secondaryButtonText?: string;
  showSecondaryButton?: boolean;
  onPrimaryAction?: () => void;
  onSecondaryAction?: () => void;
}

const StyledNotificationModal = styled(Modal)`
  .adm-modal-body {
    padding: 24px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;

    .adm-modal-content {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-direction: column;
      border-radius: 12px;
      padding: 0;
      max-width: 327px;
      margin: 0 auto;
    }

    .adm-modal-header {
      display: none;
    }

    .adm-modal-footer.adm-modal-footer-empty {
      display: none;
    }
  }
`;

const IconContainer = styled.div<{ $type: NotificationPopupType }>`
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 1000px;
  background-color: ${({ $type }) => ($type === 'success' ? '#00C578' : '#F5832F')};

  svg {
    width: 26px;
    height: 26px;
  }
`;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
`;

const TitleContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  max-width: 295px;
`;

const Title = styled.h3`
  font-family: Roboto, sans-serif;
  font-weight: 600;
  font-size: 20px;
  line-height: 1.4em;
  text-align: center;
  color: #001a33;
  margin: 0;
`;

const Description = styled.p`
  font-family: Roboto, sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4666666666666666em;
  text-align: center;
  color: #667685;
  margin: 0;
  white-space: pre-line;
`;

const ButtonContainer = styled.div<{ $isSingle: boolean }>`
  display: flex;
  ${({ $isSingle }) =>
    $isSingle
      ? 'justify-content: center;'
      : 'justify-content: stretch; gap: 15px; width: 100%; max-width: 295px;'}
`;

const BaseButton = styled.button`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  height: 40px;
  border-radius: 9999px;
  border: none;
  cursor: pointer;

  font-family: Roboto, sans-serif;
  font-weight: 600;
  font-size: 15px;
  line-height: 1.4666666666666666em;
  text-align: center;
  color: #001a33;

  &:hover {
    opacity: 0.9;
  }

  &:active {
    transform: scale(0.98);
  }
`;

const PrimaryButton = styled(BaseButton)<{ $isSingle: boolean }>`
  background: #e4b653;
  width: ${({ $isSingle }) => ($isSingle ? '164px' : 'auto')};
  flex: ${({ $isSingle }) => ($isSingle ? 'none' : '1')};
`;

const SecondaryButton = styled(BaseButton)`
  background: #e9ebed;
  flex: 1;
`;

export const NotificationPopup: React.FC<NotificationPopupProps> = ({
  type,
  title,
  description,
  primaryButtonText = type === 'success' ? 'Đồng ý' : 'Xác nhận',
  secondaryButtonText = 'Hủy',
  showSecondaryButton = type === 'warning',
  onPrimaryAction,
  onSecondaryAction,
  ...modalProps
}) => {
  const handlePrimaryAction = () => {
    onPrimaryAction?.();
    modalProps.onClose?.();
  };

  const handleSecondaryAction = () => {
    onSecondaryAction?.();
    modalProps.onClose?.();
  };

  const getDefaultTitle = () => {
    if (title) return title;

    return type === 'success' ? 'Thành công' : 'Cảnh báo';
  };

  return (
    <StyledNotificationModal
      content={
        <>
          <IconContainer $type={type}>
            {type === 'success' ? <SuccessIcon /> : <WarningIcon />}
          </IconContainer>

          <ContentContainer>
            <TitleContainer>
              <Title>{getDefaultTitle()}</Title>

              {description && <Description>{description}</Description>}
            </TitleContainer>

            <ButtonContainer $isSingle={!showSecondaryButton}>
              {showSecondaryButton && (
                <SecondaryButton onClick={handleSecondaryAction}>
                  {secondaryButtonText}
                </SecondaryButton>
              )}

              <PrimaryButton $isSingle={!showSecondaryButton} onClick={handlePrimaryAction}>
                {primaryButtonText}
              </PrimaryButton>
            </ButtonContainer>
          </ContentContainer>
        </>
      }
      {...modalProps}
    />
  );
};
