import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { NotificationPopup, NotificationPopupProps } from './NotificationPopup';
import { Button } from '@antscorp/ama-ui';

const meta: Meta<typeof NotificationPopup> = {
  title: 'UI/NotificationPopup',
  component: NotificationPopup,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'NotificationPopup is a versatile modal component that can display success or warning messages with customizable icons, text, and actions.',
      },
    },
  },
  argTypes: {
    type: {
      control: { type: 'radio' },
      options: ['success', 'warning'],
      description: 'The type of notification popup',
    },
    title: {
      control: 'text',
      description: 'Title of the popup',
    },
    description: {
      control: 'text',
      description: 'Description text (supports line breaks)',
    },
    primaryButtonText: {
      control: 'text',
      description: 'Text for the primary action button',
    },
    secondaryButtonText: {
      control: 'text',
      description: 'Text for the secondary action button',
    },
    showSecondaryButton: {
      control: 'boolean',
      description: 'Whether to show the secondary button',
    },
    visible: {
      control: 'boolean',
      description: 'Whether the popup is visible',
    },
    onPrimaryAction: {
      action: 'onPrimaryAction',
      description: 'Callback when primary button is clicked',
    },
    onSecondaryAction: {
      action: 'onSecondaryAction',
      description: 'Callback when secondary button is clicked',
    },
    onClose: {
      action: 'onClose',
      description: 'Callback when popup is closed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof NotificationPopup>;

// Interactive wrapper for showing the popup
const InteractiveWrapper = (props: NotificationPopupProps) => {
  const [visible, setVisible] = useState(false);

  return (
    <div style={{ padding: '20px' }}>
      <Button onClick={() => setVisible(true)} type="button">
        Show Popup
      </Button>

      <NotificationPopup {...props} visible={visible} onClose={() => setVisible(false)} />
    </div>
  );
};

export const SuccessDefault: Story = {
  args: {
    type: 'success',
    visible: true,
    title: 'Thành công',
    description: 'HSD: 05.10.2021 19:23\nĐiểm còn lại: 60.132 G-Coin',
  },
};

export const SuccessCustom: Story = {
  args: {
    type: 'success',
    visible: true,
    title: 'Đổi ưu đãi thành công',
    description: 'Voucher đã được thêm vào tài khoản của bạn',
    primaryButtonText: 'Hoàn tất',
  },
};

export const SuccessWithoutDescription: Story = {
  args: {
    type: 'success',
    visible: true,
    title: 'Đã lưu thay đổi',
    description: '',
  },
};

export const WarningDefault: Story = {
  args: {
    type: 'warning',
    visible: true,
    title: 'Cảnh báo',
    description: 'Bạn có muốn sử dụng 30.000 G-Coin để đổi ưu đãi.',
  },
};

export const WarningConfirmAction: Story = {
  args: {
    type: 'warning',
    visible: true,
    title: 'Xác nhận hành động',
    description: 'Bạn có chắc chắn muốn xóa mục này?\nHành động này không thể hoàn tác.',
    primaryButtonText: 'Xóa',
    secondaryButtonText: 'Giữ lại',
  },
};

export const WarningLongContent: Story = {
  args: {
    type: 'warning',
    visible: true,
    title: 'Điều khoản sử dụng',
    description:
      'Bằng việc tiếp tục, bạn đồng ý với điều khoản sử dụng và chính sách bảo mật của chúng tôi.\n\nVui lòng đọc kỹ trước khi xác nhận.',
    primaryButtonText: 'Đồng ý',
    secondaryButtonText: 'Đọc thêm',
  },
};

export const Interactive: Story = {
  render: args => <InteractiveWrapper {...args} />,
  args: {
    type: 'success',
    title: 'Thao tác thành công',
    description: 'Dữ liệu đã được cập nhật thành công.',
  },
};
