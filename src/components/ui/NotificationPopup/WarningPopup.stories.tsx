import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { WarningPopup, WarningPopupProps } from './WarningPopup';
import { Button } from '@antscorp/ama-ui';

const meta: Meta<typeof WarningPopup> = {
  title: 'UI/NotificationPopup/WarningPopup',
  component: WarningPopup,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'WarningPopup is a specialized notification popup for displaying warning/confirmation messages with an orange warning icon and two action buttons.',
      },
    },
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'Title of the warning popup',
    },
    description: {
      control: 'text',
      description: 'Description text (supports line breaks)',
    },
    confirmText: {
      control: 'text',
      description: 'Text for the confirm button',
    },
    cancelText: {
      control: 'text',
      description: 'Text for the cancel button',
    },
    visible: {
      control: 'boolean',
      description: 'Whether the popup is visible',
    },
    onConfirm: {
      action: 'onConfirm',
      description: 'Callback when confirm button is clicked',
    },
    onCancel: {
      action: 'onCancel',
      description: 'Callback when cancel button is clicked',
    },
    onClose: {
      action: 'onClose',
      description: 'Callback when popup is closed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof WarningPopup>;

// Interactive wrapper for showing the popup
const InteractiveWrapper = (props: WarningPopupProps) => {
  const [visible, setVisible] = useState(false);

  return (
    <div style={{ padding: '20px' }}>
      <Button onClick={() => setVisible(true)} type="button">
        Show Warning Popup
      </Button>

      <WarningPopup {...props} visible={visible} onClose={() => setVisible(false)} />
    </div>
  );
};

export const Default: Story = {
  args: {
    visible: true,
    title: 'Cảnh báo',
    description: 'Bạn có muốn sử dụng 30.000 G-Coin để đổi ưu đãi.',
  },
};

export const VoucherRedemption: Story = {
  args: {
    visible: true,
    title: 'Đổi ưu đãi ngay?',
    description: 'Bạn có muốn sử dụng 15.000 G-Coin để đổi Voucher Giảm giá 20%?',
    confirmText: 'Đồng ý đổi',
    cancelText: 'Bỏ qua',
  },
};

export const DeleteConfirmation: Story = {
  args: {
    visible: true,
    title: 'Xóa mục này?',
    description: 'Bạn có chắc chắn muốn xóa mục này không?\nHành động này không thể hoàn tác.',
    confirmText: 'Xóa',
    cancelText: 'Giữ lại',
  },
};

export const LogoutConfirmation: Story = {
  args: {
    visible: true,
    title: 'Đăng xuất',
    description: 'Bạn có chắc chắn muốn đăng xuất khỏi tài khoản?',
    confirmText: 'Đăng xuất',
    cancelText: 'Ở lại',
  },
};

export const CancelBooking: Story = {
  args: {
    visible: true,
    title: 'Hủy đặt bàn?',
    description: 'Bạn có muốn hủy đặt bàn cho ngày 25/12/2024?\nViệc hủy có thể mất phí.',
    confirmText: 'Hủy đặt bàn',
    cancelText: 'Giữ lại',
  },
};

export const PointsInsufficient: Story = {
  args: {
    visible: true,
    title: 'Không đủ điểm',
    description: 'Bạn cần thêm 5.000 G-Coin để đổi ưu đãi này.\nBạn có muốn tích điểm thêm không?',
    confirmText: 'Tích điểm',
    cancelText: 'Để sau',
  },
};

export const AppUpdate: Story = {
  args: {
    visible: true,
    title: 'Cập nhật ứng dụng',
    description: 'Phiên bản mới đã có sẵn với nhiều tính năng hấp dẫn.\nBạn có muốn cập nhật ngay?',
    confirmText: 'Cập nhật',
    cancelText: 'Để sau',
  },
};

export const LocationPermission: Story = {
  args: {
    visible: true,
    title: 'Quyền truy cập vị trí',
    description:
      'Ứng dụng cần quyền truy cập vị trí để tìm nhà hàng gần bạn.\nBạn có đồng ý không?',
    confirmText: 'Cho phép',
    cancelText: 'Từ chối',
  },
};

export const Interactive: Story = {
  render: (args) => <InteractiveWrapper {...args} />,
  args: {
    title: 'Xác nhận hành động',
    description: 'Bạn có chắc chắn muốn thực hiện hành động này?',
  },
};
