// Libraries
import { Input, InputProps } from "@antscorp/ama-ui";
import clsx from "clsx";
import { ChevronDownIcon } from "lucide-react";
import React from "react";
import styled from "styled-components";

interface InputPickerProps extends InputProps {}

const InputPickerWrapper = styled.div`
  input {
    padding-right: 1.5rem;
  }
`;

export const InputPicker: React.FC<InputPickerProps> = ({
  disabled,
  onClick,
  ...props
}) => {
  return (
    <InputPickerWrapper
      className={clsx("relative flex items-center", {
        "pointer-events-none": disabled,
      })}
      onClick={onClick}
    >
      <Input {...props} disabled={disabled} />
      <div className="absolute right-0.5">
        <ChevronDownIcon
          size={16}
          color="var(--adm-color-light)"
          className={clsx({
            "opacity-50": disabled,
          })}
        />
      </div>
    </InputPickerWrapper>
  );
};
