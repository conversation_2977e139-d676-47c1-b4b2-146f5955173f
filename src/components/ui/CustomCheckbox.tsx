// Libraries
import clsx from "clsx";
import React from "react";
import styled from "styled-components";

// Components
import { Text } from "zmp-ui";

interface CustomCheckboxProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

// Styled
const CustomCheckboxWrapper = styled.div``;

const CustomCheckboxContainer = styled.div`
  &.custom-checkbox-container {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    cursor: pointer;
  }

  .custom-checkbox {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
  }

  .custom-checkbox-label {
    display: inline-block;
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    background-color: transparent;
    border: 2px solid rgba(255, 255, 255, 0.75);
    border-radius: 100%;
    transition: all 0.3s ease;
    position: relative;
  }

  .custom-checkbox-label:hover {
    border-color: rgba(255, 255, 255, 1);
  }

  .custom-checkbox-label::after {
    content: "";
    position: absolute;
    top: -6px;
    left: 7px;
    width: 12px;
    height: 22px;
    border: solid #fff200;
    border-width: 0 3px 3px 0;
    /* border-width: 5px; */
    transform: rotate(45deg);
    opacity: 0; /* Ẩn mặc định */
    transition: opacity 0.2s ease-in-out;
  }

  .custom-checkbox:checked + .custom-checkbox-label::after {
    opacity: 1;
  }

  .checkbox-content {
    color: #ffffff !important;
    font-weight: 500;
    font-size: 11px;
  }
`;

export const CustomCheckbox: React.FC<CustomCheckboxProps> = (props) => {
  const { children, className, ...restProps } = props;

  return (
    <CustomCheckboxContainer
      className={clsx("custom-checkbox-container", className)}
    >
      <input
        type="checkbox"
        id="custom-checkbox"
        className="custom-checkbox"
        {...restProps}
      />
      <label htmlFor="custom-checkbox" className="custom-checkbox-label" />
      <Text className="checkbox-content">{children}</Text>
    </CustomCheckboxContainer>
  );
};
