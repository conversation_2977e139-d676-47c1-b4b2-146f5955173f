import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Map } from './Map';

const meta: Meta<typeof Map> = {
  title: 'UI/Map',
  component: Map,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    center: {
      control: 'object',
      description: 'Center coordinates of the map',
    },
    zoom: {
      control: { type: 'range', min: 1, max: 20, step: 1 },
      description: 'Zoom level of the map',
    },
    height: {
      control: 'text',
      description: 'Height of the map container',
    },
    width: {
      control: 'text',
      description: 'Width of the map container',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
    onMapLoad: {
      action: 'map loaded',
      description: 'Callback when map is loaded',
    },
    onMarkerClick: {
      action: 'marker clicked',
      description: 'Callback when marker is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Map>;

export const Default: Story = {
  args: {
    center: {
      lat: 10.8231,
      lng: 106.6297,
    },
    zoom: 15,
    height: '400px',
    width: '100%',
    className: '',
  },
};

export const WithMarkers: Story = {
  args: {
    center: {
      lat: 10.8231,
      lng: 106.6297,
    },
    zoom: 15,
    height: '400px',
    width: '100%',
    className: '',
    markers: [
      {
        position: { lat: 10.8231, lng: 106.6297 },
        title: 'Ho Chi Minh City',
        infoWindow: 'Welcome to Ho Chi Minh City!',
      },
      {
        position: { lat: 10.8331, lng: 106.6397 },
        title: 'Another Location',
        infoWindow: 'This is another interesting location',
      },
    ],
  },
};

export const HighZoom: Story = {
  args: {
    center: {
      lat: 10.8231,
      lng: 106.6297,
    },
    zoom: 18,
    height: '400px',
    width: '100%',
    className: '',
    markers: [
      {
        position: { lat: 10.8231, lng: 106.6297 },
        title: 'Detailed View',
        infoWindow: 'High zoom level for detailed view',
      },
    ],
  },
};

export const LowZoom: Story = {
  args: {
    center: {
      lat: 10.8231,
      lng: 106.6297,
    },
    zoom: 8,
    height: '400px',
    width: '100%',
    className: '',
    markers: [
      {
        position: { lat: 10.8231, lng: 106.6297 },
        title: 'Wide View',
        infoWindow: 'Low zoom level for wide view',
      },
    ],
  },
};

export const CustomSize: Story = {
  args: {
    center: {
      lat: 10.8231,
      lng: 106.6297,
    },
    zoom: 15,
    height: '300px',
    width: '80%',
    className: 'mx-auto',
    markers: [
      {
        position: { lat: 10.8231, lng: 106.6297 },
        title: 'Custom Size',
        infoWindow: 'This map has custom dimensions',
      },
    ],
  },
};
