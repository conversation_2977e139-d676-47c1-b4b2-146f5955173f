import React, { useCallback, useMemo } from 'react';
import { GoogleMap, LoadScript, Marker, InfoWindow } from '@react-google-maps/api';
import { APP_CONFIG } from 'constant/appConfig';

export interface MapProps {
  center: {
    lat: number;
    lng: number;
  };
  zoom?: number;
  className?: string;
  style?: React.CSSProperties;
  options?: google.maps.MapOptions;
  onMapLoad?: (map: google.maps.Map) => void;
  onMarkerClick?: (marker: {
    position: { lat: number; lng: number };
    title?: string;
    infoWindow?: string;
  }) => void;
  markers?: Array<{
    position: { lat: number; lng: number };
    title?: string;
    infoWindow?: string;
  }>;
  height?: string;
  width?: string;
}

const mapContainerStyle = {
  width: '100%',
  height: '100%',
};

const defaultOptions: google.maps.MapOptions = {
  disableDefaultUI: false,
  zoomControl: true,
  mapTypeControl: false,
  scaleControl: false,
  cameraControl: false,
  streetViewControl: false,
  rotateControl: false,
  fullscreenControl: false,
  gestureHandling: 'none',
  draggable: false,
  clickableIcons: false,
  disableDoubleClickZoom: false,
  keyboardShortcuts: false,
};

export const Map: React.FC<MapProps> = ({
  center,
  zoom = 15,
  className = '',
  style,
  onMapLoad,
  onMarkerClick,
  markers = [],
  height = '400px',
  width = '100%',
  options = {},
}) => {
  const apiKey = APP_CONFIG.GOOGLE_MAP_API_KEY;

  const [selectedMarker, setSelectedMarker] = React.useState<number | null>(null);

  const mapRef = React.useRef<google.maps.Map | null>(null);

  const onMapLoadCallback = useCallback(
    (map: google.maps.Map) => {
      mapRef.current = map;

      if (onMapLoad) {
        onMapLoad(map);
      }
    },
    [onMapLoad],
  );

  const handleMarkerClick = useCallback(
    (
      marker: {
        position: { lat: number; lng: number };
        title?: string;
        infoWindow?: string;
      },
      index: number,
    ) => {
      setSelectedMarker(selectedMarker === index ? null : index);

      if (onMarkerClick) {
        onMarkerClick(marker);
      }
    },
    [onMarkerClick, selectedMarker],
  );

  const containerStyle = useMemo(
    () => ({
      ...mapContainerStyle,
      width,
      height,
      ...style,
    }),
    [width, height, style],
  );

  if (!apiKey) {
    return (
      <div
        className={`bg-gray-100 flex items-center justify-center ${className}`}
        style={containerStyle}
      >
        <p className="text-gray-500">Google Maps API key is required</p>
      </div>
    );
  }

  return (
    <div className={className} style={{ width, height }}>
      <LoadScript googleMapsApiKey={apiKey}>
        <GoogleMap
          mapContainerStyle={containerStyle}
          center={center}
          zoom={zoom}
          onLoad={onMapLoadCallback}
          options={{ ...defaultOptions, ...options }}
        >
          {markers.map((marker, index) => (
            <React.Fragment key={index}>
              <Marker
                position={marker.position}
                title={marker.title}
                onClick={() => handleMarkerClick(marker, index)}
              />

              {selectedMarker === index && marker.infoWindow && (
                <InfoWindow position={marker.position} onCloseClick={() => setSelectedMarker(null)}>
                  <div className="p-2">
                    <p className="text-sm font-medium">{marker.infoWindow}</p>
                  </div>
                </InfoWindow>
              )}
            </React.Fragment>
          ))}
        </GoogleMap>
      </LoadScript>
    </div>
  );
};

export default Map;
