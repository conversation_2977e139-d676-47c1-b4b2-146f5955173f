import { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta = {
  title: 'ui/Button',
  component: Button,
} satisfies Meta<typeof Button>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Đổi ngay',
  },
};

export const Small: Story = {
  args: {
    size: 'small',
    children: '1 người',
  },
};

export const Large: Story = {
  args: {
    size: 'large',
    children: 'Đặt bàn ngay',
  },
};

export const Disabled: Story = {
  args: {
    children: 'Không thể nhấn',
    disabled: true,
  },
};

export const DisabledSmall: Story = {
  args: {
    size: 'small',
    children: 'Hết voucher',
    disabled: true,
  },
};

export const DisabledLarge: Story = {
  args: {
    size: 'large',
    children: 'Hết chỗ trống',
    disabled: true,
  },
};

export const ColorVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Button color="primary">Primary Color</Button>
      <Button color="default">Default Color</Button>
      <Button color="success">Success Color</Button>
      <Button color="warning">Warning Color</Button>
      <Button color="danger">Danger Color</Button>
    </div>
  ),
};

export const ShapeVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Button shape="default">Default Shape</Button>
      <Button shape="rounded">Rounded Shape</Button>
      <Button shape="rectangular">Rectangular Shape</Button>
    </div>
  ),
};

export const TypeVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Button type="button">Button Type</Button>
      <Button type="submit">Submit Type</Button>
      <Button type="reset">Reset Type</Button>
    </div>
  ),
};

export const Interactive: Story = {
  args: {
    children: 'Nhấn vào đây',
    onClick: () => alert('Bạn đã nhấn nút!'),
  },
};

export const AllSizes: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', alignItems: 'flex-start' }}>
      <Button size="small">Nút nhỏ</Button>
      <Button size="middle">Nút vừa</Button>
      <Button size="large">Nút lớn</Button>
    </div>
  ),
};

export const LongText: Story = {
  args: {
    children: 'Đây là một nút có văn bản rất dài để kiểm tra hiển thị',
    style: { maxWidth: '200px' },
  },
};

export const RestaurantActions: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <Button size="large" color="primary">Đặt bàn ngay</Button>
      <Button size="middle" color="success">Đổi voucher</Button>
      <Button size="middle" color="warning">Xem menu</Button>
      <Button size="small" color="default">Thêm vào yêu thích</Button>
      <Button size="small" color="danger" disabled>Hết chỗ</Button>
    </div>
  ),
};
