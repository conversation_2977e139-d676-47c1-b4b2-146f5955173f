import { Button as AMAButton, ButtonProps as AMAButtonProps } from '@antscorp/ama-ui';
import styled from 'styled-components';

type ButtonProps = Pick<
  AMAButtonProps,
  'onClick' | 'className' | 'children' | 'type' | 'disabled' | 'shape' | 'color' | 'style'
> & {
  size?: 'large' | 'middle' | 'small';
};

const StyledButton = styled(AMAButton)`
  &.adm-button {
    --text-color: var(--color-text-primary);

    font-size: 15px;
    line-height: 22px;
    padding-top: 8px;
    padding-bottom: 8px;

    &.adm-button-large {
      font-size: 16px;
      line-height: 24px;
      padding-top: 11px;
      padding-bottom: 11px;
    }

    &.adm-button-small {
      font-size: 14px;
      line-height: 20px;
      padding-top: 5px;
      padding-bottom: 5px;
    }

    &.adm-button-disabled {
      --text-color: var(--color-text-light);
      --color: var(--color-background-disabled);

      opacity: 1;
    }
  }
`;

export const Button = (props: ButtonProps) => (
  <StyledButton color="primary" shape="rounded" {...props} />
);
