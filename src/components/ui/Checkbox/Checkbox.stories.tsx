import { <PERSON>a, StoryObj } from '@storybook/react';
import { Space } from '@antscorp/ama-ui';
import { Checkbox } from './Checkbox';

const meta = {
  title: 'ui/Checkbox',
  component: Checkbox,
  argTypes: {
    checked: {
      control: 'boolean',
    },
    disabled: {
      control: 'boolean',
    },
    indeterminate: {
      control: 'boolean',
    },
  },
} satisfies Meta<typeof Checkbox>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <span>
        Bằng việc tiếp tục, bạn xác nhận đã đọc và đồng ý với{' '}
        <a href="/" style={{ color: 'var(--color-main-primary)' }}>
          Điều khoản sử dụng
        </a>
      </span>
    ),
  },
};

export const Checked: Story = {
  args: {
    children: 'Selected option',
    checked: true,
  },
};

export const Disabled: Story = {
  args: {
    children: 'Disabled option',
    disabled: true,
  },
};

export const DisabledChecked: Story = {
  args: {
    children: 'Disabled selected option',
    disabled: true,
    checked: true,
  },
};

export const Indeterminate: Story = {
  args: {
    children: 'Indeterminate state',
    indeterminate: true,
  },
};

export const WithValue: Story = {
  args: {
    children: 'Option with value',
    value: 'option1',
  },
};

export const CheckboxGroupBasic = () => (
  <Checkbox.Group defaultValue={['coffee']}>
    <Space direction="vertical">
      <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>
        Chọn đồ uống yêu thích (nhiều lựa chọn):
      </h3>
      <Checkbox value="coffee">Cà phê</Checkbox>
      <Checkbox value="tea">Trà</Checkbox>
      <Checkbox value="juice">Nước ép</Checkbox>
      <Checkbox value="smoothie">Sinh tố</Checkbox>
    </Space>
  </Checkbox.Group>
);

export const CheckboxGroupToppings = () => (
  <Checkbox.Group defaultValue={['whip', 'syrup']}>
    <Space direction="vertical">
      <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>Chọn topping thêm:</h3>
      <Checkbox value="whip" style={{ '--icon-size': '24px' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '250px',
          }}
        >
          <span>Kem tươi</span>
          <span style={{ fontWeight: '600', color: '#D4A574' }}>+10.000đ</span>
        </div>
      </Checkbox>
      <Checkbox value="syrup" style={{ '--icon-size': '24px' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '250px',
          }}
        >
          <span>Syrup vani</span>
          <span style={{ fontWeight: '600', color: '#D4A574' }}>+5.000đ</span>
        </div>
      </Checkbox>
      <Checkbox value="extra-shot" style={{ '--icon-size': '24px' }} disabled>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '250px',
          }}
        >
          <span>Extra shot (hết hàng)</span>
          <span style={{ fontWeight: '600', color: '#D4A574' }}>+15.000đ</span>
        </div>
      </Checkbox>
      <Checkbox value="ice" style={{ '--icon-size': '24px' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '250px',
          }}
        >
          <span>Đá</span>
          <span style={{ fontWeight: '600', color: '#666' }}>Miễn phí</span>
        </div>
      </Checkbox>
    </Space>

    <h3 style={{ marginTop: 20, fontSize: '16px', fontWeight: '600' }}>Horizontal layout:</h3>
    <Space direction="horizontal" style={{ '--gap': '20px' }}>
      <Checkbox value="sugar">Đường</Checkbox>
      <Checkbox value="milk">Sữa</Checkbox>
      <Checkbox value="hot">Nóng</Checkbox>
    </Space>
  </Checkbox.Group>
);
