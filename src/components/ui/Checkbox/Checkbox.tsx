import { Checkbox as AMACheckbox, CheckboxProps as AMACheckboxProps } from '@antscorp/ama-ui';
import styled from 'styled-components';

type CheckboxProps = Pick<
  AMACheckboxProps,
  | 'onChange'
  | 'className'
  | 'children'
  | 'disabled'
  | 'checked'
  | 'defaultChecked'
  | 'style'
  | 'value'
  | 'indeterminate'
>;

const StyledCheckbox = styled(AMACheckbox)`
  &.adm-checkbox {
    --icon-size: 20px;
    --color: var(--color-main-primary);
    --border-color: var(--color-grey-secondary);
    --text-color: var(--color-text-primary);
    align-items: flex-start;

    .adm-checkbox-content {
      color: var(--text-color);
      font-size: 15px;
      line-height: 22px;
    }

    .adm-checkbox-icon {
      display: inline-flex;
      border-width: 1.5px;
      border-color: var(--border-color);
      border-radius: 4px;
    }

    &.adm-checkbox-checked .adm-checkbox-icon {
      background-color: var(--color-green-primary);
      border-color: var(--color-green-primary);
    }

    &.adm-checkbox-indeterminate .adm-checkbox-icon {
      background-color: var(--color-green-primary);
      border-color: var(--color-green-primary);
    }

    &.adm-checkbox-disabled {
      .adm-checkbox-content {
        color: var(--color-text-light);
      }

      .adm-checkbox-icon {
        border-color: var(--color-grey-secondary);
        background-color: var(--color-disabled);

        &.adm-checkbox-icon-checked {
          background-color: var(--color-background-disabled);
        }
      }
    }
  }
`;

export const Checkbox = (props: CheckboxProps) => <StyledCheckbox {...props} />;

Checkbox.Group = AMACheckbox.Group;
