// Libraries
import clsx from "clsx";
import React from "react";
import styled from "styled-components";

interface DotLoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  color?: string;
  children?: React.ReactNode;
  visible?: boolean;
}

// Styled
const StyledDotLoading = styled.div<{ $color?: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: ${({ $color }) => $color || "var(--color-main-primary)"};

  span {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: ${({ $color }) => $color || "var(--color-main-primary)"};
    animation: dot-blink 1.5s infinite ease-in-out;
  }

  span:nth-child(2) {
    animation-delay: 0.2s;
  }

  span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes dot-blink {
    0%,
    80%,
    100% {
      transform: scale(0);
      opacity: 0.3;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

export const DotLoading: React.FC<DotLoadingProps> = (props) => {
  const {
    color,
    children = "Loading",
    visible = true,
    className,
    ...restOfProps
  } = props;

  return (
    <StyledDotLoading
      {...restOfProps}
      $color={color}
      className={clsx(className, "font-bold", {
        "!hidden": !visible,
      })}
    >
      {children}
      <span />
      <span />
      <span />
    </StyledDotLoading>
  );
};
