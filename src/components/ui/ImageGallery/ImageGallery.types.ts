export interface ImageItem {
  /** Unique identifier for the image */
  id: string | number;
  /** Image source URL */
  src: string;
  /** Alt text for accessibility */
  alt: string;
  /** Optional title for the image */
  title?: string;
  /** Optional description for the image */
  description?: string;
  /** Optional custom class name */
  className?: string;
}

export interface ImageGalleryProps {
  /** Array of images to display in the gallery */
  images: ImageItem[];
  /** Optional custom class name for the gallery container */
  className?: string;
  /** Optional callback when an image is clicked */
  onImageClick?: (image: ImageItem, index: number) => void;
  /** Whether images should be lazy loaded */
  lazyLoad?: boolean;
  /** Custom gap between images in pixels */
  gap?: number;
  /** ARIA label for the gallery */
  ariaLabel?: string;
}

export interface ImageItemProps {
  /** Image data */
  image: ImageItem;
  /** Index of the image in the gallery */
  index: number;
  /** Whether this is a hero (full-width) image */
  isHero: boolean;
  /** Click handler */
  onClick?: (image: ImageItem, index: number) => void;
  /** Whether to lazy load the image */
  lazyLoad?: boolean;
}