import { Meta, StoryObj } from '@storybook/react';
import { ImageGallery } from './ImageGallery';
import { ImageItem } from './ImageGallery.types';

// Sample image data for stories
const sampleImages: ImageItem[] = [
  {
    id: 1,
    src: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=375&h=200&fit=crop&crop=center',
    alt: 'Golden Gate Restaurant Interior',
    title: 'Elegant dining space with warm lighting',
    description: 'Modern restaurant interior with comfortable seating',
  },
  {
    id: 2,
    src: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=185&h=185&fit=crop&crop=center',
    alt: 'Grilled seafood platter',
    title: 'Fresh grilled seafood',
    description: 'Delicious grilled prawns and fish',
  },
  {
    id: 3,
    src: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=185&h=185&fit=crop&crop=center',
    alt: 'Vietnamese hot pot',
    title: 'Traditional hot pot',
    description: 'Authentic Vietnamese hot pot experience',
  },
  {
    id: 4,
    src: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=375&h=200&fit=crop&crop=center',
    alt: 'Restaurant exterior view',
    title: 'Golden Gate Restaurant facade',
    description: 'Beautiful restaurant exterior with outdoor seating',
  },
  {
    id: 5,
    src: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=185&h=185&fit=crop&crop=center',
    alt: 'Beef BBQ dish',
    title: 'Premium BBQ beef',
    description: 'Tender grilled beef with vegetables',
  },
  {
    id: 6,
    src: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=185&h=185&fit=crop&crop=center',
    alt: 'Asian fusion dessert',
    title: 'Signature dessert',
    description: 'Creative Asian-inspired dessert presentation',
  },
  {
    id: 7,
    src: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=375&h=200&fit=crop&crop=center',
    alt: 'Private dining room',
    title: 'VIP dining experience',
    description: 'Exclusive private dining room for special occasions',
  },
  {
    id: 8,
    src: 'https://images.unsplash.com/photo-1562967914-608f82629710?w=185&h=185&fit=crop&crop=center',
    alt: 'Coffee and pastries',
    title: 'Coffee corner',
    description: 'Artisan coffee with fresh pastries',
  },
];

// Smaller dataset for specific tests
const smallImageSet: ImageItem[] = sampleImages.slice(0, 3);
const largeImageSet: ImageItem[] = [
  ...sampleImages,
  ...sampleImages.map((img) => ({ ...img, id: `${img.id}-100` })),
];

const meta = {
  title: 'ui/ImageGallery',
  component: ImageGallery,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
The ImageGallery component provides a dynamic masonry layout for displaying images in a Golden Gate Mini App.

**Features:**
- Masonry layout with alternating hero and grid images
- Accessibility support with ARIA labels
- Lazy loading for performance
- Responsive design optimized for mobile
- Customizable gap spacing
- Click handlers for image interactions

**Layout Pattern:**
- Hero images: 375px × 200px (full width)
- Grid images: 185.5px × 185px (half width pairs)
- Pattern: [Hero] → [Square][Square] → [Hero] → [Square][Square]
        `,
      },
    },
  },
  argTypes: {
    images: {
      description: 'Array of image objects to display',
      control: { type: 'object' },
    },
    onImageClick: {
      description: 'Callback when an image is clicked',
      action: 'imageClicked',
    },
    lazyLoad: {
      description: 'Whether images should be lazy loaded',
      control: { type: 'boolean' },
    },
    gap: {
      description: 'Gap between images in pixels',
      control: { type: 'number', min: 0, max: 32, step: 2 },
    },
    ariaLabel: {
      description: 'ARIA label for the gallery',
      control: { type: 'text' },
    },
  },
} satisfies Meta<typeof ImageGallery>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    images: sampleImages,
    lazyLoad: true,
    gap: 4,
    ariaLabel: 'Golden Gate restaurant image gallery',
  },
};

export const SmallGallery: Story = {
  args: {
    images: smallImageSet,
    lazyLoad: false,
    gap: 4,
    ariaLabel: 'Small image gallery with 3 images',
  },
};

export const LargeGallery: Story = {
  args: {
    images: largeImageSet,
    lazyLoad: true,
    gap: 4,
    ariaLabel: 'Large gallery with many images',
  },
};

export const CustomGap = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: '600' }}>Gap: 0px</h3>
        <ImageGallery images={smallImageSet} gap={0} />
      </div>
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: '600' }}>Gap: 8px</h3>
        <ImageGallery images={smallImageSet} gap={8} />
      </div>
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: '600' }}>Gap: 16px</h3>
        <ImageGallery images={smallImageSet} gap={16} />
      </div>
    </div>
  ),
};

export const WithInteractions: Story = {
  args: {
    images: sampleImages,
    lazyLoad: false,
    gap: 4,
    onImageClick: (image, index) => {
      alert(`Clicked on image: ${image.title || image.alt} at index ${index}`);
    },
  },
};

export const EmptyState: Story = {
  args: {
    images: [],
    gap: 4,
    ariaLabel: 'Empty gallery',
  },
};

export const AccessibilityDemo = {
  render: () => (
    <div>
      <p style={{ marginBottom: '16px', fontSize: '14px', color: 'var(--color-text-secondary)' }}>
        This gallery includes full accessibility support:
        <br />• Images are focusable with keyboard navigation
        <br />• ARIA labels describe each image
        <br />• Alt text provides image descriptions
        <br />• Keyboard activation with Enter or Space
        <br />• Screen reader friendly structure
      </p>
      <ImageGallery
        images={smallImageSet}
        gap={4}
        ariaLabel="Accessible image gallery demonstration"
      />
    </div>
  ),
};

export const MasonryPattern = {
  render: () => (
    <div>
      <p style={{ marginBottom: '16px', fontSize: '14px', color: 'var(--color-text-secondary)' }}>
        The masonry layout follows this pattern:
        <br />• Image 1, 4, 7, etc.: Hero images (full width, 375×200px)
        <br />• Images 2-3, 5-6, 8-9, etc.: Grid pairs (half width, 185×185px)
        <br />• 4px gap between all images
        <br />• Responsive design optimized for mobile screens
      </p>
      <ImageGallery images={sampleImages} gap={4} ariaLabel="Masonry pattern demonstration" />
    </div>
  ),
};


export const ResponsiveDemo = {
  render: () => (
    <div>
      <p style={{ marginBottom: '16px', fontSize: '14px', color: 'var(--color-text-secondary)' }}>
        Resize your browser to see how the gallery adapts to different screen sizes. The component
        maintains proper proportions across devices.
      </p>
      <ImageGallery images={sampleImages} gap={4} ariaLabel="Responsive design demonstration" />
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

export const GoldenGateBranding = {
  render: () => (
    <div>
      <h3
        style={{
          marginBottom: '16px',
          fontSize: '18px',
          fontWeight: '600',
          color: 'var(--color-primary)',
          fontFamily: 'var(--font-family, inherit)',
        }}
      >
        Golden Gate Restaurant Gallery
      </h3>
      <p style={{ marginBottom: '16px', fontSize: '14px', color: 'var(--color-text-secondary)' }}>
        Showcasing Golden Gate Group&apos;s culinary experiences across Vietnam&apos;s 40+ brands
        and 500+ restaurants.
      </p>
      <ImageGallery
        images={sampleImages}
        gap={4}
        ariaLabel="Golden Gate Group restaurant showcase"
        onImageClick={(image, index) => {
          console.log(`Viewing ${image.title} - Golden Gate experience #${index + 1}`);
        }}
      />
    </div>
  ),
};

