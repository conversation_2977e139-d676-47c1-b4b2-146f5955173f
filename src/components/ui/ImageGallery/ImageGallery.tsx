import React, { useCallback, useMemo } from 'react';
import styled from 'styled-components';
import { ImageGalleryProps, ImageItemProps } from './ImageGallery.types';
import { Image } from '../Image';

// Individual image component
const ImageItemComponent: React.FC<ImageItemProps> = ({
  image,
  index,
  isHero,
  onClick,
  lazyLoad = true,
}) => {
  const handleClick = useCallback(() => {
    if (onClick) {
      onClick(image, index);
    }
  }, [image, index, onClick]);

  return (
    <StyledImageItem
      isHero={isHero}
      onClick={handleClick}
      className={`image-gallery-item ${image.className || ''}`}
      role="button"
      tabIndex={0}
      aria-label={`View image: ${image.alt}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      }}
    >
      <Image
        src={image.src}
        alt={image.alt}
        lazy={lazyLoad}
        fit="cover"
      />
    </StyledImageItem>
  );
};

// Main ImageGallery component
export const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  className,
  lazyLoad = true,
  gap = 4,
  ariaLabel = 'Image gallery',
  onImageClick,
}) => {
  // Memoize the layout pattern calculation
  const layoutPattern = useMemo(() => {
    return images.map((_, index) => {
      // Pattern: Hero → Grid pair → Hero → Grid pair
      // Index 0: Hero, Index 1-2: Grid pair, Index 3: Hero, Index 4-5: Grid pair, etc.
      const groupIndex = Math.floor(index / 3);
      const positionInGroup = index % 3;

      return {
        isHero: positionInGroup === 0,
        groupIndex,
        positionInGroup,
      };
    });
  }, [images]);

  if (!images || images.length === 0) {
    return <EmptyState>No images to display</EmptyState>;
  }

  return (
    <StyledImageGallery
      className={`image-gallery ${className || ''}`}
      gap={gap}
      role="region"
      aria-label={ariaLabel}
    >
      {images.map((image, index) => {
        const pattern = layoutPattern[index];

        return (
          <ImageItemComponent
            key={image.id}
            image={image}
            index={index}
            isHero={pattern.isHero}
            onClick={onImageClick}
            lazyLoad={lazyLoad}
          />
        );
      })}
    </StyledImageGallery>
  );
};

// Styled Components
const StyledImageGallery = styled.div<{ gap: number }>`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${(props) => props.gap}px;
  width: 100%;

  /* Hero images span both columns */
  .image-gallery-item:nth-child(3n + 1) {
    grid-column: span 2;
  }
`;

const StyledImageItem = styled.div<{ isHero: boolean }>`
  width: 100%;
  height: ${(props) => (props.isHero ? '200px' : '185px')};
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: scale(1.02);
  }

  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  &:focus:not(:focus-visible) {
    outline: none;
  }

  .adm-image {
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
  }
`;


const EmptyState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
  color: var(--color-text-secondary);
  font-size: 14px;
  text-align: center;
  background-color: var(--color-background);
  border-radius: 8px;
  border: 1px dashed var(--color-border);
`;

