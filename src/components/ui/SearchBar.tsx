// Libraries
import React from "react";
import styled from "styled-components";

// Components
import {
  SearchBar as AMASearchBar,
  type SearchBarProps as AMASearchBarProps,
} from "@antscorp/ama-ui";

export interface SearchBarProps extends AMASearchBarProps {}

const StyledSearchBar = styled(AMASearchBar)`
  --background: #fff;
  --height: 36px;
  --border-radius: 8px;

  .adm-input-element {
    font-size: 12px;
  }

  .adm-search-bar-input-box .adm-search-bar-input-box-icon {
    color: var(--color-text-primary);
  }

  .adm-search-bar-input {
    margin-left: 10px;
  }

  &:not(.adm-search-bar-active) {
    .adm-search-bar-input-box {
      border-color: var(--color-border-light);
    }
  }
`;

export const SearchBar: React.FC<SearchBarProps> = (props) => {
  const { placeholder = "Tìm kiếm...", ...restProps } = props;

  return <StyledSearchBar {...restProps} placeholder={placeholder} />;
};
