// Libraries
import React from "react";
import { Tag as AMATag, type TagProps as AMATagProps } from "@antscorp/ama-ui";
import styled from "styled-components";
import { respond } from "utils";

interface TagProps extends AMATagProps {}

const StyledTag = styled(AMATag)<{ $color?: AMATagProps["color"] }>`
  --adm-color-warning: #fff6c5;

  ${({ $color }) => $color === "warning" && `--text-color: #9D6E26 !important;`}

  padding: 2px 10px;

  ${respond.xs`
    font-size: 12px;
  `}
`;

export const Tag: React.FC<TagProps> = (props) => {
  const { color, ...restProps } = props;

  return <StyledTag {...restProps} $color={color} color={color} />;
};
