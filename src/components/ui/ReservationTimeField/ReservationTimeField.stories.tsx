import type { Meta, StoryObj } from '@storybook/react';
import { ReservationTimeField } from './ReservationTimeField';

const meta: Meta<typeof ReservationTimeField> = {
  title: 'Components/UI/ReservationTimeField',
  component: ReservationTimeField,
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    value: new Date(2024, 0, 15, 18, 30), // Jan 15, 2024 at 6:30 PM
  },
};