import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { CalendarIcon, ClockIcon } from 'components/icons';
import { formatVietnameseDate, formatTime } from 'utils/dateTime';
import { DateTimePicker, DateTimeValue } from '../DateTimePicker/DateTimePicker';

// Interface theo chuẩn Ant Design Mobile custom field
export interface ReservationTimeFieldProps {
  value?: Date;
  onChange?: (value: Date) => void;
}

export const ReservationTimeField: React.FC<ReservationTimeFieldProps> = ({ value, onChange }) => {
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);

  // Default value if not provided
  const currentValue = value || new Date();

  const handleDateTimeClick = () => {
    setShowDateTimePicker(true);
  };

  const handleDateTimePickerClose = (selectedValue?: DateTimeValue) => {
    setShowDateTimePicker(false);
    if (selectedValue?.date) {
      onChange?.(selectedValue.date);
    }
  };

  return (
    <>
      <div className="flex justify-between gap-2">
        <button
          type="button"
          className="flex min-h-12 flex-1 cursor-pointer items-center gap-2.5 rounded-md border border-gray-800 bg-white px-4 py-3"
          onClick={handleDateTimeClick}
        >
          <CalendarIcon size={24} />
          <span className="flex-1 font-roboto text-[15px] font-medium leading-[22px] text-black">
            {formatVietnameseDate(currentValue)}
          </span>
          <ChevronDown size={24} />
        </button>

        <button
          type="button"
          className="flex min-h-12 flex-1 cursor-pointer items-center gap-2.5 rounded-md border border-NL800 bg-white px-4 py-3"
          onClick={handleDateTimeClick}
        >
          <ClockIcon size={24} />
          <span className="flex-1 font-roboto text-[15px] font-medium leading-[22px] text-black">
            {formatTime(currentValue)}
          </span>
          <ChevronDown size={24} />
        </button>
      </div>

      <DateTimePicker
        visible={showDateTimePicker}
        value={{ date: currentValue }}
        title="Chọn ngày, giờ đặt bàn"
        onClose={handleDateTimePickerClose}
      />
    </>
  );
};
