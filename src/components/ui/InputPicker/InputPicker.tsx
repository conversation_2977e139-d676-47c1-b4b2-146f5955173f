// Libraries
import { Calendar } from 'lucide-react';
import React, { memo, useCallback, useMemo } from 'react';
import { useImmer } from 'use-immer';

// Icons
import { Picker, PickerProps } from '@antscorp/ama-ui';
import { Input } from '../Input/Input';

// Types
import { PickerValue } from 'antd-mobile/es/components/picker-view';
import type { InputProps } from '../Input/Input';

export type InputPickerItem = {
  label: string;
  value: string | number;
  key?: string | number;
};

export interface InputPickerProps {
  pickerProps?: Omit<PickerProps, 'columns' | 'value' | 'defaultValue' | 'onChange'>;
  inputProps?: Omit<InputProps, 'readOnly' | 'defaultValue' | 'value' | 'onChange'>;
  value?: string | PickerValue[];
  defaultValue?: string | number | PickerValue[];
  onChange?: (value: string | PickerValue[]) => void;
  options?: InputPickerItem[];
  // Support for multiple columns
  columns?: InputPickerItem[][];
  // Custom formatter for multi-column display
  formatter?: (values: PickerValue[]) => string;
}

export const InputPicker: React.FC<InputPickerProps> = memo((props) => {
  const { value, defaultValue, options, columns, formatter, pickerProps, inputProps, onChange } =
    props;

  // Determine if we're in multi-column mode
  const isMultiColumn = Boolean(columns && columns.length > 0);

  // State
  const [state, setState] = useImmer({
    visible: false,
    internalValue: value || defaultValue,
  });

  // Variables
  const { internalValue, visible } = state;
  const displayValue = value !== undefined ? value : internalValue;

  // Memo for single column mode
  const currentOption = useMemo(() => {
    if (isMultiColumn) return null;
    return options?.find((option) => option.value === displayValue);
  }, [displayValue, options, isMultiColumn]);

  // Memo for multi-column mode
  const displayText = useMemo(() => {
    if (!isMultiColumn) {
      return currentOption?.label || '';
    }

    if (formatter && Array.isArray(displayValue)) {
      return formatter(displayValue);
    }

    if (Array.isArray(displayValue) && columns) {
      return displayValue
        .map((val, index) => {
          const column = columns[index];
          const item = column?.find((item) => item.value === val);
          return item?.label || '';
        })
        .join('/');
    }

    return '';
  }, [isMultiColumn, currentOption, formatter, displayValue, columns]);

  const pickerValue = useMemo(() => {
    if (isMultiColumn && Array.isArray(displayValue)) return displayValue;
    return [displayValue || ''] as PickerValue[];
  }, [isMultiColumn, displayValue]);

  // Handlers
  const toggleActionVisible = useCallback(() => {
    setState((draft) => {
      draft.visible = !draft.visible;
    });
  }, [setState]);

  const onConfirmValue = useCallback(
    (value: PickerValue[]) => {
      if (isMultiColumn) {
        setState((draft) => {
          draft.internalValue = value;
        });
        onChange?.(value);
      } else {
        setState((draft) => {
          draft.internalValue = value[0] as string;
        });
        onChange?.(value[0] as string);
      }
    },
    [isMultiColumn, onChange, setState],
  );

  return (
    <>
      <div onClick={toggleActionVisible}>
        <Input suffixIcon={<Calendar />} {...inputProps} readOnly value={displayText} />
      </div>

      <Picker
        {...pickerProps}
        columns={isMultiColumn ? columns || [] : [options || []]}
        visible={visible}
        onClose={() => {
          setState((draft) => {
            draft.visible = false;
          });
        }}
        value={pickerValue}
        onConfirm={onConfirmValue}
      />
    </>
  );
});

InputPicker.displayName = 'InputPicker';
