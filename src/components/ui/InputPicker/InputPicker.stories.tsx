import { <PERSON>a, StoryObj } from '@storybook/react';
import { Calendar, MapPin, Clock, Users, Star, Building, Phone } from 'lucide-react';
import { InputPicker, InputPickerItem } from './InputPicker';

const meta = {
  title: 'ui/InputPicker',
  component: InputPicker,
  parameters: {
    layout: 'padded',
  },
  argTypes: {
    onChange: { action: 'changed' },
  },
} satisfies Meta<typeof InputPicker>;

export default meta;

type Story = StoryObj<typeof meta>;

// Sample data
const dateOptions: InputPickerItem[] = [
  { label: '01/01/1990', value: '1990-01-01' },
  { label: '15/05/1995', value: '1995-05-15' },
  { label: '20/12/2000', value: '2000-12-20' },
  { label: '10/08/2005', value: '2005-08-10' },
];

const cityOptions: InputPickerItem[] = [
  { label: '<PERSON><PERSON>', value: 'hcm' },
  { label: '<PERSON><PERSON>', value: 'hanoi' },
  { label: 'Đà Nẵng', value: 'danang' },
  { label: '<PERSON><PERSON><PERSON>', value: 'cantho' },
  { label: 'H<PERSON><PERSON>ng', value: 'haiphong' },
];

const timeOptions: InputPickerItem[] = [
  { label: '9:00 AM', value: '09:00' },
  { label: '10:00 AM', value: '10:00' },
  { label: '11:00 AM', value: '11:00' },
  { label: '12:00 PM', value: '12:00' },
  { label: '1:00 PM', value: '13:00' },
  { label: '2:00 PM', value: '14:00' },
  { label: '3:00 PM', value: '15:00' },
];

const restaurantOptions: InputPickerItem[] = [
  { label: 'Highlands Coffee Nguyễn Huệ', value: 'highlands-nguyen-hue' },
  { label: 'Highlands Coffee Bitexco', value: 'highlands-bitexco' },
  { label: 'Highlands Coffee Landmark 81', value: 'highlands-landmark-81' },
  { label: 'Highlands Coffee Saigon Centre', value: 'highlands-saigon-centre' },
];

const peopleOptions: InputPickerItem[] = [
  { label: '1 người', value: 1 },
  { label: '2 người', value: 2 },
  { label: '3 người', value: 3 },
  { label: '4 người', value: 4 },
  { label: '5+ người', value: 5 },
];

const statusOptions: InputPickerItem[] = [
  { label: 'Hoạt động', value: 'active' },
  { label: 'Tạm ngưng', value: 'inactive' },
  { label: 'Chờ duyệt', value: 'pending' },
];

// Date picker options
const yearOptions: InputPickerItem[] = [
  { label: '1980', value: 1980 },
  { label: '1985', value: 1985 },
  { label: '1990', value: 1990 },
  { label: '1995', value: 1995 },
  { label: '2000', value: 2000 },
  { label: '2005', value: 2005 },
  { label: '2010', value: 2010 },
  { label: '2015', value: 2015 },
  { label: '2020', value: 2020 },
];

const monthOptions: InputPickerItem[] = [
  { label: 'Tháng 1', value: 1 },
  { label: 'Tháng 2', value: 2 },
  { label: 'Tháng 3', value: 3 },
  { label: 'Tháng 4', value: 4 },
  { label: 'Tháng 5', value: 5 },
  { label: 'Tháng 6', value: 6 },
  { label: 'Tháng 7', value: 7 },
  { label: 'Tháng 8', value: 8 },
  { label: 'Tháng 9', value: 9 },
  { label: 'Tháng 10', value: 10 },
  { label: 'Tháng 11', value: 11 },
  { label: 'Tháng 12', value: 12 },
];

const dayOptions: InputPickerItem[] = Array.from({ length: 31 }, (_, i) => ({
  label: `Ngày ${i + 1}`,
  value: i + 1,
}));

// Default Story (Date Picker)
export const Default: Story = {
  args: {
    options: dateOptions,
    defaultValue: '1995-05-15',
    inputProps: {
      label: 'Ngày sinh',
      placeholder: 'Chọn ngày sinh của bạn',
      required: true,
    },
    onChange: (value) => console.log('Default onChange:', value),
  },
};

// Custom Label and Placeholder
export const CustomInputProps: Story = {
  args: {
    options: cityOptions,
    defaultValue: 'hcm',
    inputProps: {
      label: 'Thành phố',
      placeholder: 'Chọn thành phố của bạn',
      required: true,
      suffixIcon: <MapPin size={16} />,
    },
    onChange: (value) => console.log('CustomInputProps onChange:', value),
  },
};

// Time Picker with Clock Icon
export const TimePicker: Story = {
  args: {
    options: timeOptions,
    defaultValue: '12:00',
    inputProps: {
      label: 'Thời gian',
      placeholder: 'Chọn thời gian',
      suffixIcon: <Clock size={16} />,
    },
    onChange: (value) => console.log('TimePicker onChange:', value),
  },
};

// Restaurant Selector with Building Icon
export const RestaurantPicker: Story = {
  args: {
    options: restaurantOptions,
    defaultValue: 'highlands-nguyen-hue',
    inputProps: {
      label: 'Cửa hàng',
      placeholder: 'Chọn cửa hàng',
      suffixIcon: <Building size={16} />,
      required: true,
    },
    onChange: (value) => console.log('RestaurantPicker onChange:', value),
  },
};

// People Counter with Users Icon
export const PeoplePicker: Story = {
  args: {
    options: peopleOptions,
    defaultValue: 2,
    inputProps: {
      label: 'Số lượng khách',
      placeholder: 'Chọn số lượng khách',
      suffixIcon: <Users size={16} />,
    },
    onChange: (value) => console.log('PeoplePicker onChange:', value),
  },
};

// Optional Field (No Required)
export const OptionalField: Story = {
  args: {
    options: cityOptions,
    inputProps: {
      label: 'Thành phố (Tùy chọn)',
      placeholder: 'Chọn thành phố nếu muốn',
      suffixIcon: <MapPin size={16} />,
      required: false,
    },
    onChange: (value) => console.log('OptionalField onChange:', value),
  },
};

// Status Picker with Different Styling
export const StatusPicker: Story = {
  args: {
    options: statusOptions,
    defaultValue: 'active',
    inputProps: {
      label: 'Trạng thái',
      placeholder: 'Chọn trạng thái',
      suffixIcon: <Star size={16} />,
      status: 'success',
    },
    onChange: (value) => console.log('StatusPicker onChange:', value),
  },
};

// Error State
export const ErrorState: Story = {
  args: {
    options: dateOptions,
    inputProps: {
      label: 'Ngày sinh',
      placeholder: 'Chọn ngày sinh',
      required: true,
      status: 'error',
    },
    onChange: (value) => console.log('ErrorState onChange:', value),
  },
};

// Warning State
export const WarningState: Story = {
  args: {
    options: timeOptions,
    inputProps: {
      label: 'Thời gian',
      placeholder: 'Chọn thời gian',
      suffixIcon: <Clock size={16} />,
      status: 'warning',
    },
    onChange: (value) => console.log('WarningState onChange:', value),
  },
};

// With Prefix Icon
export const WithPrefixIcon: Story = {
  args: {
    options: cityOptions,
    defaultValue: 'hcm',
    inputProps: {
      label: 'Địa chỉ liên hệ',
      placeholder: 'Chọn thành phố',
      prefixIcon: <Phone size={16} />,
      suffixIcon: <MapPin size={16} />,
    },
    onChange: (value) => console.log('WithPrefixIcon onChange:', value),
  },
};

// Custom Picker Props
export const CustomPickerProps: Story = {
  args: {
    options: timeOptions,
    defaultValue: '12:00',
    inputProps: {
      label: 'Thời gian đặt bàn',
      placeholder: 'Chọn thời gian',
      suffixIcon: <Clock size={16} />,
    },
    pickerProps: {
      title: 'Chọn thời gian phù hợp',
      confirmText: 'Xác nhận',
      cancelText: 'Hủy bỏ',
    },
    onChange: (value) => console.log('CustomPickerProps onChange:', value),
  },
};

// Multiple Selectors in Form
export const FormExample: Story = {
  render: () => (
    <div
      style={{
        maxWidth: '400px',
        padding: '24px',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        backgroundColor: '#ffffff',
      }}
    >
      <h3
        style={{
          marginBottom: '20px',
          color: '#1a1a1a',
          fontSize: '18px',
          fontWeight: '600',
        }}
      >
        Thông tin đặt bàn
      </h3>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <InputPicker
          options={restaurantOptions}
          defaultValue="highlands-nguyen-hue"
          inputProps={{
            label: 'Cửa hàng',
            placeholder: 'Chọn cửa hàng',
            suffixIcon: <Building size={16} />,
            required: true,
          }}
        />

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
          <InputPicker
            options={timeOptions}
            defaultValue="12:00"
            inputProps={{
              label: 'Giờ',
              placeholder: 'Chọn giờ',
              suffixIcon: <Clock size={16} />,
              required: true,
            }}
          />

          <InputPicker
            options={peopleOptions}
            defaultValue={2}
            inputProps={{
              label: 'Số khách',
              placeholder: 'Chọn số khách',
              suffixIcon: <Users size={16} />,
              required: true,
            }}
          />
        </div>

        <InputPicker
          options={cityOptions}
          inputProps={{
            label: 'Thành phố (Tùy chọn)',
            placeholder: 'Chọn thành phố',
            suffixIcon: <MapPin size={16} />,
            required: false,
          }}
        />
      </div>
    </div>
  ),
};

// Profile Form Example
export const ProfileForm: Story = {
  render: () => (
    <div
      style={{
        maxWidth: '500px',
        padding: '32px',
        border: '1px solid #e1e5e9',
        borderRadius: '16px',
        backgroundColor: '#fafbfc',
      }}
    >
      <h3
        style={{
          marginBottom: '24px',
          color: '#1a1a1a',
          fontSize: '20px',
          fontWeight: '700',
          textAlign: 'center',
        }}
      >
        Hồ sơ cá nhân
      </h3>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        <InputPicker
          options={dateOptions}
          defaultValue="1995-05-15"
          inputProps={{
            label: 'Ngày sinh',
            placeholder: 'Chọn ngày sinh của bạn',
            suffixIcon: <Calendar size={18} />,
            required: true,
          }}
        />

        <InputPicker
          options={cityOptions}
          defaultValue="hcm"
          inputProps={{
            label: 'Nơi ở hiện tại',
            placeholder: 'Chọn thành phố',
            suffixIcon: <MapPin size={18} />,
            required: true,
          }}
        />

        <InputPicker
          options={statusOptions}
          defaultValue="active"
          inputProps={{
            label: 'Trạng thái tài khoản',
            placeholder: 'Chọn trạng thái',
            suffixIcon: <Star size={18} />,
            status: 'success',
            required: true,
          }}
        />
      </div>
    </div>
  ),
};

// Interactive Example with Actions
export const InteractiveExample: Story = {
  args: {
    options: cityOptions,
    defaultValue: 'hcm',
    inputProps: {
      label: 'Thành phố yêu thích',
      placeholder: 'Chọn thành phố bạn yêu thích',
      suffixIcon: <MapPin size={16} />,
      required: true,
    },
    onChange: (value) => {
      const selectedOption = cityOptions.find((option) => option.value === value);
      console.log('Selected:', selectedOption);
      alert(`Bạn đã chọn: ${selectedOption?.label}`);
    },
  },
};

// Long Options List
export const LongOptionsList: Story = {
  args: {
    options: [
      ...cityOptions,
      { label: 'Vũng Tàu', value: 'vungtau' },
      { label: 'Nha Trang', value: 'nhatrang' },
      { label: 'Đà Lạt', value: 'dalat' },
      { label: 'Phan Thiết', value: 'phanthiet' },
      { label: 'Quy Nhơn', value: 'quynhon' },
      { label: 'Huế', value: 'hue' },
      { label: 'Biên Hòa', value: 'bienhoa' },
      { label: 'Long Xuyên', value: 'longxuyen' },
      { label: 'Rạch Giá', value: 'rachgia' },
      { label: 'Cà Mau', value: 'camau' },
    ],
    defaultValue: 'hcm',
    inputProps: {
      label: 'Tất cả tỉnh thành',
      placeholder: 'Chọn tỉnh/thành phố',
      suffixIcon: <MapPin size={16} />,
      required: true,
    },
    onChange: (value) => console.log('LongOptionsList onChange:', value),
  },
};

// ==================== MULTI-COLUMN DATE PICKERS ====================

// Combined Date Picker with Multiple Columns
export const MultiColumnDatePicker: Story = {
  args: {
    defaultValue: [15, 5, 1995],
    inputProps: {
      label: 'Ngày sinh',
      placeholder: 'Chọn ngày tháng năm sinh',
      suffixIcon: <Calendar size={16} />,
      required: true,
    },
    pickerProps: {
      title: 'Chọn ngày sinh',
      confirmText: 'Xác nhận',
      cancelText: 'Hủy',
    },
    columns: [dayOptions, monthOptions, yearOptions],
    formatter: (values) => {
      const [day, month, year] = values;
      return `${day}/${month}/${year}`;
    },
    onChange: (value) => console.log('MultiColumnDatePicker onChange:', value),
  },
};

// Year Picker
export const YearPicker: Story = {
  args: {
    options: yearOptions,
    defaultValue: 1995,
    inputProps: {
      label: 'Năm sinh',
      placeholder: 'Chọn năm sinh',
      required: true,
    },
    onChange: (value) => console.log('YearPicker onChange:', value),
  },
};

// Month Picker
export const MonthPicker: Story = {
  args: {
    options: monthOptions,
    defaultValue: 5,
    inputProps: {
      label: 'Tháng sinh',
      placeholder: 'Chọn tháng sinh',
      required: true,
    },
    onChange: (value) => console.log('MonthPicker onChange:', value),
  },
};

// Day Picker
export const DayPicker: Story = {
  args: {
    options: dayOptions,
    defaultValue: 15,
    inputProps: {
      label: 'Ngày sinh',
      placeholder: 'Chọn ngày sinh',
      required: true,
    },
    onChange: (value) => console.log('DayPicker onChange:', value),
  },
};

// Multi-Column Time Picker
export const MultiColumnTimePicker: Story = {
  args: {
    defaultValue: [12, 30],
    inputProps: {
      label: 'Thời gian',
      placeholder: 'Chọn giờ và phút',
      suffixIcon: <Clock size={16} />,
      required: true,
    },
    pickerProps: {
      title: 'Chọn thời gian',
      confirmText: 'Xác nhận',
      cancelText: 'Hủy',
    },
    columns: [
      Array.from({ length: 24 }, (_, i) => ({
        label: `${i.toString().padStart(2, '0')}`,
        value: i,
      })),
      Array.from({ length: 60 }, (_, i) => ({
        label: `${i.toString().padStart(2, '0')}`,
        value: i,
      })),
    ],
    formatter: (values) => {
      const [hour, minute] = values;
      return `${hour?.toString().padStart(2, '0')}:${minute?.toString().padStart(2, '0')}`;
    },
    onChange: (value) => console.log('MultiColumnTimePicker onChange:', value),
  },
};

// Multi-Column Date Picker with Custom Format
export const CustomFormattedDatePicker: Story = {
  args: {
    defaultValue: [15, 5, 1995],
    inputProps: {
      label: 'Ngày sinh',
      placeholder: 'Chọn ngày sinh',
      suffixIcon: <Calendar size={16} />,
      required: true,
    },
    pickerProps: {
      title: 'Chọn ngày sinh',
      confirmText: 'Xác nhận',
      cancelText: 'Hủy',
    },
    columns: [dayOptions, monthOptions, yearOptions],
    formatter: (values) => {
      const [day, month, year] = values;
      const monthNames = [
        'Tháng 1',
        'Tháng 2',
        'Tháng 3',
        'Tháng 4',
        'Tháng 5',
        'Tháng 6',
        'Tháng 7',
        'Tháng 8',
        'Tháng 9',
        'Tháng 10',
        'Tháng 11',
        'Tháng 12',
      ];
      return `${day} ${monthNames[month as number - 1]}, ${year}`;
    },
    onChange: (value) => console.log('CustomFormattedDatePicker onChange:', value),
  },
};

// Multi-Column Form Examples
export const MultiColumnFormExample: Story = {
  render: () => (
    <div
      style={{
        maxWidth: '600px',
        padding: '32px',
        border: '1px solid #e1e5e9',
        borderRadius: '16px',
        backgroundColor: '#ffffff',
      }}
    >
      <h3
        style={{
          marginBottom: '24px',
          color: '#1a1a1a',
          fontSize: '20px',
          fontWeight: '700',
          textAlign: 'center',
        }}
      >
        Thông tin cá nhân
      </h3>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        <InputPicker
          defaultValue={[15, 5, 1995]}
          inputProps={{
            label: 'Ngày sinh',
            placeholder: 'Chọn ngày sinh của bạn',
            suffixIcon: <Calendar size={16} />,
            required: true,
          }}
          pickerProps={{
            title: 'Chọn ngày sinh',
            confirmText: 'Xác nhận',
            cancelText: 'Hủy',
          }}
          columns={[dayOptions, monthOptions, yearOptions]}
          formatter={(values) => {
            const [day, month, year] = values;
            return `${day}/${month}/${year}`;
          }}
          onChange={(value) => console.log('MultiColumnFormExample DatePicker onChange:', value)}
        />

        <InputPicker
          defaultValue={[9, 30]}
          inputProps={{
            label: 'Giờ làm việc',
            placeholder: 'Chọn giờ bắt đầu',
            suffixIcon: <Clock size={16} />,
            required: true,
          }}
          pickerProps={{
            title: 'Chọn giờ làm việc',
            confirmText: 'Xác nhận',
            cancelText: 'Hủy',
          }}
          columns={[
            Array.from({ length: 24 }, (_, i) => ({
              label: `${i.toString().padStart(2, '0')} giờ`,
              value: i,
            })),
            Array.from({ length: 60 }, (_, i) => ({
              label: `${i.toString().padStart(2, '0')} phút`,
              value: i,
            })),
          ]}
          formatter={(values) => {
            const [hour, minute] = values;
            return `${hour?.toString().padStart(2, '0')}:${minute?.toString().padStart(2, '0')}`;
          }}
          onChange={(value) => console.log('MultiColumnFormExample TimePicker onChange:', value)}
        />

        <InputPicker
          options={cityOptions}
          defaultValue="hcm"
          inputProps={{
            label: 'Thành phố',
            placeholder: 'Chọn thành phố',
            suffixIcon: <MapPin size={16} />,
            required: true,
          }}
          onChange={(value) => console.log('MultiColumnFormExample CityPicker onChange:', value)}
        />
      </div>
    </div>
  ),
};

// Advanced Multi-Column DateTime Picker
export const DateTimePicker: Story = {
  args: {
    defaultValue: [15, 5, 1995, 14, 30],
    inputProps: {
      label: 'Ngày và giờ hẹn',
      placeholder: 'Chọn ngày và giờ',
      suffixIcon: <Calendar size={16} />,
      required: true,
    },
    pickerProps: {
      title: 'Chọn ngày và giờ hẹn',
      confirmText: 'Xác nhận',
      cancelText: 'Hủy',
    },
    columns: [
      dayOptions,
      monthOptions,
      yearOptions,
      Array.from({ length: 24 }, (_, i) => ({
        label: `${i.toString().padStart(2, '0')}h`,
        value: i,
      })),
      Array.from({ length: 60 }, (_, i) => ({
        label: `${i.toString().padStart(2, '0')}m`,
        value: i,
      })),
    ],
    formatter: (values) => {
      const [day, month, year, hour, minute] = values;
      return `${day}/${month}/${year} lúc ${hour?.toString().padStart(2, '0')}:${minute?.toString().padStart(2, '0')}`;
    },
    onChange: (value) => console.log('DateTimePicker onChange:', value),
  },
};

// Registration Form with Multi-Column Date Pickers
export const RegistrationForm: Story = {
  render: () => (
    <div
      style={{
        maxWidth: '700px',
        padding: '32px',
        border: '1px solid #e1e5e9',
        borderRadius: '16px',
        backgroundColor: '#ffffff',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      }}
    >
      <h3
        style={{
          marginBottom: '24px',
          color: '#1a1a1a',
          fontSize: '24px',
          fontWeight: '700',
          textAlign: 'center',
        }}
      >
        Đăng ký tài khoản
      </h3>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        <div>
          <h4
            style={{
              marginBottom: '12px',
              fontSize: '16px',
              fontWeight: '600',
              color: '#374151',
            }}
          >
            Ngày sinh
          </h4>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '12px' }}>
            <InputPicker
              options={dayOptions}
              defaultValue={15}
              inputProps={{
                label: 'Ngày',
                placeholder: 'DD',
                required: true,
                status: 'default',
              }}
            />

            <InputPicker
              options={monthOptions}
              defaultValue={5}
              inputProps={{
                label: 'Tháng',
                placeholder: 'MM',
                required: true,
                status: 'default',
              }}
            />

            <InputPicker
              options={yearOptions}
              defaultValue={1995}
              inputProps={{
                label: 'Năm',
                placeholder: 'YYYY',
                required: true,
                status: 'default',
              }}
            />
          </div>
        </div>

        <div>
          <h4
            style={{
              marginBottom: '12px',
              fontSize: '16px',
              fontWeight: '600',
              color: '#374151',
            }}
          >
            Thông tin khác
          </h4>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <InputPicker
              options={cityOptions}
              defaultValue="hcm"
              inputProps={{
                label: 'Thành phố',
                placeholder: 'Chọn thành phố',
                suffixIcon: <MapPin size={16} />,
                required: true,
              }}
            />

            <InputPicker
              options={statusOptions}
              defaultValue="active"
              inputProps={{
                label: 'Trạng thái',
                placeholder: 'Chọn trạng thái',
                suffixIcon: <Star size={16} />,
                required: true,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  ),
};

// Compact Date Picker Row
export const CompactDateRow: Story = {
  render: () => (
    <div
      style={{
        maxWidth: '400px',
        padding: '20px',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        backgroundColor: '#ffffff',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'end',
          gap: '8px',
          marginBottom: '12px',
        }}
      >
        <div style={{ flex: '0 0 80px' }}>
          <InputPicker
            options={dayOptions}
            defaultValue={15}
            inputProps={{
              label: 'Ngày',
              placeholder: 'DD',
              required: true,
            }}
          />
        </div>

        <div style={{ flex: '0 0 100px' }}>
          <InputPicker
            options={monthOptions}
            defaultValue={5}
            inputProps={{
              label: 'Tháng',
              placeholder: 'MM',
              required: true,
            }}
          />
        </div>

        <div style={{ flex: '1' }}>
          <InputPicker
            options={yearOptions}
            defaultValue={1995}
            inputProps={{
              label: 'Năm',
              placeholder: 'YYYY',
              required: true,
            }}
          />
        </div>
      </div>

      <div
        style={{
          fontSize: '12px',
          color: '#6b7280',
          textAlign: 'center',
        }}
      >
        Định dạng: DD/MM/YYYY
      </div>
    </div>
  ),
};
