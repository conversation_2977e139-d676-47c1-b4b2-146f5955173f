// Libraries
import React, { memo } from "react";
import { AnimatePresence, motion } from "motion/react";

// Components
import { Spinner } from "./Spinner";

interface FullLoadingProps {}

export const FullLoading: React.FC<FullLoadingProps> = memo(() => {
  return (
    <motion.div
      initial={{ opacity: 1 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{
        duration: 0.3,
        ease: "easeInOut",
      }}
      className="fixed w-full top-0 left-0 h-screen flex justify-center items-center bg-white z-[1000]"
    >
      <Spinner />
    </motion.div>
  );
});

FullLoading.displayName = "FullLoading";
