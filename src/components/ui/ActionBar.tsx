// Libraries
import React from "react";
import styled from "styled-components";
import { motion } from "motion/react";

// Components
import { Text } from "zmp-ui";

interface ActionBarProps {
  title?: string;
}

const ActionBarWrapper = styled(motion.div)`
  position: relative;
  /* aspect-ratio: 3.56; */
  width: 100%;
  height: 100px;
  background-color: var(--color-main-primary);
  padding: 0px 10px;
  flex-shrink: 0;

  > .header-title {
    font-size: 20px;
    font-weight: 500;
    color: #fff;
    position: absolute;
    bottom: 10px;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;

export const ActionBar: React.FC<ActionBarProps> = (props) => {
  const { title, ...restProps } = props;

  return (
    <ActionBarWrapper {...restProps}>
      <Text className="header-title">{title}</Text>
    </ActionBarWrapper>
  );
};
