import { Radio as AMARadio, RadioProps as AMARadioProps } from '@antscorp/ama-ui';
import styled from 'styled-components';

type RadioProps = Pick<
  AMARadioProps,
  | 'onChange'
  | 'className'
  | 'children'
  | 'disabled'
  | 'checked'
  | 'defaultChecked'
  | 'style'
  | 'value'
>;

const StyledRadio = styled(AMARadio)`
  &.adm-radio {
    --icon-size: 20px;
    --color: var(--color-main-primary);
    --border-color: var(--color-grey-secondary);
    --text-color: var(--color-text-primary);

    .adm-radio-content {
      color: var(--text-color);
      font-size: 15px;
      line-height: 22px;
    }

    .adm-radio-icon {
      display: inline-flex;
      border-width: 1.5px;
      border-color: var(--border-color);
    }

    &.adm-radio-checked .adm-radio-icon {
      background-color: var(--color-green-primary);
      border-color: var(--color-green-primary);
    }

    &.adm-radio-disabled {
      .adm-radio-content {
        color: var(--color-text-light);
      }

      .adm-radio-icon {
        border-color: var(--color-grey-secondary);
        background-color: var(--color-disabled);

        &.adm-radio-icon-checked {
          background-color: var(--color-background-disabled);
        }
      }
    }
  }
`;

export const Radio = (props: RadioProps) => <StyledRadio {...props} />;

Radio.Group = AMARadio.Group;
