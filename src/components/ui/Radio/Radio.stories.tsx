import { Meta, StoryObj } from '@storybook/react';
import { Space } from '@antscorp/ama-ui';
import { Radio } from './Radio';

const meta = {
  title: 'ui/Radio',
  component: Radio,
  argTypes: {
    checked: {
      control: 'boolean',
    },
    disabled: {
      control: 'boolean',
    },
  },
} satisfies Meta<typeof Radio>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Radio option',
  },
};

export const Checked: Story = {
  args: {
    children: 'Selected option',
    checked: true,
  },
};

export const Disabled: Story = {
  args: {
    children: 'Disabled option',
    disabled: true,
  },
};

export const DisabledChecked: Story = {
  args: {
    children: 'Disabled selected option',
    disabled: true,
    checked: true,
  },
};

export const WithValue: Story = {
  args: {
    children: 'Option with value',
    value: 'option1',
  },
};

export const RadioGroupBasic = () => (
  <Radio.Group defaultValue="coffee">
    <Space direction="vertical">
      <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>Chọn đồ uống yêu thích:</h3>
      <Radio value="coffee">Cà phê</Radio>
      <Radio value="tea">Trà</Radio>
      <Radio value="juice">Nước ép</Radio>
    </Space>
  </Radio.Group>
);

export const RadioGroupSize = () => (
  <Radio.Group defaultValue="medium">
    <Space direction="vertical">
      <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>Chọn size cà phê:</h3>
      <Radio value="small" style={{ '--icon-size': '24px' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '250px',
          }}
        >
          <span>Size S - 12oz</span>
          <span style={{ fontWeight: '600', color: '#D4A574' }}>45.000đ</span>
        </div>
      </Radio>
      <Radio value="medium" style={{ '--icon-size': '24px' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '250px',
          }}
        >
          <span>Size M - 16oz</span>
          <span style={{ fontWeight: '600', color: '#D4A574' }}>55.000đ</span>
        </div>
      </Radio>
      <Radio value="large" style={{ '--icon-size': '24px' }} disabled>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '250px',
          }}
        >
          <span>Size L - 20oz</span>
          <span style={{ fontWeight: '600', color: '#D4A574' }}>65.000đ</span>
        </div>
      </Radio>
    </Space>

    <h3 style={{ marginTop: 20, fontSize: '16px', fontWeight: '600' }}>Chọn size cà phê:</h3>
    <Space direction="horizontal" style={{ '--gap': '20px' }}>
      <Radio value="small">
        <span>Size S - 12oz</span>
      </Radio>
      <Radio value="medium">
        <span>Size M - 16oz</span>
      </Radio>
    </Space>
  </Radio.Group>
);
