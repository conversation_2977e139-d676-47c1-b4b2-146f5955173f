// Libraries
import React from 'react';
import { Selector as AMASelector, SelectorProps as AMASelectorProps } from '@antscorp/ama-ui';
import styled from 'styled-components';

export type SelectorTagProps = Pick<
  AMASelectorProps<string | number>,
  'value' | 'defaultValue' | 'disabled' | 'onChange' | 'options'
>;

// Styled Components
const StyledSelector = styled(AMASelector)`
  &.adm-selector {
    --border: 1px solid #e9ebed;
    --checked-border: 1px solid transparent;
    --color: var(--color-background);
    --text-color: var(--color-text-primary);
    --border-radius: 54px;
    --checked-color: var(--color-main-primary);
    --checked-text-color: var(--color-text-primary);
    --padding: 6px 12px;
    --gap: 10px;

    line-height: 22px;
  }
`;

// Main Component
export const SelectorTag: React.FC<SelectorTagProps> = (props) => {
  return <StyledSelector {...props} multiple={false} showCheckMark={false} />;
};

SelectorTag.displayName = 'SelectorTag';
