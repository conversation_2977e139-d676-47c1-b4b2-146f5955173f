import { Meta, StoryObj } from '@storybook/react';
import { SelectorTag } from './SelectorTag';

const meta = {
  title: 'ui/SelectorTag',
  component: SelectorTag,
  parameters: {
    layout: 'padded',
  },
  argTypes: {},
} satisfies Meta<typeof SelectorTag>;

export default meta;

type Story = StoryObj<typeof meta>;

// Basic usage with hashtags
export const Default: Story = {
  args: {
    options: [
      { label: '#all', value: 'all' },
      { label: '#new-restaurant', value: 'new-restaurant' },
      { label: '#popular', value: 'popular' },
      { label: '#nearby', value: 'nearby' },
    ],
    defaultValue: ['all'],
    onChange: (value) => console.log('Selection changed:', value),
  },
};

export const SingleSelection: Story = {
  args: {
    options: [
      { label: 'Small', value: 'small' },
      { label: 'Medium', value: 'medium' },
      { label: 'Large', value: 'large' },
    ],
    defaultValue: ['medium'],
    onChange: (value) => console.log('Size selected:', value),
  },
};

// Disabled state showcase
export const DisabledState: Story = {
  args: {
    options: [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ],
    defaultValue: ['option1'],
    disabled: true,
    onChange: (value) => console.log('This should not trigger:', value),
  },
};
