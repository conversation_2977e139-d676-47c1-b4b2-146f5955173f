import React from 'react';
import { Button } from '../Button';
import { ActionSheet as AntdActionSheet, Space } from '@antscorp/ama-ui';
import type { ButtonProps, ActionSheetProps as AntdActionSheetProps } from '@antscorp/ama-ui';
import clsx from 'clsx';
import styled from 'styled-components';

export interface ActionSheetAction {
  key: string;
  text: string;
  disabled?: boolean;
  danger?: boolean;
  icon?: React.ReactNode;
  onClick?: () => void;
  // Button props
  shape?: ButtonProps['shape'];
  type?: ButtonProps['type'];
}

export interface ActionSheetProps {
  visible: boolean;
  onClose: () => void;
  actions?: ActionSheetAction[];
  title?: string;
  description?: string;
  cancelText?: string;
  className?: string;
  styles?: AntdActionSheetProps['styles'];
  safeArea?: boolean;
  closeOnMaskClick?: boolean;
  closeOnAction?: boolean;
  body?: string | TrustedHTML;
  extra?: React.ReactNode;
}

const OFFSET_PD = 8;
const ACT_HEIGHT = 48;

const calcMaxHeightExtraArea = (
  props: AntdActionSheetProps & { actionSheetActionsLength: number },
) => {
  const { cancelText, actionSheetActionsLength: len } = props;
  return `calc(100vh - ${len * ACT_HEIGHT + OFFSET_PD * (len + 1) + (cancelText ? ACT_HEIGHT + OFFSET_PD : 0)}px)`;
};

const AntdActionSheetStyled = styled(AntdActionSheet)<{ actionSheetActionsLength: number }>`
  &.adm-action-sheet.ants-action-sheet {
    max-height: 100vh;

    .adm-action-sheet-extra {
      max-height: ${calcMaxHeightExtraArea};
      flex-direction: column;
      padding: 0px;
      border-bottom: none;
    }

    .adm-action-sheet-button-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: ${OFFSET_PD}px ${OFFSET_PD * 3}px;
    }

    .adm-action-sheet-button-item-wrapper {
      max-height: ${ACT_HEIGHT}px;
      border-bottom: none;
    }

    .ants-action-sheet__btn {
      height: ${ACT_HEIGHT}px;
    }

    .adm-action-sheet-button-item {
      height: ${ACT_HEIGHT}px;
      line-height: ${ACT_HEIGHT}px;
      padding: 0px;
    }
  }
`;

const ActionSheetBodyStyled = styled.div``;

export const ActionSheet: React.FC<ActionSheetProps> = ({
  visible,
  extra,
  onClose,
  actions = [],
  title,
  description,
  cancelText,
  className = '',
  styles,
  safeArea = true,
  closeOnMaskClick = true,
  closeOnAction = true,
  body,
}) => {
  const handleActionClick = (action: ActionSheetAction) => {
    if (action.disabled) return;

    action.onClick?.();

    if (closeOnAction) {
      onClose();
    }
  };

  const actionSheetActions = actions.map((action) => ({
    key: action.key,
    text: (
      <Button
        shape={action.shape ?? 'rounded'}
        type={action.type ?? 'submit'}
        disabled={action.disabled}
        color={action.danger ? 'danger' : 'primary'}
        className="ants-action-sheet__btn w-full justify-start p-0 text-center"
        onClick={() => {}}
      >
        <Space>
          {action.icon && <span className="align-text-bottom">{action.icon}</span>}
          <span>{action.text}</span>
        </Space>
      </Button>
    ),
    disabled: action.disabled,
    onClick: () => handleActionClick(action),
  }));

  return (
    <AntdActionSheetStyled
      visible={visible}
      onClose={onClose}
      actions={actionSheetActions}
      cancelText={cancelText}
      safeArea={safeArea}
      actionSheetActionsLength={actionSheetActions.length}
      closeOnMaskClick={closeOnMaskClick}
      className={clsx('ants-action-sheet', className)}
      styles={styles}
      extra={
        <>
          {title || description ? (
            <div className="border-b border-solid border-b-[#e9ebed] px-6 py-4">
              {title && <div className="text-center text-xl font-semibold text-black">{title}</div>}

              {description && (
                <div className="mt-1 leading-[22px] text-secondary">{description}</div>
              )}
            </div>
          ) : undefined}

          {body && (
            <ActionSheetBodyStyled
              className="min-h-auto flex-1 overflow-y-auto p-4"
              dangerouslySetInnerHTML={{ __html: body }}
            />
          )}

          {extra}
        </>
      }
    />
  );
};

export default ActionSheet;
