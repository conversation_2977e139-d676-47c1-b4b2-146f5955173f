import type { <PERSON>a, StoryObj } from '@storybook/react';
import { ActionSheet } from './ActionSheet';
import { useState } from 'react';
import { FileTextIcon, SettingsIcon, TrashIcon, ShareIcon, EditIcon, InfoIcon } from 'lucide-react';
import '../../../css/app.scss';

const meta: Meta<typeof ActionSheet> = {
  title: 'UI/ActionSheet',
  component: ActionSheet,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'ActionSheet component provides a bottom sheet with action options. It wraps the Antd Mobile ActionSheet with customized styling and Vietnamese language support.',
      },
    },
  },
  argTypes: {
    visible: {
      control: 'boolean',
      description: 'Whether the ActionSheet is visible',
    },
    title: {
      control: 'text',
      description: 'Title displayed at the top of the ActionSheet',
    },
    description: {
      control: 'text',
      description: 'Description text below the title',
    },
    cancelText: {
      control: 'text',
      description: 'Text for the cancel button',
    },
    closeOnAction: {
      control: 'boolean',
      description: 'Whether to close the ActionSheet when an action is selected',
    },
    closeOnMaskClick: {
      control: 'boolean',
      description: 'Whether to close when clicking on the mask/backdrop',
    },
    safeArea: {
      control: 'boolean',
      description: 'Whether to add safe area padding for notched devices',
    },
  },
};

export default meta;
type Story = StoryObj<typeof ActionSheet>;

// Wrapper component for interactive stories
const ActionSheetWrapper = (args: any) => {
  const [visible, setVisible] = useState(false);

  return (
    <div>
      <button onClick={() => setVisible(true)} className="rounded bg-blue-500 px-4 py-2 text-white">
        Hiển thị ActionSheet
      </button>
      <ActionSheet {...args} visible={visible} onClose={() => setVisible(false)} />
    </div>
  );
};

export const Default: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    actions: [
      {
        key: 'share',
        text: 'Chia sẻ',
        icon: <ShareIcon size={20} />,
        onClick: () => console.log('Share clicked'),
      },
      {
        key: 'edit',
        text: 'Chỉnh sửa',
        icon: <EditIcon size={20} />,
        onClick: () => console.log('Edit clicked'),
      },
      {
        key: 'delete',
        text: 'Xóa',
        icon: <TrashIcon size={20} />,
        danger: true,
        onClick: () => console.log('Delete clicked'),
      },
    ],
    cancelText: 'Hủy',
  },
};

export const Basic: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    actions: [
      {
        key: 'agree',
        text: 'Đồng ý',
        onClick: () => console.log('agree'),
      },
      {
        key: 'notched',
        text: 'Không đồng ý',
        onClick: () => console.log('notched'),
      },
    ],
    cancelText: 'Hủy',
    title: 'Điều khoản sử dụng',
    body: `
  <h1>Chính sách Quyền riêng tư</h1>
  <p><strong>Ngày hiệu lực:</strong> 01/09/2025</p>
  <p><strong>Cập nhật lần cuối:</strong> 08/09/2025</p>

  <div class="section">
    <h2>1. Giới thiệu</h2>
    <p>
      Chúng tôi trân trọng sự tin tưởng của bạn và cam kết bảo vệ quyền riêng tư của bạn. 
      Chính sách này giải thích cách chúng tôi thu thập, sử dụng, lưu trữ và chia sẻ thông tin khi bạn sử dụng website, 
      ứng dụng di động và các dịch vụ liên quan. Khi truy cập hoặc sử dụng dịch vụ, bạn đồng ý với các điều khoản trong chính sách này.
    </p>
  </div>

  <div class="section">
    <h2>2. Thông tin chúng tôi thu thập</h2>
    <p>Chúng tôi có thể thu thập các loại thông tin sau:</p>
    <ul>
      <li><strong>Thông tin tài khoản:</strong> họ tên, địa chỉ email, số điện thoại, thông tin đăng nhập.</li>
      <li><strong>Dữ liệu sử dụng:</strong> các trang đã truy cập, thao tác thực hiện, cách bạn tương tác với nội dung.</li>
      <li><strong>Thông tin thiết bị & kỹ thuật:</strong> loại trình duyệt, hệ điều hành, địa chỉ IP, định danh thiết bị.</li>
      <li><strong>Cookie & công cụ theo dõi:</strong> dữ liệu từ cookie, pixel và các công cụ phân tích.</li>
    </ul>
  </div>

  <div class="section">
    <h2>3. Cách chúng tôi sử dụng thông tin</h2>
    <p>Thông tin được thu thập sẽ được sử dụng cho các mục đích:</p>
    <ul>
      <li>Cung cấp, vận hành và duy trì dịch vụ.</li>
      <li>Cải thiện trải nghiệm người dùng và cá nhân hóa nội dung.</li>
      <li>Gửi thông báo, cập nhật, khuyến mãi hoặc hỗ trợ khách hàng.</li>
      <li>Đảm bảo an ninh, ngăn chặn gian lận và tuân thủ pháp luật.</li>
    </ul>
  </div>

  <div class="section">
    <h2>4. Chia sẻ thông tin</h2>
    <p>
      Chúng tôi không bán thông tin cá nhân của bạn. Tuy nhiên, chúng tôi có thể chia sẻ dữ liệu với các bên thứ ba đáng tin cậy, bao gồm:
    </p>
    <ul>
      <li><strong>Nhà cung cấp dịch vụ:</strong> để hỗ trợ vận hành (lưu trữ, phân tích, chăm sóc khách hàng).</li>
      <li><strong>Cơ quan pháp luật:</strong> khi có yêu cầu theo quy định hiện hành.</li>
      <li><strong>Chuyển giao doanh nghiệp:</strong> trong trường hợp sáp nhập, mua lại hoặc bán tài sản.</li>
    </ul>
  </div>

  <div class="section">
    <h2>5. Thời gian lưu trữ dữ liệu</h2>
    <p>
      Chúng tôi chỉ lưu trữ dữ liệu cá nhân trong thời gian cần thiết để phục vụ các mục đích nêu trong chính sách này, 
      trừ khi pháp luật yêu cầu thời gian lâu hơn. Sau khi không còn cần thiết, dữ liệu sẽ được xóa an toàn hoặc ẩn danh.
    </p>
  </div>

  <div class="section">
    <h2>6. Quyền của bạn</h2>
    <p>Tùy thuộc vào khu vực, bạn có thể có các quyền sau:</p>
    <ul>
      <li>Quyền truy cập và yêu cầu bản sao dữ liệu cá nhân.</li>
      <li>Quyền chỉnh sửa thông tin không chính xác hoặc chưa đầy đủ.</li>
      <li>Quyền yêu cầu xóa dữ liệu (“quyền được lãng quên”).</li>
      <li>Quyền hạn chế hoặc phản đối một số hoạt động xử lý.</li>
      <li>Quyền rút lại sự đồng ý đối với các xử lý dựa trên sự đồng ý.</li>
    </ul>
  </div>

  <div class="section">
    <h2>7. Bảo mật</h2>
    <p>
      Chúng tôi áp dụng các biện pháp kỹ thuật và tổ chức hợp lý để bảo vệ dữ liệu cá nhân khỏi việc truy cập, 
      thay đổi, tiết lộ hoặc phá hủy trái phép. Tuy nhiên, không có phương thức truyền tải qua Internet 
      hoặc lưu trữ điện tử nào an toàn tuyệt đối.
    </p>
  </div>

  <div class="section">
    <h2>8. Quyền riêng tư của trẻ em</h2>
    <p>
      Dịch vụ của chúng tôi không hướng tới trẻ em dưới 13 tuổi (hoặc độ tuổi trưởng thành theo luật địa phương). 
      Chúng tôi không cố ý thu thập dữ liệu cá nhân từ trẻ em. Nếu bạn tin rằng trẻ em đã cung cấp thông tin cho chúng tôi, 
      hãy liên hệ để chúng tôi kịp thời xóa dữ liệu đó.
    </p>
  </div>

  <div class="section">
    <h2>9. Chuyển dữ liệu quốc tế</h2>
    <p>
      Thông tin của bạn có thể được chuyển tới, lưu trữ và xử lý tại các quốc gia khác với quốc gia bạn đang cư trú. 
      Chúng tôi đảm bảo áp dụng các biện pháp bảo vệ phù hợp để bảo mật dữ liệu cá nhân khi xử lý ở nước ngoài.
    </p>
  </div>

  <div class="section">
    <h2>10. Thay đổi chính sách</h2>
    <p>
      Chúng tôi có thể cập nhật Chính sách này theo thời gian để phản ánh thay đổi công nghệ, yêu cầu pháp lý 
      hoặc hoạt động kinh doanh. Bản cập nhật sẽ được đăng kèm ngày “Cập nhật lần cuối”. 
      Chúng tôi khuyến nghị bạn thường xuyên xem lại trang này.
    </p>
  </div>

  <div class="section">
    <h2>11. Liên hệ</h2>
    <p>
      Nếu có bất kỳ câu hỏi hoặc góp ý nào về chính sách này, vui lòng liên hệ:<br/>
      Email: <a href="mailto:<EMAIL>"><EMAIL></a><br/>
      Địa chỉ: 123 Đường Ví Dụ, Thành phố Ví Dụ, EX 12345
    </p>
  </div>
`,
  },
};

export const WithTitleAndDescription: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    title: 'Điều khoản sử dụng',
    description: 'Nhằm giúp gia tăng quyền lợi của khách hàng khi sử dụng Thẻ The Golden Spoon...',
    actions: [
      {
        key: 'agree',
        text: 'Đồng ý',
        onClick: () => console.log('Agree clicked'),
      },
      {
        key: 'decline',
        text: 'Từ chối',
        danger: true,
        onClick: () => console.log('Decline clicked'),
      },
    ],
    cancelText: 'Đóng',
  },
};

export const PolicyActionSheet: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    title: 'Chính sách thành viên',
    description:
      'Nhằm giúp gia tăng quyền lợi của khách hàng khi sử dụng Thẻ The Golden Spoon, Mãnh hàng thiết kế The Golden Spoon, Công Ty Cộng Vàng xin thông báo tới chủ tài khoản ứng dụng/ thể một số điều chỉnh, bổ sung trong Chính sách khách hàng thân thiết The Golden Spoon.',
    actions: [
      {
        key: 'read_policy',
        text: 'Cơ chế tích lũy G-coin vào tài khoản ứng dụng/thẻ',
        onClick: () => console.log('Read policy clicked'),
      },
      {
        key: 'view_details',
        text: 'Xem chi tiết',
        onClick: () => console.log('View details clicked'),
      },
    ],
    cancelText: 'Đồng ý',
  },
};

export const PrivacyPolicySheet: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    title: 'Chính sách bảo mật',
    description:
      '1. Giới thiệu\n\n1.1 Website http://ggg.com.vn được điều hành bởi Công ty cổ phần thương mại dịch vụ Cộng Vàng, các công ty con và các thực thể thuộc về Cộng Vàng...',
    actions: [
      {
        key: 'introduction',
        text: '1. Giới thiệu',
        onClick: () => console.log('Introduction clicked'),
      },
    ],
    cancelText: 'Đồng ý',
  },
};

export const SimpleActions: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    actions: [
      {
        key: 'camera',
        text: 'Chụp ảnh',
        onClick: () => console.log('Camera clicked'),
      },
      {
        key: 'gallery',
        text: 'Chọn từ thư viện',
        onClick: () => console.log('Gallery clicked'),
      },
    ],
    cancelText: 'Hủy',
  },
};

export const WithDisabledActions: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    title: 'Tùy chọn',
    actions: [
      {
        key: 'available',
        text: 'Tùy chọn khả dụng',
        icon: <InfoIcon size={20} />,
        onClick: () => console.log('Available clicked'),
      },
      {
        key: 'disabled',
        text: 'Tùy chọn không khả dụng',
        icon: <SettingsIcon size={20} />,
        disabled: true,
        onClick: () => console.log('Disabled clicked'),
      },
      {
        key: 'dangerous',
        text: 'Tùy chọn nguy hiểm',
        icon: <TrashIcon size={20} />,
        danger: true,
        onClick: () => console.log('Dangerous clicked'),
      },
    ],
    cancelText: 'Hủy',
  },
};

export const LongActionsList: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    title: 'Nhiều tùy chọn',
    description: 'Chọn một trong các tùy chọn sau:',
    actions: [
      { key: 'option1', text: 'Tùy chọn 1', icon: <FileTextIcon size={20} /> },
      { key: 'option2', text: 'Tùy chọn 2', icon: <FileTextIcon size={20} /> },
      { key: 'option3', text: 'Tùy chọn 3', icon: <FileTextIcon size={20} /> },
      { key: 'option4', text: 'Tùy chọn 4', icon: <FileTextIcon size={20} /> },
      { key: 'option5', text: 'Tùy chọn 5', icon: <FileTextIcon size={20} /> },
      { key: 'option6', text: 'Tùy chọn 6', icon: <FileTextIcon size={20} /> },
      { key: 'delete', text: 'Xóa tất cả', icon: <TrashIcon size={20} />, danger: true },
    ],
    cancelText: 'Hủy',
  },
};

export const CustomCancelText: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    title: 'Xác nhận hành động',
    description: 'Bạn có chắc chắn muốn thực hiện hành động này?',
    actions: [
      {
        key: 'confirm',
        text: 'Xác nhận',
        onClick: () => console.log('Confirm clicked'),
      },
    ],
    cancelText: 'Không, quay lại',
  },
};

export const NoCloseOnAction: Story = {
  render: (args) => <ActionSheetWrapper {...args} />,
  args: {
    title: 'Chọn nhiều tùy chọn',
    description: 'ActionSheet này sẽ không tự động đóng khi chọn tùy chọn',
    actions: [
      {
        key: 'option1',
        text: 'Tùy chọn 1',
        onClick: () => console.log('Option 1 clicked - sheet stays open'),
      },
      {
        key: 'option2',
        text: 'Tùy chọn 2',
        onClick: () => console.log('Option 2 clicked - sheet stays open'),
      },
    ],
    closeOnAction: false,
    cancelText: 'Đóng',
  },
};
