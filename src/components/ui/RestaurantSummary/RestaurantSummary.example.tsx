import React from 'react';
import { RestaurantSummary } from './RestaurantSummary';

// Example usage of RestaurantSummary component
export const RestaurantSummaryExample: React.FC = () => {
  const handlePhoneClick = () => {
    console.log('Phone number clicked!');
    // In a real app, this would open the phone dialer
    // window.open('tel:0123456789', '_self');
  };

  return (
    <div className="space-y-4 p-4">
      <h2 className="mb-4 text-xl font-bold">RestaurantSummary Examples</h2>

      {/* Basic usage */}
      <div>
        <h3 className="mb-2 text-lg font-semibold">Basic Usage</h3>
        <RestaurantSummary
          name="Go<PERSON> Lin<PERSON> Đàm"
          address="Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai"
          phoneNumber="0123 456 789"
          rating={4.5}
        />
      </div>

      {/* With custom logo */}
      <div>
        <h3 className="mb-2 text-lg font-semibold">With Custom Logo</h3>
        <RestaurantSummary
          name="<PERSON><PERSON>"
          address="Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai"
          phoneNumber="0123 456 789"
          rating={4.5}
          logo={
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary">
              <span className="text-xs font-bold text-white">G</span>
            </div>
          }
        />
      </div>

      {/* With phone click handler */}
      <div>
        <h3 className="mb-2 text-lg font-semibold">With Phone Click Handler</h3>
        <RestaurantSummary
          name="Gogi Linh Đàm"
          address="Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai"
          phoneNumber="0123 456 789"
          rating={4.5}
          onPhoneClick={handlePhoneClick}
        />
      </div>

      {/* Multiple restaurants */}
      <div>
        <h3 className="mb-2 text-lg font-semibold">Multiple Restaurants</h3>
        <div className="space-y-3">
          <RestaurantSummary
            name="Gogi Linh Đàm"
            address="Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai"
            phoneNumber="0123 456 789"
            rating={4.5}
          />
          <RestaurantSummary
            name="Gogi Times City"
            address="Tầng 4, TTTM Times City, 458 Minh Khai, Hai Bà Trưng"
            phoneNumber="0987 654 321"
            rating={4.2}
          />
          <RestaurantSummary
            name="Gogi Royal City"
            address="Tầng B1, TTTM Royal City, 72A Nguyễn Trãi, Thanh Xuân"
            phoneNumber="0912 345 678"
            rating={4.8}
          />
        </div>
      </div>
    </div>
  );
};

export default RestaurantSummaryExample;
