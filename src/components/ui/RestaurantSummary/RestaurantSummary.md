# RestaurantSummary Component

A React component that displays restaurant information including name, address, phone number, and rating in a card format.

## Features

- **Restaurant Information Display**: Shows restaurant name, address, and phone number
- **Rating Display**: Displays star rating with customizable value
- **Logo Support**: Supports both image URLs and React components for restaurant logos
- **Interactive Phone Number**: Clickable phone number with custom click handler
- **Responsive Design**: Built with Tailwind CSS for responsive layout
- **Accessibility**: Proper semantic HTML and keyboard navigation support

## Usage

```tsx
import { RestaurantSummary } from 'components/ui';

// Basic usage
<RestaurantSummary
  name="Go<PERSON>"
  address="Dãy A - TT3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Hoàng Mai"
  phoneNumber="0123 456 789"
  rating={4.5}
/>

// With custom logo
<RestaurantSummary
  name="Gogi Lin<PERSON> Đàm"
  address="Dãy A - TT3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"
  phoneNumber="0123 456 789"
  rating={4.5}
  logo={<CustomLogoComponent />}
/>

// With phone click handler
<RestaurantSummary
  name="<PERSON><PERSON>"
  address="Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai"
  phoneNumber="0123 456 789"
  rating={4.5}
  onPhoneClick={() => console.log('Phone clicked!')}
/>
```

## Props

| Prop           | Type                        | Default | Description                                     |
| -------------- | --------------------------- | ------- | ----------------------------------------------- |
| `name`         | `string`                    | -       | **Required.** Restaurant name                   |
| `address`      | `string`                    | -       | **Required.** Restaurant address                |
| `phoneNumber`  | `string`                    | -       | **Required.** Restaurant phone number           |
| `rating`       | `number`                    | `4.5`   | Restaurant rating (1-5)                         |
| `logo`         | `string \| React.ReactNode` | -       | Restaurant logo (URL string or React component) |
| `className`    | `string`                    | `''`    | Additional CSS classes                          |
| `onPhoneClick` | `() => void`                | -       | Callback when phone number is clicked           |

## Design System

The component follows the project's design system:

- **Colors**: Uses CSS variables from `base.scss`
  - Primary text: `--color-text-primary` (#001A33)
  - Secondary text: `--color-text-secondary` (#667685)
  - Star color: `--color-primary` (#E4B653)
  - Phone number: `--color-text-link` (#0068FF)
  - Background: `--color-background` (#FFFFFF)

- **Typography**: Roboto font family with different weights and sizes
  - Title: 17px, font-weight 500
  - Restaurant name: 15px, font-weight 500
  - Address: 12px, font-weight 400
  - Phone number: 15px, font-weight 500

- **Spacing**: Consistent spacing using Tailwind classes
  - Card padding: 12px (p-3)
  - Section gap: 8px (gap-2)
  - Content gap: 12px (gap-3)

## Accessibility

- Uses semantic HTML elements (`h3`, `h4`, `p`, `button`)
- Proper ARIA labels for interactive elements
- Keyboard navigation support
- Screen reader friendly structure

## Examples

### Basic Restaurant Card

```tsx
<RestaurantSummary
  name="Gogi Linh Đàm"
  address="Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai"
  phoneNumber="0123 456 789"
  rating={4.5}
/>
```

### With Custom Logo

```tsx
<RestaurantSummary
  name="Gogi Linh Đàm"
  address="Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai"
  phoneNumber="0123 456 789"
  rating={4.5}
  logo={
    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary">
      <span className="text-xs font-bold text-white">G</span>
    </div>
  }
/>
```

### With Phone Click Handler

```tsx
<RestaurantSummary
  name="Gogi Linh Đàm"
  address="Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai"
  phoneNumber="0123 456 789"
  rating={4.5}
  onPhoneClick={() => {
    // Custom phone handling logic
    window.open('tel:0123456789', '_self');
  }}
/>
```

## Storybook

The component includes comprehensive Storybook stories with various examples:

- Default usage
- With custom logo
- With logo URL
- Different ratings (high, low)
- Long restaurant names and addresses
- Interactive examples
- Multiple examples in a container

Run `npm run storybook` to view all examples and interact with the component.

## Dependencies

- React 18+
- Lucide React (for star icon)
- Tailwind CSS (for styling)
- TypeScript (for type definitions)
