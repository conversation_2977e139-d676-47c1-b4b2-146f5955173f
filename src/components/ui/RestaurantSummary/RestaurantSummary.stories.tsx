import type { <PERSON>a, StoryObj } from '@storybook/react';
import { RestaurantSummary } from './RestaurantSummary';

const meta = {
  title: 'UI/RestaurantSummary',
  component: RestaurantSummary,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    rating: {
      control: { type: 'number', min: 1, max: 5, step: 0.1 },
    },
    onPhoneClick: { action: 'phone clicked' },
  },
} satisfies Meta<typeof RestaurantSummary>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: 'Nhà hàng Golden Gate',
    address: '123 Nguyễn Hu<PERSON>, Quận 1, TP.HCM',
    phoneNumber: '028 1234 5678',
    rating: 4.5,
  },
};

export const WithLogo: Story = {
  args: {
    name: 'Nhà hàng Golden Gate',
    address: '123 Nguyễn Huệ, Quận 1, TP.HCM',
    phoneNumber: '028 1234 5678',
    rating: 4.8,
    logo: 'https://via.placeholder.com/100x100/FFD700/000000?text=GG',
  },
};

export const HighRating: Story = {
  args: {
    name: '<PERSON><PERSON><PERSON> hàng <PERSON>in Star',
    address: '456 Lê Lợi, Quận 1, TP.HCM',
    phoneNumber: '028 9876 5432',
    rating: 5.0,
  },
};

export const LowRating: Story = {
  args: {
    name: 'Nhà hàng Bình Dân',
    address: '789 Hai Bà Trưng, Quận 3, TP.HCM',
    phoneNumber: '028 5555 1234',
    rating: 3.2,
  },
};

export const LongAddress: Story = {
  args: {
    name: 'Nhà hàng Tên Rất Dài Và Phức Tạp',
    address: 'Số 999/99/99 Đường Cách Mạng Tháng Tám, Phường 15, Quận 10, Thành phố Hồ Chí Minh',
    phoneNumber: '028 1111 2222',
    rating: 4.2,
  },
};

export const WithCustomClassName: Story = {
  args: {
    name: 'Nhà hàng Custom Style',
    address: '321 Pasteur, Quận 1, TP.HCM',
    phoneNumber: '028 3333 4444',
    rating: 4.0,
    className: 'border-2 border-primary rounded-lg',
  },
};

export const WithoutTitle: Story = {
  args: {
    name: 'Nhà hàng không có tiêu đề',
    address: '321 Pasteur, Quận 1, TP.HCM',
    phoneNumber: '028 3333 4444',
    rating: 4.0,
    title: false,
  },
};

export const WithoutRating: Story = {
  args: {
    name: 'Nhà hàng không có đánh giá',
    address: '321 Pasteur, Quận 1, TP.HCM',
    phoneNumber: '028 3333 4444',
    rating: undefined,
  },
};
