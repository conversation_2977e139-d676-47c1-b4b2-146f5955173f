import React from 'react';
import StarIcon from 'assets/svg/star.svg?react';

export interface RestaurantSummaryProps {
  title?: string | false;
  rating?: number;
  name: string;
  address: string;
  phoneNumber: string;
  logo?: string | React.ReactNode;
  className?: string;
  onPhoneClick?: () => void;
}

export const RestaurantSummary: React.FC<RestaurantSummaryProps> = ({
  title = 'Nhà hàng',
  name,
  address,
  phoneNumber,
  rating,
  logo,
  className = '',
  onPhoneClick,
}) => {
  const handlePhoneClick = () => {
    if (onPhoneClick) {
      onPhoneClick();
    } else {
      // Default behavior: open phone dialer
      window.open(`tel:${phoneNumber}`, '_self');
    }
  };

  const renderTitle = () => {
    if (title === false) {
      return null;
    }

    return <h3 className="text-[17px] font-medium leading-[26px] text-black">{title}</h3>;
  };

  const renderRating = () => {
    if (rating === undefined) {
      return null;
    }

    return (
      <div className="flex flex-1 items-center justify-end gap-1">
        <StarIcon className="h-6 w-6 fill-current text-black" />

        <span className="font-medium leading-[22px] text-black">{rating}</span>
      </div>
    );
  };

  const renderHeader = () => {
    if (title === false && rating === undefined) {
      return null;
    }

    return (
      <div className="flex w-full items-center justify-between">
        {renderTitle()}
        {renderRating()}
      </div>
    );
  };

  return (
    <div className={`flex flex-col gap-2 bg-white p-3 ${className}`}>
      {renderHeader()}

      <div className="flex gap-3">
        <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center">
          {logo ? (
            typeof logo === 'string' ? (
              <img src={logo} alt={`${name} logo`} className="h-full w-full object-contain" />
            ) : (
              logo
            )
          ) : (
            <div className="flex h-full w-full items-center justify-center rounded-full bg-black">
              <span className="text-xs font-medium text-white">{name.charAt(0).toUpperCase()}</span>
            </div>
          )}
        </div>

        <div className="flex flex-1 flex-col gap-1">
          <h4 className="font-medium leading-[22px] text-black">{name}</h4>

          <p className="text-xs leading-[18px] text-secondary">{address}</p>

          <div className="flex items-center gap-1">
            <span className="text-xs leading-[18px] text-secondary">Hotline:</span>

            <button onClick={handlePhoneClick} className="font-medium leading-[22px] text-blue">
              {phoneNumber}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestaurantSummary;
