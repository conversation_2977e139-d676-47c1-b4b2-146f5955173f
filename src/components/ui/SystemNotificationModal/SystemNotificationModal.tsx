// Libraries
import React from 'react';
import styled from 'styled-components';

// Components
import { Modal } from '../Modal';
import { Button } from '../Button';

// Icons
import { CloseIcon } from 'components/icons';

// Types
import { SystemNotificationModalProps } from './types';

export const StyledModal = styled(Modal)`
  &.adm-modal {
    .close-button {
      position: absolute;
      width: 40px;
      height: 40px;
      background-color: var(--color-primary);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #ffffff;
      right: 10px;
      top: 10px;
      z-index: 10;
      cursor: pointer;
    }

    .modal-content {
      text-align: center;
      font-size: 15px;
      line-height: 24px;
      margin-bottom: 20px;
    }

    .modal-actions {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
    }

    .adm-modal-footer-empty {
      height: 0;
    }
  }
`;

export const SystemNotificationModal: React.FC<SystemNotificationModalProps> = (props) => {
  const {
    visible = false,
    children,
    content,
    title = 'Thông báo',
    showCloseButton = false,
    retryButtonProps,
    showRetryButton = true,
    onClose,
    maskClosable,
    ...restProps
  } = props;

  const {
    children: retryChildren = 'Thử lại',
    onClick: retryOnClick,
    ...restOfRetryButtonProps
  } = retryButtonProps || {};

  const handleRetryClick = () => {
    if (retryOnClick) {
      retryOnClick();
    } else {
      window.location.reload();
    }
  };

  return (
    <StyledModal
      className="modal-classname"
      bodyClassName="body-test"
      visible={visible}
      onClose={onClose}
      title={title}
      closeOnMaskClick={maskClosable ?? false}
      content={
        <>
          {showCloseButton && (
            <div className="close-button" onClick={onClose}>
              <CloseIcon width={16} />
            </div>
          )}

          {content && <div className="modal-content">{content}</div>}

          {showRetryButton && (
            <div className="modal-actions">
              <Button onClick={handleRetryClick} {...restOfRetryButtonProps}>
                {retryChildren}
              </Button>
            </div>
          )}

          {children}
        </>
      }
      {...restProps}
    />
  );
};
