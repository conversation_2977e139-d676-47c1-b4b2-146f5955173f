import type { ReactNode } from 'react';
import type { ModalProps } from '../Modal';

export interface SystemNotificationModalProps extends Omit<ModalProps, 'content' | 'children'> {
  /**
   * The main content/message to display in the modal
   */
  content?: ReactNode;
  /**
   * Additional children to render inside the modal
   */
  children?: ReactNode;
  /**
   * Whether to show the close button (X) in the top right corner
   * @default false
   */
  showCloseButton?: boolean;
  /**
   * Props to pass to the retry/action button
   */
  retryButtonProps?: {
    onClick?: () => void;
    children?: ReactNode;
    disabled?: boolean;
    className?: string;
    style?: React.CSSProperties;
  };
  /**
   * Whether to show the retry/action button
   * @default true
   */
  showRetryButton?: boolean;
  /**
   * The modal title
   */
  title?: string;
  /**
   * Whether the modal is visible
   */
  visible?: boolean;
  /**
   * Callback when modal is closed
   */
  onClose?: () => void;
  /**
   * Whether clicking on mask closes the modal (legacy compatibility)
   */
  maskClosable?: boolean;
}

export type NotificationVariant = 'info' | 'success' | 'warning' | 'error';

export interface NotificationContent {
  title?: string;
  message?: ReactNode;
  variant?: NotificationVariant;
}

