import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { SystemNotificationModal } from './SystemNotificationModal';
import { SystemNotificationModalProps } from './types';

const meta: Meta<typeof SystemNotificationModal> = {
  title: 'UI/SystemNotificationModal',
  component: SystemNotificationModal,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A modal component for displaying system notifications, warnings, errors, and success messages.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    visible: {
      control: 'boolean',
      description: 'Whether the modal is visible',
    },
    title: {
      control: 'text',
      description: 'The modal title',
    },
    content: {
      control: 'text',
      description: 'The main content/message to display',
    },
    showCloseButton: {
      control: 'boolean',
      description: 'Whether to show the close button (X)',
    },
    showRetryButton: {
      control: 'boolean',
      description: 'Whether to show the retry/action button',
    },
    maskClosable: {
      control: 'boolean',
      description: 'Whether clicking on mask closes the modal',
    },
  },
};

export default meta;
type Story = StoryObj<typeof SystemNotificationModal>;

// Helper component for interactive stories
const InteractiveModal = (args: SystemNotificationModalProps) => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <button onClick={() => setVisible(true)} style={{ padding: '10px 20px', fontSize: '16px' }}>
        Show Modal
      </button>
      <SystemNotificationModal {...args} visible={visible} onClose={() => setVisible(false)} />
    </>
  );
};

// Basic Stories
export const Default: Story = {
  args: {
    visible: true,
    title: 'Thông báo',
    content: 'Đây là một thông báo mặc định từ hệ thống.',
    showRetryButton: true,
    showCloseButton: false,
    maskClosable: false,
  },
};

export const Success: Story = {
  args: {
    visible: true,
    title: 'Thành công',
    content: (
      <div style={{ color: '#00c578' }}>
        <div style={{ fontSize: '18px', marginBottom: '10px' }}>✅ Thao tác thành công!</div>
        <div>Dữ liệu của bạn đã được lưu thành công.</div>
      </div>
    ),
    showRetryButton: false,
    showCloseButton: true,
    retryButtonProps: {
      children: 'Tiếp tục',
      onClick: () => console.log('Continue clicked'),
    },
  },
};

export const Warning: Story = {
  args: {
    visible: true,
    title: 'Cảnh báo',
    content: (
      <div style={{ color: '#ff8c00' }}>
        <div style={{ fontSize: '18px', marginBottom: '10px' }}>⚠️ Cần chú ý!</div>
        <div>
          Hành động này có thể ảnh hưởng đến dữ liệu của bạn. Bạn có chắc chắn muốn tiếp tục?
        </div>
      </div>
    ),
    showRetryButton: true,
    showCloseButton: true,
    retryButtonProps: {
      children: 'Tiếp tục',
      onClick: () => console.log('Continue with warning'),
    },
  },
};

export const Error: Story = {
  args: {
    visible: true,
    title: 'Lỗi',
    content: (
      <div style={{ color: '#ef4e49' }}>
        <div style={{ fontSize: '18px', marginBottom: '10px' }}>❌ Có lỗi xảy ra!</div>
        <div>Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet và thử lại.</div>
      </div>
    ),
    showRetryButton: true,
    showCloseButton: false,
    retryButtonProps: {
      children: 'Thử lại',
      onClick: () => console.log('Retry clicked'),
    },
  },
};

export const NetworkError: Story = {
  args: {
    visible: true,
    title: 'Lỗi mạng',
    content: (
      <div>
        <div style={{ fontSize: '18px', marginBottom: '10px' }}>🌐 Mất kết nối mạng</div>
        <div>Vui lòng kiểm tra kết nối internet và thử lại.</div>
      </div>
    ),
    showRetryButton: true,
    showCloseButton: false,
    maskClosable: false,
    retryButtonProps: {
      children: 'Kết nối lại',
      onClick: () => window.location.reload(),
    },
  },
};

export const MaintenanceMode: Story = {
  args: {
    visible: true,
    title: 'Bảo trì hệ thống',
    content: (
      <div>
        <div style={{ fontSize: '18px', marginBottom: '10px' }}>🔧 Hệ thống đang bảo trì</div>
        <div>
          Chúng tôi đang nâng cấp hệ thống để mang lại trải nghiệm tốt hơn. Vui lòng quay lại sau.
        </div>
      </div>
    ),
    showRetryButton: true,
    showCloseButton: false,
    maskClosable: false,
    retryButtonProps: {
      children: 'Đóng ứng dụng',
      onClick: () => console.log('Close app'),
    },
  },
};

export const CustomActions: Story = {
  args: {
    visible: true,
    title: 'Xác nhận',
    content: 'Bạn có muốn xóa dữ liệu này không? Hành động này không thể hoàn tác.',
    showRetryButton: false,
    showCloseButton: true,
    children: (
      <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', marginTop: '20px' }}>
        <button
          style={{
            padding: '10px 20px',
            backgroundColor: '#ef4e49',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
          }}
          onClick={() => console.log('Delete confirmed')}
        >
          Xóa
        </button>
        <button
          style={{
            padding: '10px 20px',
            backgroundColor: '#f0f0f0',
            color: '#333',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
          }}
          onClick={() => console.log('Cancelled')}
        >
          Hủy
        </button>
      </div>
    ),
  },
};

export const LongContent: Story = {
  args: {
    visible: true,
    title: 'Điều khoản sử dụng',
    content: (
      <div style={{ textAlign: 'left', maxHeight: '300px', overflow: 'auto' }}>
        <h4>Điều khoản và Điều kiện</h4>
        <p>
          1. Chấp nhận các điều khoản: Bằng cách truy cập và sử dụng ứng dụng này, bạn chấp nhận và
          đồng ý bị ràng buộc bởi các điều khoản và điều kiện của thỏa thuận này.
        </p>
        <p>
          2. Sử dụng ứng dụng: Bạn có thể sử dụng ứng dụng của chúng tôi cho mục đích cá nhân và phi
          thương mại.
        </p>
        <p>
          3. Quyền riêng tư: Chúng tôi tôn trọng quyền riêng tư của bạn và cam kết bảo vệ thông tin
          cá nhân của bạn.
        </p>
        <p>
          4. Giới hạn trách nhiệm: Chúng tôi sẽ không chịu trách nhiệm cho bất kỳ thiệt hại nào phát
          sinh từ việc sử dụng ứng dụng này.
        </p>
        <p>
          5. Thay đổi điều khoản: Chúng tôi có quyền thay đổi các điều khoản này bất cứ lúc nào mà
          không cần thông báo trước.
        </p>
      </div>
    ),
    showRetryButton: true,
    showCloseButton: true,
    retryButtonProps: {
      children: 'Đồng ý',
      onClick: () => console.log('Terms accepted'),
    },
  },
};

// Interactive Stories
export const Interactive: Story = {
  render: (args) => <InteractiveModal {...args} />,
  args: {
    title: 'Modal tương tác',
    content: 'Đây là một modal có thể tương tác. Nhấn nút để đóng.',
    showRetryButton: true,
    showCloseButton: true,
  },
};

export const WithoutRetryButton: Story = {
  render: (args) => <InteractiveModal {...args} />,
  args: {
    title: 'Chỉ thông báo',
    content: 'Đây là modal chỉ hiển thị thông báo, không có nút thao tác.',
    showRetryButton: false,
    showCloseButton: true,
  },
};

export const MaskClosable: Story = {
  render: (args) => <InteractiveModal {...args} />,
  args: {
    title: 'Có thể đóng bằng mask',
    content: 'Bạn có thể đóng modal này bằng cách click vào vùng tối bên ngoài.',
    showRetryButton: true,
    showCloseButton: false,
    maskClosable: true,
  },
};

