import React, { useState } from "react";
import { motion } from "motion/react";

export const RippleEffect = ({ children }) => {
  const [ripples, setRipples] = useState<any>([]);

  const handleRipple = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    const newRipple = {
      key: Date.now(),
      x,
      y,
      size,
    };

    setRipples((prev) => [...prev, newRipple]);

    // Remove ripple after animation ends
    setTimeout(() => {
      setRipples((prev) => prev.filter((r) => r.key !== newRipple.key));
    }, 600);
  };

  return (
    <div
      onClick={handleRipple}
      className="relative overflow-hidden"
      style={{
        display: "inline-block",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {children}
      <div className="absolute inset-0 pointer-events-none">
        {ripples.map((ripple) => (
          <motion.div
            key={ripple.key}
            className="absolute rounded-full bg-white opacity-30"
            style={{
              width: ripple.size,
              height: ripple.size,
              top: ripple.y,
              left: ripple.x,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          />
        ))}
      </div>
    </div>
  );
};
