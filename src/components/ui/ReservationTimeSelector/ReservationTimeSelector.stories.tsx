import type { Meta, StoryObj } from '@storybook/react';
import { ReservationTimeSelector } from './ReservationTimeSelector';

const meta: Meta<typeof ReservationTimeSelector> = {
  title: 'UI/ReservationTimeSelector',
  component: ReservationTimeSelector,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onTimeSelect: { action: 'time-selected' },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockTimeSlots = [
  { id: '1', time: '12:00', available: true },
  { id: '2', time: '12:00', available: true },
  { id: '3', time: '12:30', available: true },
  { id: '4', time: '12:45', available: true },
  { id: '5', time: '13:00', available: true },
];

const mockTimeSlotsWithUnavailable = [
  { id: '1', time: '12:00', available: true },
  { id: '2', time: '12:15', available: false },
  { id: '3', time: '12:30', available: true },
  { id: '4', time: '12:45', available: false },
  { id: '5', time: '13:00', available: true },
  { id: '6', time: '13:15', available: true },
  { id: '7', time: '13:30', available: true },
  { id: '8', time: '13:45', available: false },
];

const manyTimeSlots = [
  { id: '1', time: '09:00', available: true },
  { id: '2', time: '09:30', available: true },
  { id: '3', time: '10:00', available: false },
  { id: '4', time: '10:30', available: true },
  { id: '5', time: '11:00', available: true },
  { id: '6', time: '11:30', available: true },
  { id: '7', time: '12:00', available: true },
  { id: '8', time: '12:30', available: false },
  { id: '9', time: '13:00', available: true },
  { id: '10', time: '13:30', available: true },
  { id: '11', time: '14:00', available: true },
  { id: '12', time: '14:30', available: false },
  { id: '13', time: '15:00', available: true },
  { id: '14', time: '15:30', available: true },
  { id: '15', time: '16:00', available: true },
  { id: '16', time: '16:30', available: false },
  { id: '17', time: '17:00', available: true },
  { id: '18', time: '17:30', available: true },
];

export const Default: Story = {
  args: {
    timeSlots: mockTimeSlots,
  },
};

export const WithSelection: Story = {
  args: {
    timeSlots: mockTimeSlots,
    selectedTimeId: '3',
  },
};

export const WithUnavailableSlots: Story = {
  args: {
    timeSlots: mockTimeSlotsWithUnavailable,
    selectedTimeId: '1',
  },
};

export const ManyTimeSlots: Story = {
  args: {
    timeSlots: manyTimeSlots,
    selectedTimeId: '7',
    showScrollButtons: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Many time slots with scroll buttons overlaid on the list (no margin)',
      },
    },
  },
};

export const WithoutScrollButtons: Story = {
  args: {
    timeSlots: manyTimeSlots,
    selectedTimeId: '7',
    showScrollButtons: false,
  },
};

export const CustomScrollAmount: Story = {
  args: {
    timeSlots: manyTimeSlots,
    selectedTimeId: '7',
    showScrollButtons: true,
    scrollAmount: 100,
  },
};

export const OverflowTest: Story = {
  args: {
    timeSlots: manyTimeSlots,
    selectedTimeId: '1',
    showScrollButtons: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Test layout with many time slots to see scroll buttons overlay on the list',
      },
    },
  },

  decorators: [
    (Story) => (
      <div className="w-[400px]">
        <Story />
      </div>
    ),
  ],
};

export const AutoDetectScrollButtons: Story = {
  args: {
    timeSlots: manyTimeSlots,
    selectedTimeId: '7',
    // showScrollButtons is not provided - should auto-detect based on overflow
  },
  parameters: {
    docs: {
      description: {
        story: 'Auto-detects scroll buttons based on overflow (showScrollButtons not provided). Should show scroll buttons due to overflow.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="w-[300px]">
        <Story />
      </div>
    ),
  ],
};

export const AutoDetectNoOverflow: Story = {
  args: {
    timeSlots: mockTimeSlots,
    selectedTimeId: '3',
    // showScrollButtons is not provided - should auto-detect based on overflow
  },
  parameters: {
    docs: {
      description: {
        story: 'Auto-detects scroll buttons based on overflow (showScrollButtons not provided). Should NOT show scroll buttons as there is no overflow.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="w-[500px]">
        <Story />
      </div>
    ),
  ],
};

export const DragScrolling: Story = {
  args: {
    timeSlots: manyTimeSlots,
    selectedTimeId: '7',
    showScrollButtons: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Supports drag scrolling on both desktop (mouse) and mobile (touch). Try dragging/swiping the time slots to scroll. Click vs drag is intelligently detected.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="w-[300px]">
        <Story />
      </div>
    ),
  ],
};

export const MobileFriendly: Story = {
  args: {
    timeSlots: manyTimeSlots,
    selectedTimeId: '5',
    // Auto-detect scroll buttons + drag support for best mobile experience
  },
  parameters: {
    docs: {
      description: {
        story: 'Mobile-optimized with auto-detection and drag scrolling. Try dragging on mobile or with developer tools mobile mode.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="w-[250px] border border-gray-200 p-4 rounded-lg">
        <div className="mb-2 text-sm text-gray-600">Mobile container (250px)</div>
        <Story />
      </div>
    ),
  ],
};
