import React from 'react';
import { LucideIcon } from 'lucide-react';

interface ScrollButtonProps {
  onClick: () => void;
  direction: 'left' | 'right';
  Icon: LucideIcon;
  ariaLabel: string;
}

export const ScrollButton: React.FC<ScrollButtonProps> = ({
  onClick,
  direction,
  Icon,
  ariaLabel,
}) => {
  return (
    <button
      onClick={onClick}
      className={`absolute ${direction === 'left' ? 'left-2' : 'right-2'} top-1/2 z-10 flex h-5 w-5 -translate-y-1/2 items-center justify-center rounded-full border-[1.5px] border-[#E9EBED] bg-white shadow-md transition-all duration-200 hover:bg-gray-50 hover:shadow-lg`}
      aria-label={ariaLabel}
    >
      <Icon size={16} className="text-black" />
    </button>
  );
};
