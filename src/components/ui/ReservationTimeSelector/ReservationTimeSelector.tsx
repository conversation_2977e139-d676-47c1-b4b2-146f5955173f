import React, { useRef, useState, useEffect, useCallback } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { ScrollButton } from './ScrollButton';

export interface TimeSlot {
  id: string;
  time: string;
  available?: boolean;
}

export interface ReservationTimeSelectorProps {
  timeSlots: TimeSlot[];
  selectedTimeId?: string;
  className?: string;
  showScrollButtons?: boolean;
  scrollAmount?: number;
  onTimeSelect?: (timeSlot: TimeSlot) => void;
}

export const ReservationTimeSelector: React.FC<ReservationTimeSelectorProps> = ({
  timeSlots,
  selectedTimeId,
  onTimeSelect,
  className = '',
  showScrollButtons,
  scrollAmount = 200,
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [hasOverflow, setHasOverflow] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });
  const [hasDragged, setHasDragged] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  const checkScrollPosition = () => {
    const container = scrollContainerRef.current;
    if (container) {
      const { scrollLeft, scrollWidth, clientWidth } = container;
      const scrollRight = scrollWidth - clientWidth - scrollLeft;

      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollRight > 1); // 1px threshold to account for rounding
    }
  };

  const checkOverflow = useCallback(() => {
    const container = scrollContainerRef.current;

    if (container) {
      const isOverflowing = container.scrollWidth > container.clientWidth;
      setHasOverflow(isOverflowing);
      checkScrollPosition();
    }
  }, []);

  const handleDragMove = useCallback(
    (clientX: number) => {
      if (!isDragging || !scrollContainerRef.current) return;

      const container = scrollContainerRef.current;
      const deltaX = clientX - dragStart.x;

      if (Math.abs(deltaX) > 5) {
        setHasDragged(true);
      }

      container.scrollLeft = dragStart.scrollLeft - deltaX;
      checkScrollPosition();
    },
    [isDragging, dragStart],
  );

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    checkOverflow();

    const container = scrollContainerRef.current;

    if (container) {
      const resizeObserver = new ResizeObserver(checkOverflow);

      const handleScroll = () => {
        checkScrollPosition();
      };

      resizeObserver.observe(container);
      container.addEventListener('scroll', handleScroll);

      return () => {
        resizeObserver.disconnect();
        container.removeEventListener('scroll', handleScroll);
      };
    }

    return undefined;
  }, [timeSlots, checkOverflow]);

  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        handleDragMove(e.clientX);
      }
    };

    const handleGlobalMouseUp = () => {
      if (isDragging) {
        handleDragEnd();
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging, handleDragMove, handleDragEnd]);

  const shouldShowScrollButtons = showScrollButtons !== undefined ? showScrollButtons : hasOverflow;

  const handleTimeClick = (timeSlot: TimeSlot, e: React.MouseEvent) => {
    if (hasDragged) {
      e.preventDefault();
      return;
    }

    if (timeSlot.available !== false && onTimeSelect) {
      onTimeSelect(timeSlot);
    }
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  const handleDragStart = (clientX: number) => {
    const container = scrollContainerRef.current;
    if (container) {
      setIsDragging(true);
      setHasDragged(false);
      setDragStart({
        x: clientX,
        scrollLeft: container.scrollLeft,
      });
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    handleDragStart(e.clientX);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (e.touches.length === 1) {
      handleDragStart(e.touches[0].clientX);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (e.touches.length === 1) {
      e.preventDefault();
      handleDragMove(e.touches[0].clientX);
    }
  };

  const handleTouchEnd = () => {
    handleDragEnd();
  };

  return (
    <div className={`flex flex-col gap-[10px] ${className}`}>
      <div className="relative flex items-center">
        {/* Left scroll button */}
        {shouldShowScrollButtons && canScrollLeft && (
          <ScrollButton
            onClick={scrollLeft}
            direction="left"
            Icon={ChevronLeft}
            ariaLabel="Scroll left"
          />
        )}

        {/* Scrollable time slots container */}
        <div
          ref={scrollContainerRef}
          className={`flex flex-row gap-2 overflow-x-auto scrollbar-hide ${isDragging ? 'cursor-grabbing select-none' : 'cursor-grab'}`}
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {timeSlots.map((timeSlot) => (
            <button
              key={timeSlot.id}
              onClick={(e) => handleTimeClick(timeSlot, e)}
              disabled={timeSlot.available === false}
              className={`flex h-8 w-16 flex-shrink-0 items-center justify-center rounded-sm text-center font-roboto text-normal transition-all duration-200 ${
                selectedTimeId === timeSlot.id
                  ? 'bg-primary text-black'
                  : timeSlot.available === false
                    ? 'bg-gray-200 cursor-not-allowed text-gray-400'
                    : 'bg-primary text-black hover:bg-main-primary'
              } `}
            >
              {timeSlot.time}
            </button>
          ))}
        </div>

        {/* Right scroll button */}
        {shouldShowScrollButtons && canScrollRight && (
          <ScrollButton
            onClick={scrollRight}
            direction="right"
            Icon={ChevronRight}
            ariaLabel="Scroll right"
          />
        )}
      </div>
    </div>
  );
};
