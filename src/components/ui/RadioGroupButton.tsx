// Libraries
import clsx from "clsx";
import React from "react";
import styled from "styled-components";

interface RadioGroupButtonProps {
  options?: { label: string; value: string }[];
  value?: string;
  onChange?: (value: string) => void;
}

const RadioGroupButtonWrapper = styled.div`
  display: flex;
  align-items: center;
  border-radius: 100px;
  background-color: #ffffff;
  width: fit-content;

  .radio-group-button {
    display: flex;
    align-items: center;
    border-radius: 100px;
    padding: 0px 20px;
    height: 40px;
    color: var(--hl-color-text-description);
    cursor: pointer;

    &--active {
      background-color: var(--color-main-primary);
      color: #ffffff;
    }
  }
`;

export const RadioGroupButton: React.FC<RadioGroupButtonProps> = (props) => {
  const { options, value, onChange } = props;

  const renderOptions = () => {
    return options?.map((option) => {
      return (
        <div
          key={option.value}
          className={clsx("radio-group-button", {
            "radio-group-button--active": option.value === value,
          })}
          onClick={() => onChange?.(option.value)}
        >
          {option.label}
        </div>
      );
    });
  };

  return <RadioGroupButtonWrapper>{renderOptions()}</RadioGroupButtonWrapper>;
};
