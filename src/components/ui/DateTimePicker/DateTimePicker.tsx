import React, { useCallback, useEffect, useState } from 'react';
import { DatePicker } from '@antscorp/ama-ui';
import dayjs from 'dayjs';
import 'dayjs/locale/vi';
import styled from 'styled-components';
import { CloseIcon } from 'components/icons';

// Set Vietnamese locale for dayjs
dayjs.locale('vi');

export interface DateTimeValue {
  date: Date;
}

export interface DateTimePickerProps {
  visible?: boolean;
  onClose?: (selectedValue?: DateTimeValue) => void;
  value?: DateTimeValue;
  minDate?: Date;
  maxDate?: Date;
  title?: string;
  precision?: 'year' | 'month' | 'day' | 'hour' | 'minute';
  children?: (date: Date | null) => React.ReactNode;
}

const StyledDatePicker = styled(DatePicker)`
  &.adm-picker {
    --item-font-size: 20px;
    --item-height: 40px;

    .adm-picker-header {
      padding: 16px 24px;

      .adm-picker-header-button {
        display: none;
      }

      .adm-picker-header-title {
        padding: 0;
        position: relative;
      }
    }

    .adm-picker-view {
      padding-left: 24px;
      padding-right: 24px;

      .adm-picker-view-column:nth-child(1) {
        display: none;
      }
    }
  }
`;

export const DateTimePicker: React.FC<DateTimePickerProps> = ({
  visible,
  value,
  minDate,
  maxDate,
  title = 'Chọn ngày, giờ',
  children,
  onClose,
}) => {
  // Local state để track current selected date khi user đang scroll
  const [currentSelectedDate, setCurrentSelectedDate] = useState<Date>(value?.date || new Date());

  useEffect(() => {
    if (value?.date) {
      setCurrentSelectedDate(value.date);
    }
  }, [value?.date]);

  const handleClose = useCallback(() => {
    onClose?.({ date: currentSelectedDate });
  }, [onClose, currentSelectedDate]);

  // Handle onSelect để update local state real-time
  const handleSelect = useCallback((selectedDate: Date) => {
    setCurrentSelectedDate(selectedDate);
  }, []);

  // Custom label rendering to match UI format "Th 2, 5 thg 1"
  const renderLabel = useCallback(
    (type: string, data: number) => {
      if (type === 'day') {
        // Sử dụng currentSelectedDate thay vì value?.date để có real-time update
        const year = currentSelectedDate.getFullYear();
        const month = currentSelectedDate.getMonth();
        const date = new Date(year, month, data); // month đã đúng index
        const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.

        // Vietnamese day names: Th 2 = Monday, Th 3 = Tuesday, etc.
        const vietnameseDays = ['CN', 'Th 2', 'Th 3', 'Th 4', 'Th 5', 'Th 6', 'Th 7'];
        const dayName = vietnameseDays[dayOfWeek];

        return `${dayName}, ${data}`;
      }

      if (type === 'month') {
        return `thg ${data}`;
      }

      return data.toString();
    },
    [currentSelectedDate],
  );

  return (
    <StyledDatePicker
      title={
        <>
          <span className="text-[20px] font-semibold">{title}</span>

          <button
            onClick={() => handleClose()}
            className="absolute -right-2.5 top-1/2 -translate-y-1/2"
          >
            <CloseIcon className="text-black" width={20} height={20} />
          </button>
        </>
      }
      visible={visible}
      value={value?.date}
      min={minDate}
      max={maxDate}
      precision="minute"
      mouseWheel
      renderLabel={renderLabel}
      confirmText={null}
      cancelText={null}
      filter={{
        year: (value) => dayjs().year() === value,
        minute: (value) => value % 15 === 0,
      }}
      onSelect={handleSelect}
      onClose={handleClose}
    >
      {children}
    </StyledDatePicker>
  );
};
