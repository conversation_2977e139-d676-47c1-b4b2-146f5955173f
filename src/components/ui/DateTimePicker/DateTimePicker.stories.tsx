/* eslint-disable react-hooks/rules-of-hooks */
import { Meta, StoryObj } from '@storybook/react';
import { DateTimePicker } from './DateTimePicker';
import { useState } from 'react';
import { Button } from '../Button';

const meta: Meta<typeof DateTimePicker> = {
  title: 'UI/DateTimePicker',
  component: DateTimePicker,
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => {
    const [visible, setVisible] = useState(false);

    return (
      <DateTimePicker onClose={() => setVisible(false)} visible={visible}>
        {(date) => (
          <Button color="primary" onClick={() => setVisible(true)}>
            Choose Date Time{date ? `: ${date?.toLocaleString()}` : ''}
          </Button>
        )}
      </DateTimePicker>
    );
  },
};
