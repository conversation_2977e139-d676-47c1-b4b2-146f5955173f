import { Meta, StoryObj } from '@storybook/react/*';
import { Tabs } from './Tabs';

const meta = {
  title: 'ui/Tabs',
  component: Tabs,
} satisfies Meta<typeof Tabs>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    items: [
      {
        key: 'all',
        title: 'Tất cả',
        content: 'Tất cả Content',
      },
      {
        key: 'food',
        title: 'Món ăn',
        content: 'Món ăn Content',
      },
      {
        key: 'atmosphere',
        title: 'Không gian',
        content: 'Không gian Content',
      },
    ],
    activeKey: 'all',
  },
};
