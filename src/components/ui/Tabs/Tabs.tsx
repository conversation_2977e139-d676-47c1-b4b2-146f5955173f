import { Tabs as AMATabs } from '@antscorp/ama-ui';
import styled from 'styled-components';

type TabBarProps = {
  items?: {
    key: string;
    title: React.ReactNode;
    content?: React.ReactNode;
  }[];
  activeKey?: string;
  onChange?: (key: string) => void;
};

export const StyledAMATabs = styled(AMATabs)`
  --active-line-height: 2px;

  .adm-tabs-tab {
    color: var(--adm-color-text-secondary);
    font-size: var(--adm-font-size-main);
    line-height: 22px;
    padding: 11px 0 11px;

    &.adm-tabs-tab-active {
      color: var(--color-text-primary);
      font-weight: 500;
    }
  }
`;

export const Tabs = (props: TabBarProps) => {
  const { items = [], activeKey, onChange } = props;

  return (
    <StyledAMATabs activeKey={activeKey} onChange={onChange} activeLineMode="full">
      {items.map((item) => (
        <StyledAMATabs.Tab key={item.key} title={item.title}>
          {item.content}
        </StyledAMATabs.Tab>
      ))}
    </StyledAMATabs>
  );
};
