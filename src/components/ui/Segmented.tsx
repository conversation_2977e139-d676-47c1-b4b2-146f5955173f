// Libraries
import React from "react";
import {
  Segmented as AMASegmented,
  SegmentedProps as AMASegmentedProps,
} from "@antscorp/ama-ui";
import styled from "styled-components";

const StyledSegmented = styled(AMASegmented)`
  --segmented-background: #fff;
  --segmented-item-color: var(--color-text-tertiary);
  --segmented-item-selected-background: var(--color-main-primary);
  --segmented-item-selected-color: #fff;

  border-radius: 50px;
  border: 1px solid var(--color-main-primary);
  padding: 0px;

  .adm-segmented-item,
  .adm-segmented-thumb {
    border-radius: 50px;
  }

  .adm-segmented-item-label {
    /* font-family: "Red Rose", sans-serif; */
    font-weight: 600;
    font-size: 14px;
    padding: 0 28px;
    line-height: 40px;
  }
` as React.FC<AMASegmentedProps>;

export const Segmented: React.FC<AMASegmentedProps> = (props) => {
  return <StyledSegmented {...props} />;
};
