// Libraries
import React from "react";

interface HomeIconProps extends React.SVGProps<SVGSVGElement> {}

export const HomeIcon: React.FC<HomeIconProps> = (props) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.0399 18.9628H18.7638V10.3559L12.0859 5.16239L5.40797 10.3559V18.9628H11.1319V13.2388H13.0399V18.9628ZM20.6718 19.9167C20.6718 20.1698 20.5712 20.4124 20.3923 20.5913C20.2134 20.7702 19.9708 20.8707 19.7178 20.8707H4.45399C4.20097 20.8707 3.95832 20.7702 3.77942 20.5913C3.60051 20.4124 3.5 20.1698 3.5 19.9167V9.89035C3.4999 9.74498 3.53302 9.6015 3.59684 9.47088C3.66067 9.34027 3.75349 9.22596 3.86824 9.1367L11.5001 3.201C11.6676 3.07073 11.8737 3 12.0859 3C12.298 3 12.5042 3.07073 12.6716 3.201L20.3035 9.1367C20.4183 9.22596 20.5111 9.34027 20.5749 9.47088C20.6387 9.6015 20.6719 9.74498 20.6718 9.89035V19.9167Z"
        fill="currentColor"
      />
    </svg>
  );
};
