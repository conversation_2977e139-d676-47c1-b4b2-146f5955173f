// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const ShoppingBagIcon: React.FC<IconProps> = (props) => {
  const iconProps = useIconProps(props);

  return (
    <svg
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <path
        d="M5.87395 7.4418V5.77037C5.87395 4.66214 6.29726 3.59931 7.05076 2.81567C7.80425 2.03204 8.82621 1.5918 9.89181 1.5918C10.9574 1.5918 11.9794 2.03204 12.7329 2.81567C13.4864 3.59931 13.9097 4.66214 13.9097 5.77037V7.4418H16.3204C16.5335 7.4418 16.7379 7.52984 16.8886 7.68657C17.0393 7.8433 17.124 8.05587 17.124 8.27751V18.3061C17.124 18.5277 17.0393 18.7403 16.8886 18.897C16.7379 19.0537 16.5335 19.1418 16.3204 19.1418H3.46324C3.25012 19.1418 3.04573 19.0537 2.89503 18.897C2.74433 18.7403 2.65967 18.5277 2.65967 18.3061V8.27751C2.65967 8.05587 2.74433 7.8433 2.89503 7.68657C3.04573 7.52984 3.25012 7.4418 3.46324 7.4418H5.87395ZM5.87395 9.11322H4.26681V17.4704H15.5168V9.11322H13.9097V10.7847H12.3025V9.11322H7.4811V10.7847H5.87395V9.11322ZM7.4811 7.4418H12.3025V5.77037C12.3025 5.10543 12.0485 4.46773 11.5964 3.99755C11.1443 3.52737 10.5312 3.26323 9.89181 3.26323C9.25245 3.26323 8.63927 3.52737 8.18718 3.99755C7.73508 4.46773 7.4811 5.10543 7.4811 5.77037V7.4418Z"
        fill="currentColor"
      />
    </svg>
  );
};
