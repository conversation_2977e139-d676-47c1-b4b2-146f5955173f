// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const AllIcon: React.FC<IconProps> = (props) => {
  const iconProps = useIconProps(props);

  return (
    <svg
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <path
        d="M2.73291 5.88411L9.6617 1.26491C9.79653 1.17496 9.95497 1.12695 10.1171 1.12695C10.2791 1.12695 10.4376 1.17496 10.5724 1.26491L17.5012 5.88411V17.3706C17.5012 17.5882 17.4148 17.7968 17.2609 17.9507C17.107 18.1046 16.8983 18.191 16.6807 18.191H3.55337C3.33577 18.191 3.12708 18.1046 2.97322 17.9507C2.81935 17.7968 2.73291 17.5882 2.73291 17.3706V5.88411ZM4.37383 6.762V16.5501H15.8603V6.762L10.1171 2.93373L4.37383 6.762ZM10.1171 9.16595C9.68186 9.16595 9.26448 8.99307 8.95675 8.68533C8.64902 8.3776 8.47613 7.96023 8.47613 7.52503C8.47613 7.08983 8.64902 6.67245 8.95675 6.36472C9.26448 6.05699 9.68186 5.88411 10.1171 5.88411C10.5523 5.88411 10.9696 6.05699 11.2774 6.36472C11.5851 6.67245 11.758 7.08983 11.758 7.52503C11.758 7.96023 11.5851 8.3776 11.2774 8.68533C10.9696 8.99307 10.5523 9.16595 10.1171 9.16595Z"
        fill="currentColor"
      />
    </svg>
  );
};
