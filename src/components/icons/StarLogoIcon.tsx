// Libraries
import React from "react";

interface StarLogoIconProps extends React.SVGProps<SVGSVGElement> {}

export const StarLogoIcon: React.FC<StarLogoIconProps> = (props) => {
  const { ...restProps } = props;

  return (
    <svg
      viewBox="0 0 54 57"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
      {...restProps}
    >
      <path d="M22.2887 3.11002C24.6835 0.00782895 29.3647 0.00782943 31.7595 3.11002C33.2651 5.06042 35.8126 5.88816 38.1771 5.19525C41.938 4.09316 45.7251 6.84466 45.8391 10.762C45.9108 13.2249 47.4853 15.392 49.8055 16.2212C53.4959 17.5402 54.9424 21.9922 52.7321 25.2284C51.3424 27.2631 51.3424 29.9417 52.7321 31.9764C54.9424 35.2126 53.4959 39.6646 49.8055 40.9836C47.4853 41.8129 45.9108 43.9799 45.8391 46.4428C45.7251 50.3602 41.938 53.1117 38.1771 52.0096C35.8126 51.3167 33.2651 52.1444 31.7595 54.0948C29.3647 57.197 24.6835 57.197 22.2887 54.0948C20.7831 52.1444 18.2356 51.3167 15.871 52.0096C12.1102 53.1117 8.32306 50.3602 8.20904 46.4428C8.13735 43.9799 6.56291 41.8129 4.2427 40.9836C0.552298 39.6646 -0.89425 35.2126 1.31607 31.9764C2.70574 29.9417 2.70574 27.2631 1.31607 25.2284C-0.89425 21.9922 0.552299 17.5402 4.2427 16.2212C6.56291 15.392 8.13735 13.2249 8.20904 10.762C8.32306 6.84466 12.1102 4.09316 15.871 5.19525C18.2356 5.88816 20.7831 5.06042 22.2887 3.11002Z" />
    </svg>
  );
};
