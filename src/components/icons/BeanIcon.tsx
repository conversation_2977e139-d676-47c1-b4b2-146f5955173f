// Libraries
import React from "react";
import { motion } from "motion/react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

interface BeanIconProps extends IconProps {
  animated?: boolean;
}

export const BeanIcon: React.FC<BeanIconProps> = ({
  size = 46,
  animated = false,
  ...props
}) => {
  const iconProps = useIconProps({
    ...props,
    size,
  });

  return (
    <svg
      viewBox="0 0 46 46"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <g clipPath="url(#clip0_6027_545)">
        <path
          d="M18.8454 42.1904C15.2747 42.1904 11.6904 40.8559 8.41728 37.5827C-0.0586605 29.1068 4.45883 18.557 11.5056 11.5057C18.5568 4.45896 29.1112 -0.0585384 37.5826 8.4174C46.0541 16.8888 41.5411 27.4432 34.4898 34.4899C30.1617 38.8181 24.5171 42.1904 18.8409 42.1904H18.8454ZM27.1139 6.74475C21.059 6.74475 15.5948 11.5688 13.5795 13.5841C10.6445 16.5191 1.74924 26.7714 10.4912 35.5133C19.2286 44.2508 29.4854 35.3556 32.4204 32.4205C35.3554 29.4855 44.2507 19.2332 35.5132 10.4913C32.7721 7.75014 29.8821 6.74475 27.1184 6.74475H27.1139Z"
          fill="currentColor"
        />
        <path
          d="M9.70673 38.7189C8.56157 37.2897 8.18286 35.2474 8.66527 33.1149C9.28293 30.3917 11.8798 25.0672 22.3575 20.0042C33.0156 14.851 36.0678 12.8628 36.5502 10.7573L39.4086 11.4111C38.6016 14.9367 35.139 17.0872 23.6334 22.6462C13.9131 27.344 11.9249 31.9877 11.5236 33.7641C11.2396 35.022 11.4109 36.1626 11.9925 36.8885L9.70222 38.7189H9.70673Z"
          fill="currentColor"
        />
        <path
          d="M11.1223 39.2464L10.5858 36.3654C23.9895 33.8723 37.4879 19.8238 36.5276 12.1865L39.4356 11.8213C40.0127 16.4109 36.8883 22.5424 31.0723 28.2141C25.1798 33.9624 17.9076 37.9885 11.1178 39.2509L11.1223 39.2464Z"
          fill="currentColor"
        />
        <path
          d="M25.9688 38.6242C23.3313 38.6242 21.2349 37.5783 19.7516 36.8389C19.3098 36.618 18.9265 36.4286 18.6154 36.3069L18.0699 36.095L17.822 35.563C16.1087 31.8886 16.1177 24.5623 20.6172 20.9194L22.4612 23.2007C19.4585 25.6308 19.188 30.8832 20.2746 33.8317C20.527 33.9489 20.793 34.0797 21.0635 34.2149C23.3403 35.3511 26.1717 36.7622 30.2338 34.4404L31.69 36.9876C29.571 38.1959 27.6595 38.6242 25.9733 38.6242H25.9688Z"
          fill="currentColor"
        />
        <path
          d="M30.0534 32.2356C28.6738 32.2356 27.3258 32.0012 26.104 31.5594L25.342 31.2844L25.1707 30.4954C23.7325 23.8769 25.1437 18.9447 29.1472 16.6138L30.6215 19.1475C29.057 20.0582 26.6901 22.5424 27.8713 29.0346C30.8875 29.765 35.7566 29.1068 38.1371 24.2556L40.7701 25.5496C38.4797 30.2113 34.1201 32.2356 30.0534 32.2402V32.2356Z"
          fill="currentColor"
        />
        <motion.path
          {...(animated && {
            initial: { opacity: 0 },
            animate: { opacity: 1 },
            transition: {
              repeat: Infinity,
              repeatType: "reverse",
              duration: 2,
            },
          })}
          d="M0.135254 5.62207C2.27678 6.76272 4.0396 8.52553 5.18024 10.6716C5.27492 10.8519 5.52739 10.8519 5.62658 10.6716C6.76723 8.53004 8.53004 6.76723 10.6761 5.62658C10.8564 5.5319 10.8564 5.27943 10.6761 5.18024C8.53004 4.0441 6.76723 2.28129 5.62658 0.135254C5.5319 -0.0450848 5.27943 -0.0450848 5.18024 0.135254C4.0441 2.27678 2.28129 4.0396 0.135254 5.18024C-0.0450848 5.27492 -0.0450848 5.52739 0.135254 5.62658V5.62207Z"
          fill="currentColor"
        />
        <motion.path
          {...(animated && {
            initial: { opacity: 0 },
            animate: { opacity: 1 },
            transition: {
              repeat: Infinity,
              repeatType: "reverse",
              duration: 2,
              delay: 0.5,
            },
          })}
          d="M35.3285 40.8153C37.47 41.9559 39.2328 43.7188 40.3735 45.8648C40.4682 46.0451 40.7206 46.0451 40.8198 45.8648C41.9605 43.7233 43.7233 41.9605 45.8693 40.8198C46.0497 40.7251 46.0497 40.4727 45.8693 40.3735C43.7278 39.2328 41.965 37.47 40.8243 35.324C40.7296 35.1436 40.4772 35.1436 40.378 35.324C39.2373 37.4655 37.4745 39.2283 35.3285 40.369C35.1482 40.4636 35.1482 40.7161 35.3285 40.8153Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_6027_545">
          <rect width="46" height="46" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
