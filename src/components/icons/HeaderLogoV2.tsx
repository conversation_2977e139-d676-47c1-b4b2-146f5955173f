// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const HeaderLogoV2: React.FC<IconProps> = ({
  width = 188,
  height = 164,
  ...props
}) => {
  const iconProps = useIconProps({
    ...props,
    width,
    height,
  });

  return (
    <svg
      viewBox="0 0 188 164"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <g clipPath="url(#clip0_2194_1850)">
        <path
          d="M31.4851 108.947C30.8044 110.334 30.66 111.939 31.0416 113.461C33.4036 122.872 47.9671 136.403 52.5157 140.441C52.8251 140.72 53.3098 140.379 53.1448 139.996C44.8935 119.818 67.4093 115.024 71.7103 114.3C71.9784 114.258 72.1744 114.548 72.0197 114.786L67.5227 121.743C67.368 121.982 67.5949 122.282 67.8631 122.199L98.9705 112.964C99.2387 112.881 99.4553 113.171 99.3212 113.409L95.67 119.466C95.5359 119.694 95.7112 119.973 95.9691 119.942C105.778 118.824 141.692 114.02 141.692 114.02C141.692 114.02 146.828 113.596 146.57 113.544C140.392 112.322 100.146 103.056 100.146 103.056C46.9254 88.5933 34.3009 103.222 31.4748 108.968L31.4851 108.947Z"
          fill="currentColor"
        />
        <path
          d="M129.944 60.6813C130.49 60.9504 131.058 61.1782 131.635 61.3646C156.554 69.5641 178.936 63.166 184.103 61.4577C184.567 61.3024 184.66 60.6295 184.258 60.3293C181.102 57.9999 174.934 54.221 169.808 51.1669C164.475 47.9886 158.38 46.6737 152.346 47.4192L138.143 49.1481C137.865 49.1792 137.7 48.8168 137.906 48.6097L148.127 37.5218C148.282 37.3665 148.231 37.0869 148.035 37.0041L135.41 31.1858C133.77 30.43 131.955 30.2333 130.201 30.6164C109.501 35.0578 96.2474 42.3048 93.3697 43.982C93.1428 44.1062 93.1841 44.4582 93.4213 44.541C101.858 47.326 125.282 58.4554 129.954 60.6916L129.944 60.6813Z"
          fill="currentColor"
        />
        <path
          d="M36.3329 69.9682C40.4895 68.5498 71.5453 63.187 114.163 74.9169C114.483 75.01 114.71 74.5959 114.462 74.3578C114.411 74.306 114.349 74.2439 114.297 74.1818C113.183 73.395 111.605 72.0802 109.439 70.0613C108.944 69.6472 108.119 68.9639 107.614 68.5498C103.633 65.6613 97.0522 61.0336 89.43 56.3333C87.7591 55.2981 85.7994 54.8632 83.8604 55.1738C78.2495 56.0849 70.5036 58.7766 68.6264 59.2632C68.4098 59.315 68.2654 59.0251 68.4304 58.8595L77.9813 50.7428C78.0948 50.6392 78.0742 50.4529 77.9298 50.3804C71.8341 47.6058 66.347 45.4835 62.4998 44.0962C62.407 44.0651 62.3245 44.0755 62.242 44.1169C59.2818 45.8769 55.1355 48.5065 50.8036 51.7574C45.8012 55.7743 40.1182 61.4166 36.0028 69.4919C35.879 69.7404 36.0853 70.0303 36.3329 69.9475V69.9682Z"
          fill="currentColor"
        />
        <path
          d="M139.67 0.396484C62.7783 0.396484 0.233398 42.8539 0.233398 95.043C0.233398 147.232 62.7783 189.69 139.67 189.69C216.562 189.69 279.107 147.232 279.107 95.043C279.107 42.8539 216.552 0.396484 139.67 0.396484ZM59.9729 35.0685C60.3545 34.851 61.0146 34.5405 61.4271 34.5301H61.5612C61.5612 34.5301 67.0483 34.851 81.581 40.1621C83.8707 39.6755 86.0779 38.8059 88.1614 37.6153C95.2162 33.5983 114.04 24.1565 145.869 16.9405C147.715 16.516 149.623 16.7438 151.346 17.6031C159.05 21.4544 180.473 33.1428 212.931 58.3108L213.107 58.435C213.478 58.6731 222.194 64.1913 232.786 64.1913C237.5 64.1913 241.842 63.1042 245.751 60.9404C247.03 60.2364 248.577 60.5781 249.474 61.7997C253.507 67.3593 256.529 73.1673 258.499 79.1616C259.871 83.3442 259.283 88.0755 256.766 91.5437C255.415 93.4072 253.631 95.0948 251.413 96.6063C244.957 100.986 234.416 103.211 220.1 103.211C209.414 103.211 196.738 101.948 182.433 99.4534C157.256 95.0534 135.441 88.3032 132.966 87.5371C104.746 79.2858 80.8383 75.1033 61.9222 75.1033C44.0685 75.1033 30.2269 78.8407 20.7895 86.212C19.5105 87.2058 18.3863 88.1169 17.3961 88.9762C20.1706 68.1047 35.3118 48.8482 59.9729 35.0892V35.0685ZM140.031 174.067C83.2106 174.067 33.9194 149.241 21.0989 114.62C20.4594 112.902 20.2634 111.028 20.6348 109.227C21.3671 105.727 23.3989 100.82 28.8654 96.7202C36.1885 91.2228 47.6784 88.4378 63.0052 88.4378C80.8383 88.4378 103.581 92.2891 130.552 99.8675C135.771 101.462 182.649 115.418 220.853 115.418C237.685 115.418 250.475 112.633 258.86 107.156L258.891 107.135C259.861 106.504 261.078 107.435 260.789 108.595C251.919 145.503 200.812 174.057 140.031 174.057V174.067Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2194_1850">
          <rect
            width="187.767"
            height="163.022"
            fill="white"
            transform="translate(0.233398 0.396484)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
