import React, { useState } from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import styled from 'styled-components';

// Import all icons
import {
  AccountIcon,
  AccumulatePointsIcon,
  AirportShuttleIcon,
  AllIcon,
  AppsIcon,
  ArrowLeftIcon,
  BabyChangingStationIcon,
  BeanIcon,
  ChatIcon,
  CheckIcon,
  ClocheIcon,
  CloseIcon,
  CoffeeFinIcon,
  EditIcon,
  GiftCardIcon,
  GiftIcon,
  HeaderAccessory,
  HeaderLogo,
  HeaderLogoV2,
  HeaderTextLogo,
  HistoryIcon,
  HomeIcon,
  InfoIcon,
  LightningIcon,
  ListIcon,
  LocalAirportIcon,
  LuckyMoneyIcon,
  LunchDiningIcon,
  MapBinIcon,
  PhoneIcon,
  PublicIcon,
  RestaurantMapBinIcon,
  ShoppingBagIcon,
  ShoppingCartIcon,
  SportsEsportsIcon,
  StarLogoIcon,
  StorefrontIcon,
  SuccessIcon,
  VolumeOffIcon,
  VolumeUpIcon,
  ClockIcon,
  CalendarIcon,
  EmailIcon,
  UserBadgeIcon,
  UsersIcon,
} from './index';

const Container = styled.div`
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
`;

const Title = styled.h1`
  margin-bottom: 20px;
  color: #333;
  font-size: 24px;
`;

const IconGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
`;

const IconCard = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
  transition: box-shadow 0.2s;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
`;

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
  color: #333;
`;

const IconName = styled.span`
  font-size: 12px;
  text-align: center;
  color: #666;
  word-break: break-word;
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 20px;
`;

const PaginationButton = styled.button<{ disabled?: boolean }>`
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: ${(props) => (props.disabled ? '#f5f5f5' : '#fff')};
  color: ${(props) => (props.disabled ? '#999' : '#333')};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background: #f0f0f0;
  }
`;

const PageInfo = styled.span`
  color: #666;
  font-size: 14px;
`;

const SearchContainer = styled.div`
  margin-bottom: 20px;
`;

const SearchInput = styled.input`
  width: 100%;
  max-width: 400px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;

  &:focus {
    outline: none;
    border-color: #007acc;
  }
`;

// Icon data with components
const iconData = [
  { name: 'AccountIcon', component: AccountIcon },
  { name: 'AccumulatePointsIcon', component: AccumulatePointsIcon },
  { name: 'AirportShuttleIcon', component: AirportShuttleIcon },
  { name: 'AllIcon', component: AllIcon },
  { name: 'AppsIcon', component: AppsIcon },
  { name: 'ArrowLeftIcon', component: ArrowLeftIcon },
  { name: 'BabyChangingStationIcon', component: BabyChangingStationIcon },
  { name: 'BeanIcon', component: BeanIcon },
  { name: 'ChatIcon', component: ChatIcon },
  { name: 'CheckIcon', component: CheckIcon },
  { name: 'ClocheIcon', component: ClocheIcon },
  { name: 'CloseIcon', component: CloseIcon },
  { name: 'CoffeeFinIcon', component: CoffeeFinIcon },
  { name: 'EditIcon', component: EditIcon },
  { name: 'GiftCardIcon', component: GiftCardIcon },
  { name: 'GiftIcon', component: GiftIcon },
  { name: 'HeaderAccessory', component: HeaderAccessory },
  { name: 'HeaderLogo', component: HeaderLogo },
  { name: 'HeaderLogoV2', component: HeaderLogoV2 },
  { name: 'HeaderTextLogo', component: HeaderTextLogo },
  { name: 'HistoryIcon', component: HistoryIcon },
  { name: 'HomeIcon', component: HomeIcon },
  { name: 'InfoIcon', component: InfoIcon },
  { name: 'LightningIcon', component: LightningIcon },
  { name: 'ListIcon', component: ListIcon },
  { name: 'LocalAirportIcon', component: LocalAirportIcon },
  { name: 'LuckyMoneyIcon', component: LuckyMoneyIcon },
  { name: 'LunchDiningIcon', component: LunchDiningIcon },
  { name: 'MapBinIcon', component: MapBinIcon },
  { name: 'PhoneIcon', component: PhoneIcon },
  { name: 'PublicIcon', component: PublicIcon },
  { name: 'RestaurantMapBinIcon', component: RestaurantMapBinIcon },
  { name: 'ShoppingBagIcon', component: ShoppingBagIcon },
  { name: 'ShoppingCartIcon', component: ShoppingCartIcon },
  { name: 'SportsEsportsIcon', component: SportsEsportsIcon },
  { name: 'StarLogoIcon', component: StarLogoIcon },
  { name: 'StorefrontIcon', component: StorefrontIcon },
  { name: 'SuccessIcon', component: SuccessIcon },
  { name: 'VolumeOffIcon', component: VolumeOffIcon },
  { name: 'VolumeUpIcon', component: VolumeUpIcon },
  { name: 'ClockIcon', component: ClockIcon },
  { name: 'CalendarIcon', component: CalendarIcon },
  { name: 'EmailIcon', component: EmailIcon },
  { name: 'UserBadgeIcon', component: UserBadgeIcon },
  { name: 'UsersIcon', component: UsersIcon },
];

const ICONS_PER_PAGE = 12;

interface IconShowcaseProps {
  iconsPerPage?: number;
}

const IconShowcase: React.FC<IconShowcaseProps> = ({ iconsPerPage = ICONS_PER_PAGE }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter icons based on search term
  const filteredIcons = iconData.filter((icon) =>
    icon.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const totalPages = Math.ceil(filteredIcons.length / iconsPerPage);
  const startIndex = (currentPage - 1) * iconsPerPage;
  const endIndex = startIndex + iconsPerPage;
  const currentIcons = filteredIcons.slice(startIndex, endIndex);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  return (
    <Container>
      <Title>Icon Showcase - Golden Gate Group Mini App</Title>

      <SearchContainer>
        <SearchInput
          type="text"
          placeholder="Search icons by name..."
          value={searchTerm}
          onChange={handleSearchChange}
        />
      </SearchContainer>

      <IconGrid>
        {currentIcons.map(({ name, component: IconComponent }) => (
          <IconCard key={name}>
            <IconWrapper>
              <IconComponent />
            </IconWrapper>
            <IconName>{name}</IconName>
          </IconCard>
        ))}
      </IconGrid>

      {totalPages > 1 && (
        <PaginationContainer>
          <PaginationButton onClick={handlePreviousPage} disabled={currentPage === 1}>
            Previous
          </PaginationButton>

          <PageInfo>
            Page {currentPage} of {totalPages} ({filteredIcons.length} icons)
          </PageInfo>

          <PaginationButton onClick={handleNextPage} disabled={currentPage === totalPages}>
            Next
          </PaginationButton>
        </PaginationContainer>
      )}
    </Container>
  );
};

const meta = {
  title: 'Components/Icons/Icon Showcase',
  component: IconShowcase,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'A comprehensive showcase of all available icons in the Golden Gate Group Mini App with search and pagination functionality.',
      },
    },
    viewport: {
      defaultViewport: 'reset',
    },
  },
  argTypes: {
    iconsPerPage: {
      control: { type: 'number', min: 4, max: 24, step: 4 },
      description: 'Number of icons to display per page',
      defaultValue: ICONS_PER_PAGE,
    },
  },
} satisfies Meta<typeof IconShowcase>;

export default meta;
type Story = StoryObj<typeof IconShowcase>;

export const Default: Story = {
  args: {
    iconsPerPage: ICONS_PER_PAGE,
  },
};

export const CompactView: Story = {
  args: {
    iconsPerPage: 8,
  },
};

export const ExtendedView: Story = {
  args: {
    iconsPerPage: 20,
  },
};
