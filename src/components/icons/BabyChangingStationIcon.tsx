// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const BabyChangingStationIcon: React.FC<IconProps> = (props) => {
  const iconProps = useIconProps(props);

  return (
    <svg
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <g clipPath="url(#clip0_2194_2491)">
        <path
          d="M16.0601 4.14466C17.2596 5.34745 17.7108 7.01053 17.4138 8.55874C17.8798 8.69001 18.3213 8.93861 18.6888 9.30371C19.8407 10.4474 19.8407 12.3017 18.6888 13.4446L14.2198 17.8816L11.7403 15.4202L9.29701 17.8693L2.3395 10.902C0.614076 8.96897 0.678892 6.00054 2.53395 4.14466C4.3923 2.28714 7.36482 2.22478 9.29783 3.95759C11.2251 2.22724 14.2034 2.28467 16.0601 4.14466ZM10.9068 10.4688C10.4013 10.9701 10.4013 11.779 10.9068 12.2803L14.2198 15.5696L17.5328 12.2803C18.0382 11.779 18.0382 10.9701 17.5328 10.4688C17.0208 9.96008 16.1856 9.96008 15.672 10.4704L14.2181 11.9095L13.0637 10.7641L12.7659 10.4688C12.2539 9.96008 11.4187 9.96008 10.9068 10.4688ZM3.69491 5.30561C2.47242 6.5281 2.41088 8.48489 3.53738 9.77794L9.29701 15.5466L10.5761 14.265L9.75072 13.4446C8.5988 12.3017 8.5988 10.4474 9.75072 9.30371C10.9027 8.16081 12.77 8.16081 13.9219 9.30371L14.2198 9.59908L14.5176 9.30371C14.8663 8.9583 15.2806 8.71708 15.7196 8.58007C16.0609 7.4585 15.7836 6.19089 14.8983 5.30397C13.6676 4.07164 11.6821 4.02159 10.394 5.17844L9.29865 6.16135L8.20252 5.17926C6.91029 4.02077 4.92888 4.07164 3.69491 5.30561Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2194_2491">
          <rect
            width="19.6911"
            height="19.6911"
            fill="white"
            transform="translate(0.271484 0.242188)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
