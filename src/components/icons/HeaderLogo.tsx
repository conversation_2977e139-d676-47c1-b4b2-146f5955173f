// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const HeaderLogo: React.FC<IconProps> = ({
  width = 247,
  height = 169,
  ...props
}) => {
  const iconProps = useIconProps({
    ...props,
    width,
    height,
  });

  return (
    <svg
      viewBox="0 0 247 169"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <path
        d="M123.5 0C55.4021 0 0 37.837 0 84.3389C0 130.841 55.4021 168.678 123.5 168.678C191.598 168.678 247 130.841 247 84.3389C247 37.837 191.583 0 123.5 0ZM123.5 164C58.5503 164 5.70793 128.266 5.70793 84.3389C5.70793 40.4115 58.5503 4.67814 123.5 4.67814C188.45 4.67814 241.307 40.4115 241.307 84.3389C241.307 128.266 188.464 164 123.5 164Z"
        fill="currentColor"
      />
      <path
        d="M224.249 94.5202H224.234C217.143 99.1395 206.345 101.493 192.119 101.493C159.843 101.493 120.241 89.7391 115.827 88.4004C93.0399 82.0157 73.8272 78.7793 58.763 78.7793C45.8172 78.7793 36.1079 81.1184 29.9145 85.7524C25.2952 89.2095 23.574 93.3286 22.9561 96.2856C22.6472 97.8008 22.809 99.3749 23.3533 100.831C34.1954 129.974 75.8426 150.864 123.83 150.864C175.187 150.864 218.364 126.841 225.852 95.7707C226.087 94.785 225.058 94.0053 224.249 94.5349M125.228 100.302C125.228 100.302 94.8788 104.347 86.5965 105.289C86.3758 105.318 86.2287 105.068 86.3464 104.892L89.4357 99.8015C89.5534 99.6103 89.3622 99.3602 89.1415 99.4337L62.8527 107.216C62.6173 107.289 62.4408 107.025 62.5585 106.833L66.354 100.978C66.4864 100.787 66.3245 100.537 66.0891 100.566C62.4555 101.17 43.434 105.215 50.4071 122.192C50.5395 122.516 50.1423 122.81 49.8775 122.574C46.0379 119.176 33.7247 107.79 31.7387 99.8751C31.415 98.5952 31.5327 97.2418 32.1211 96.0796C34.5043 91.2543 45.1699 78.9411 90.1419 91.1072C90.1419 91.1072 124.139 98.9041 129.362 99.9339C129.582 99.9781 125.243 100.331 125.243 100.331"
        fill="currentColor"
      />
      <path
        d="M57.8321 67.5381C73.8084 67.5381 94.0068 71.0688 117.854 78.0124C119.943 78.6597 138.376 84.3382 159.648 88.0454C171.74 90.1491 182.435 91.2083 191.468 91.2083C203.561 91.2083 212.476 89.34 217.933 85.6475C219.802 84.3823 221.317 82.9554 222.45 81.3813C224.568 78.4538 225.068 74.4817 223.921 70.9511C222.259 65.9052 219.714 61.0211 216.301 56.3429C215.55 55.3131 214.241 55.0189 213.152 55.6221C209.857 57.4463 206.179 58.3583 202.207 58.3583C193.263 58.3583 185.893 53.7096 185.584 53.5184L185.437 53.4154C158.015 32.2314 139.92 22.3896 133.403 19.1532C131.962 18.4323 130.343 18.2411 128.784 18.5941C101.892 24.6698 85.9893 32.6139 80.0313 35.9974C78.2806 36.9978 76.4123 37.7333 74.4704 38.1305C62.1866 33.6583 57.5526 33.3935 57.5526 33.3935H57.4349C57.0819 33.3935 56.5375 33.6731 56.1992 33.8496C35.3682 45.4272 22.5695 61.6389 20.2305 79.204C21.069 78.4832 22.0105 77.7182 23.0991 76.8797C31.0726 70.6863 42.7679 67.5381 57.8469 67.5381M84.4005 41.3523C86.8278 39.94 98.0377 33.8496 115.515 30.0983C116.986 29.7893 118.53 29.9511 119.913 30.5837L130.579 35.4825C130.741 35.5561 130.785 35.7915 130.652 35.9239L122.017 45.2507C121.855 45.4273 121.987 45.7362 122.223 45.7068L134.227 44.2504C139.332 43.6325 144.481 44.7358 148.982 47.4133C153.307 49.9877 158.515 53.1653 161.193 55.1219C161.531 55.372 161.457 55.9457 161.06 56.0781C156.706 57.5198 137.787 62.9041 116.736 56.0046C116.25 55.8427 115.765 55.6515 115.309 55.4308C111.366 53.5478 91.5795 44.1915 84.4446 41.8377C84.2386 41.7789 84.2092 41.47 84.4005 41.367M35.9272 62.8452C39.3991 56.0487 44.2096 51.297 48.4317 47.9134C52.0948 45.1772 55.5813 42.9558 58.0969 41.4847C58.1705 41.4405 58.244 41.4405 58.3176 41.47C61.5688 42.6321 66.2028 44.4269 71.3517 46.7513C71.4694 46.8101 71.4988 46.9719 71.3958 47.0455L63.3341 53.8714C63.2017 54.0038 63.3194 54.2539 63.4959 54.2098C65.0847 53.7979 71.6312 51.5471 76.3682 50.7674C78.0011 50.5026 79.6635 50.8704 81.0757 51.7383C87.5045 55.6956 93.0653 59.5941 96.4342 62.0214C96.8608 62.3745 97.5522 62.9482 97.9788 63.3013C99.8177 65.0078 101.142 66.1111 102.083 66.7731C102.127 66.8172 102.186 66.8761 102.23 66.9202C102.436 67.1262 102.23 67.4645 101.98 67.391C65.9674 57.5198 39.7375 62.0361 36.2215 63.2277C36.0155 63.3013 35.839 63.0512 35.942 62.8452"
        fill="currentColor"
      />
    </svg>
  );
};
