// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const AirportShuttleIcon: React.FC<IconProps> = (props) => {
  const iconProps = useIconProps(props);

  return (
    <svg
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <path
        d="M12.9889 6.44361C13.8622 6.94755 14.5874 7.67262 15.0915 8.54587C15.5956 9.41913 15.8608 10.4097 15.8605 11.4181V17.9817H12.4391C12.2696 18.4621 11.9553 18.8781 11.5394 19.1724C11.1236 19.4667 10.6267 19.6247 10.1172 19.6247C9.60781 19.6247 9.11092 19.4667 8.69506 19.1724C8.2792 18.8781 7.96485 18.4621 7.79534 17.9817H4.37402V11.4181C4.37366 10.4097 4.63887 9.41913 5.14297 8.54587C5.64708 7.67262 6.37229 6.94755 7.24564 6.44361C7.11082 6.20147 7.00743 5.94313 6.93796 5.67483H4.37402V4.03391H6.93878C7.12128 3.32976 7.53244 2.70615 8.10772 2.26096C8.683 1.81577 9.38983 1.57422 10.1172 1.57422C10.8447 1.57422 11.5515 1.81577 12.1268 2.26096C12.702 2.70615 13.1132 3.32976 13.2957 4.03391H15.8605V5.67483H13.2957C13.226 5.94559 13.1218 6.20403 12.9897 6.44361H12.9889ZM11.7967 7.6743C11.3053 7.96802 10.731 8.13622 10.1172 8.13622C9.52593 8.13711 8.94546 7.97746 8.43776 7.6743C7.71596 7.9983 7.10323 8.52408 6.67337 9.18831C6.24351 9.85253 6.01485 10.6269 6.01494 11.4181V16.3408H7.65587V13.059C7.65587 12.4062 7.91519 11.7801 8.37679 11.3185C8.83839 10.8569 9.46445 10.5976 10.1172 10.5976C10.77 10.5976 11.3961 10.8569 11.8577 11.3185C12.3193 11.7801 12.5786 12.4062 12.5786 13.059V16.3408H14.2195V11.4181C14.2196 10.6269 13.991 9.85253 13.5611 9.18831C13.1313 8.52408 12.5185 7.9983 11.7967 7.6743ZM10.1172 12.2385C9.89965 12.2385 9.69096 12.325 9.53709 12.4788C9.38323 12.6327 9.29679 12.8414 9.29679 13.059V17.1613C9.29679 17.3789 9.38323 17.5876 9.53709 17.7414C9.69096 17.8953 9.89965 17.9817 10.1172 17.9817C10.3348 17.9817 10.5435 17.8953 10.6974 17.7414C10.8513 17.5876 10.9377 17.3789 10.9377 17.1613V13.059C10.9377 12.8414 10.8513 12.6327 10.6974 12.4788C10.5435 12.325 10.3348 12.2385 10.1172 12.2385ZM10.1172 6.4953C10.5524 6.4953 10.9698 6.32241 11.2776 6.01468C11.5853 5.70695 11.7582 5.28957 11.7582 4.85437C11.7582 4.41917 11.5853 4.0018 11.2776 3.69407C10.9698 3.38634 10.5524 3.21345 10.1172 3.21345C9.68205 3.21345 9.26467 3.38634 8.95694 3.69407C8.64921 4.0018 8.47633 4.41917 8.47633 4.85437C8.47633 5.28957 8.64921 5.70695 8.95694 6.01468C9.26467 6.32241 9.68205 6.4953 10.1172 6.4953Z"
        fill="currentColor"
      />
    </svg>
  );
};
