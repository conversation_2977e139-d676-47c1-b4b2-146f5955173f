// Libraries
import React from "react";

interface HistoryIconIconProps extends React.SVGProps<SVGSVGElement> {}

export const HistoryIcon: React.FC<HistoryIconIconProps> = (props) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.8601 0.644043C16.5787 0.644232 21.1558 5.25592 21.156 10.9399C21.1558 16.626 16.5462 21.2356 10.8601 21.2358C5.17401 21.2357 0.564399 16.626 0.564209 10.9399C0.56443 10.4233 0.983066 10.0035 1.49976 10.0034C2.01656 10.0034 2.43508 10.4232 2.4353 10.9399C2.43549 15.5921 6.20789 19.3636 10.8601 19.3638C15.5123 19.3636 19.2837 15.5921 19.2839 10.9399C19.2837 6.2858 15.5409 2.51533 10.8601 2.51514C8.01803 2.5152 5.88245 3.69624 4.43921 4.89893C4.05251 5.22118 3.71825 5.54435 3.4353 5.84326H5.1394C5.65621 5.84326 6.07571 6.26303 6.07593 6.77979C6.07582 7.29663 5.65628 7.71533 5.1394 7.71533H1.49976C0.982995 7.7152 0.564315 7.29655 0.564209 6.77979V2.61963C0.56443 2.10296 0.983066 1.68324 1.49976 1.68311C2.01656 1.68311 2.43508 2.10288 2.4353 2.61963V4.18896C2.67795 3.95125 2.94668 3.70569 3.24097 3.46045C4.91764 2.06332 7.4627 0.644111 10.8601 0.644043ZM10.8601 5.84424C11.3769 5.8443 11.7955 6.26298 11.7957 6.77979V10.438L14.4998 12.2407L14.5759 12.2983C14.942 12.5999 15.0271 13.1353 14.7585 13.5386C14.4718 13.9687 13.8908 14.0851 13.4607 13.7983L10.3406 11.7183C10.0805 11.5447 9.92461 11.2527 9.92456 10.9399V6.77979C9.92468 6.26295 10.3432 5.84424 10.8601 5.84424Z"
        fill="currentColor"
      />
    </svg>
  );
};
