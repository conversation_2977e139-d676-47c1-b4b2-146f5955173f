// Libraries
import React from "react";

interface GiftIconProps extends React.SVGProps<SVGSVGElement> {}

export const GiftIcon: React.FC<GiftIconProps> = (props) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.8558 6.20778C14.8558 6.73465 14.4287 7.16176 13.9018 7.16176C13.375 7.16176 12.9478 6.73465 12.9478 6.20778H3.40797V8.11524C4.59188 9.00333 5.31595 10.4048 5.31595 11.9317C5.31595 13.4586 4.59188 14.8601 3.40797 15.7481V17.6556H12.9478C12.9478 17.1287 13.375 16.7016 13.9018 16.7016C14.4287 16.7016 14.8558 17.1287 14.8558 17.6556H20.5797V15.7481C19.3958 14.8601 18.6718 13.4586 18.6718 11.9317C18.6718 10.4048 19.3958 9.00333 20.5797 8.11524V6.20778H14.8558ZM22.4877 17.6556C22.4877 18.7094 21.6335 19.5636 20.5797 19.5636H3.40797C2.35423 19.5636 1.5 18.7094 1.5 17.6556V14.6869L1.9763 14.4114C2.85574 13.9026 3.40797 12.9658 3.40797 11.9317C3.40797 10.8976 2.85574 9.96076 1.9763 9.45203L1.5 9.17651V6.20778C1.5 5.15403 2.35423 4.2998 3.40797 4.2998H20.5797C21.6335 4.2998 22.4877 5.15403 22.4877 6.20778V9.17651L22.0114 9.45203C21.132 9.96076 20.5797 10.8976 20.5797 11.9317C20.5797 12.9658 21.132 13.9026 22.0114 14.4114L22.4877 14.6869V17.6556ZM13.9018 15.7476C13.375 15.7476 12.9478 15.3205 12.9478 14.7937C12.9478 14.2668 13.375 13.8397 13.9018 13.8397C14.4287 13.8397 14.8558 14.2668 14.8558 14.7937C14.8558 15.3205 14.4287 15.7476 13.9018 15.7476ZM13.9018 12.8857C13.375 12.8857 12.9478 12.4586 12.9478 11.9317C12.9478 11.4048 13.375 10.9777 13.9018 10.9777C14.4287 10.9777 14.8558 11.4048 14.8558 11.9317C14.8558 12.4586 14.4287 12.8857 13.9018 12.8857ZM13.9018 10.0237C13.375 10.0237 12.9478 9.59661 12.9478 9.06974C12.9478 8.54286 13.375 8.11575 13.9018 8.11575C14.4287 8.11575 14.8558 8.54286 14.8558 9.06974C14.8558 9.59661 14.4287 10.0237 13.9018 10.0237Z"
        fill="currentColor"
      />
    </svg>
  );
};
