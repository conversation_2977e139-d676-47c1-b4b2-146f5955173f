// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const AccountIcon: React.FC<IconProps> = (props) => {
  const iconProps = useIconProps(props);

  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <path
        d="M19.6642 22.0337H17.7562V20.1257C17.7562 19.3667 17.4547 18.6388 16.918 18.102C16.3812 17.5653 15.6533 17.2638 14.8942 17.2638H9.17032C8.41128 17.2638 7.68333 17.5653 7.14661 18.102C6.60989 18.6388 6.30836 19.3667 6.30836 20.1257V22.0337H4.40039V20.1257C4.40039 18.8607 4.90294 17.6474 5.79747 16.7529C6.69201 15.8584 7.90526 15.3558 9.17032 15.3558H14.8942C16.1593 15.3558 17.3726 15.8584 18.2671 16.7529C19.1616 17.6474 19.6642 18.8607 19.6642 20.1257V22.0337ZM12.0323 13.4478C11.2806 13.4478 10.5363 13.2998 9.84183 13.0121C9.14738 12.7245 8.51637 12.3029 7.98486 11.7713C7.45335 11.2398 7.03172 10.6088 6.74407 9.91437C6.45642 9.21991 6.30836 8.47559 6.30836 7.72392C6.30836 6.97224 6.45642 6.22793 6.74407 5.53347C7.03172 4.83901 7.45335 4.20801 7.98486 3.6765C8.51637 3.14498 9.14738 2.72336 9.84183 2.43571C10.5363 2.14805 11.2806 2 12.0323 2C13.5504 2 15.0063 2.60305 16.0797 3.6765C17.1531 4.74994 17.7562 6.20584 17.7562 7.72392C17.7562 9.242 17.1531 10.6979 16.0797 11.7713C15.0063 12.8448 13.5504 13.4478 12.0323 13.4478V13.4478ZM12.0323 11.5399C13.0443 11.5399 14.0149 11.1378 14.7306 10.4222C15.4462 9.70657 15.8482 8.73597 15.8482 7.72392C15.8482 6.71187 15.4462 5.74127 14.7306 5.02564C14.0149 4.31001 13.0443 3.90797 12.0323 3.90797C11.0202 3.90797 10.0496 4.31001 9.334 5.02564C8.61837 5.74127 8.21634 6.71187 8.21634 7.72392C8.21634 8.73597 8.61837 9.70657 9.334 10.4222C10.0496 11.1378 11.0202 11.5399 12.0323 11.5399V11.5399Z"
        fill="currentColor"
      />
    </svg>
  );
};
