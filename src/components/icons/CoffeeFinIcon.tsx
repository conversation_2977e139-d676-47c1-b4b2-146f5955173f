// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

interface CoffeeFinIconProps extends React.SVGProps<SVGSVGElement> {}

export const CoffeeFinIcon: React.FC<CoffeeFinIconProps> = ({
  width = "17px",
  height = "18px",
  ...props
}) => {
  const iconProps = useIconProps({
    ...props,
    width,
    height,
  });

  return (
    <svg
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <mask
        id="mask0_1399_1416"
        style={{
          maskType: "luminance",
        }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="17"
        height="18"
      >
        <path
          d="M16.459 0.279083H0.597656V17.8401H16.459V0.279083Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_1399_1416)">
        <path
          d="M16.4544 4.4602C16.4544 3.64058 16.0937 3.35353 15.3723 3.36108C14.8 3.36675 14.2278 3.36108 13.6556 3.37241C13.4498 3.37619 13.3459 3.3252 13.3705 3.09292C13.4082 2.73222 13.2987 2.43195 12.9947 2.20345C12.8965 2.1298 12.8379 1.98627 12.7926 1.86352C12.7303 1.69544 12.7019 1.51415 12.6547 1.34041C12.4697 0.641671 11.9975 0.279083 11.2743 0.279083C9.11949 0.279083 6.96472 0.279083 4.81186 0.279083C4.21322 0.279083 3.71844 0.639782 3.53715 1.2101C3.48049 1.38951 3.42572 1.5708 3.39928 1.75588C3.36718 1.97872 3.25765 2.05803 3.03481 2.05426C2.50792 2.04103 1.98104 2.03726 1.45415 2.05237C1.01036 2.0637 0.706312 2.32808 0.619442 2.73977C0.545791 3.08914 0.662877 3.37241 0.910269 3.61792C1.45604 4.1599 1.99237 4.70944 2.5438 5.24388C2.7251 5.41953 2.81763 5.6065 2.80253 5.86144C2.78553 6.1466 2.78364 6.43366 2.78553 6.71881C2.78742 7.70461 2.79686 8.69227 2.79875 9.67803C2.79875 9.94621 2.74398 9.98398 2.47582 9.97832C2.03203 9.96888 1.58634 9.96322 1.14255 9.97075C0.744082 9.97641 0.602446 10.1313 0.600557 10.5279C0.600557 10.7772 0.600557 11.0245 0.600557 11.2738C0.606222 12.0726 1.15766 12.6203 1.95271 12.6203C4.00171 12.6203 6.05073 12.6203 8.09971 12.6203C10.1487 12.6203 12.2279 12.6203 14.2921 12.6203C14.9511 12.6203 15.5026 12.1255 15.5554 11.4759C15.58 11.1699 15.58 10.8584 15.5649 10.5505C15.5403 10.0746 15.4195 9.97266 14.9323 9.96888C14.496 9.967 14.0579 9.95943 13.6216 9.97266C13.4177 9.97832 13.3554 9.89711 13.3591 9.70827C13.3667 9.31737 13.3648 8.92642 13.3591 8.53551C13.3591 8.36556 13.429 8.27869 13.5952 8.23335C13.9445 8.14082 14.2921 8.04263 14.632 7.92365C15.6725 7.55541 16.2825 6.81891 16.4185 5.72357C16.4695 5.30812 16.4544 4.94363 16.4544 4.4602ZM2.78364 4.10515C2.75909 4.11838 2.73454 4.13157 2.70999 4.1448C2.34362 3.74067 1.97915 3.33842 1.61089 2.9324H2.2171C2.59479 2.9324 2.78364 3.12251 2.78364 3.50272V4.10702V4.10515ZM4.33786 1.70489C4.46817 1.22899 4.5097 1.18744 5.00261 1.18366C7.01385 1.17422 9.02318 1.16856 11.0344 1.17045C11.5934 1.17045 11.837 1.36874 11.8861 1.8012C11.9069 1.98249 11.8616 2.05426 11.6595 2.05237C10.4698 2.04292 9.28 2.04859 8.09026 2.04859C6.90053 2.04859 5.7712 2.04859 4.61171 2.04859C4.27176 2.04859 4.25288 2.02404 4.33975 1.70489H4.33786ZM3.67311 3.28177C3.67311 2.97583 3.71844 2.93051 4.02815 2.93051C6.73057 2.93051 9.43297 2.93051 12.1373 2.93051C12.4357 2.93051 12.4791 2.97394 12.4791 3.27044C12.4791 5.40252 12.4791 7.53274 12.4791 9.66484C12.4791 9.94621 12.4526 9.96888 12.1637 9.96888C10.8002 9.96888 9.43863 9.96888 8.07516 9.96888C6.71922 9.96888 5.36519 9.96888 4.00926 9.96888C3.70522 9.96888 3.67311 9.93676 3.67311 9.63272C3.67311 7.51573 3.67311 5.40064 3.67311 3.28365V3.28177ZM14.1504 10.864C14.2184 10.864 14.2864 10.8659 14.3543 10.864C14.6508 10.847 14.7113 10.9037 14.6999 11.1983C14.6886 11.5325 14.4941 11.7308 14.1617 11.7308C10.1242 11.7308 6.08659 11.7308 2.04902 11.7308C1.67133 11.7308 1.39939 11.4494 1.43149 11.1095C1.4466 10.949 1.50136 10.8584 1.69965 10.8602C3.82419 10.8678 5.94872 10.8659 8.07137 10.8659C10.0977 10.8659 12.1241 10.8659 14.1504 10.8659V10.864ZM15.4988 5.78589C15.4195 6.34301 15.0795 6.75089 14.564 6.97563C14.2354 7.11729 13.8822 7.20416 13.5385 7.31179C13.3648 7.36654 13.3742 7.24002 13.3742 7.13239C13.3742 6.68857 13.3742 6.24478 13.3742 5.801C13.3742 5.36474 13.3799 4.92853 13.3723 4.49227C13.3686 4.31854 13.4233 4.25056 13.6046 4.25434C14.1844 4.26187 14.7623 4.25813 15.342 4.25621C15.4553 4.25621 15.563 4.26379 15.5573 4.41485C15.5422 4.87186 15.563 5.33454 15.4988 5.78589Z"
          fill="currentColor"
        />
        <path
          d="M16.4544 4.4602C16.4544 3.64058 16.0937 3.35353 15.3723 3.36108C14.8 3.36675 14.2278 3.36108 13.6556 3.37241C13.4498 3.37619 13.3459 3.3252 13.3705 3.09292C13.4082 2.73222 13.2987 2.43195 12.9947 2.20345C12.8965 2.1298 12.8379 1.98627 12.7926 1.86352C12.7303 1.69544 12.7019 1.51415 12.6547 1.34041C12.4697 0.641671 11.9975 0.279083 11.2743 0.279083C9.11949 0.279083 6.96472 0.279083 4.81186 0.279083C4.21322 0.279083 3.71844 0.639782 3.53715 1.2101C3.48049 1.38951 3.42572 1.5708 3.39928 1.75588C3.36718 1.97872 3.25765 2.05803 3.03481 2.05426C2.50792 2.04103 1.98104 2.03726 1.45415 2.05237C1.01036 2.0637 0.706312 2.32808 0.619442 2.73977C0.545791 3.08914 0.662877 3.37241 0.910269 3.61792C1.45604 4.1599 1.99237 4.70944 2.5438 5.24388C2.7251 5.41953 2.81763 5.6065 2.80253 5.86144C2.78553 6.1466 2.78364 6.43366 2.78553 6.71881C2.78742 7.70461 2.79686 8.69227 2.79875 9.67803C2.79875 9.94621 2.74398 9.98398 2.47582 9.97832C2.03203 9.96888 1.58634 9.96322 1.14255 9.97075C0.744082 9.97641 0.602446 10.1313 0.600557 10.5279C0.600557 10.7772 0.600557 11.0245 0.600557 11.2738C0.606222 12.0726 1.15766 12.6203 1.95271 12.6203C4.00171 12.6203 6.05073 12.6203 8.09971 12.6203C10.1487 12.6203 12.2279 12.6203 14.2921 12.6203C14.9511 12.6203 15.5026 12.1255 15.5554 11.4759C15.58 11.1699 15.58 10.8584 15.5649 10.5505C15.5403 10.0746 15.4195 9.97266 14.9323 9.96888C14.496 9.967 14.0579 9.95943 13.6216 9.97266C13.4177 9.97832 13.3554 9.89711 13.3591 9.70827C13.3667 9.31737 13.3648 8.92642 13.3591 8.53551C13.3591 8.36556 13.429 8.27869 13.5952 8.23335C13.9445 8.14082 14.2921 8.04263 14.632 7.92365C15.6725 7.55541 16.2825 6.81891 16.4185 5.72357C16.4695 5.30812 16.4544 4.94363 16.4544 4.4602ZM2.78364 4.10515C2.75909 4.11838 2.73454 4.13157 2.70999 4.1448C2.34362 3.74067 1.97915 3.33842 1.61089 2.9324H2.2171C2.59479 2.9324 2.78364 3.12251 2.78364 3.50272V4.10702V4.10515ZM4.33786 1.70489C4.46817 1.22899 4.5097 1.18744 5.00261 1.18366C7.01385 1.17422 9.02318 1.16856 11.0344 1.17045C11.5934 1.17045 11.837 1.36874 11.8861 1.8012C11.9069 1.98249 11.8616 2.05426 11.6595 2.05237C10.4698 2.04292 9.28 2.04859 8.09026 2.04859C6.90053 2.04859 5.7712 2.04859 4.61171 2.04859C4.27176 2.04859 4.25288 2.02404 4.33975 1.70489H4.33786ZM3.67311 3.28177C3.67311 2.97583 3.71844 2.93051 4.02815 2.93051C6.73057 2.93051 9.43297 2.93051 12.1373 2.93051C12.4357 2.93051 12.4791 2.97394 12.4791 3.27044C12.4791 5.40252 12.4791 7.53274 12.4791 9.66484C12.4791 9.94621 12.4526 9.96888 12.1637 9.96888C10.8002 9.96888 9.43863 9.96888 8.07516 9.96888C6.71922 9.96888 5.36519 9.96888 4.00926 9.96888C3.70522 9.96888 3.67311 9.93676 3.67311 9.63272C3.67311 7.51573 3.67311 5.40064 3.67311 3.28365V3.28177ZM14.1504 10.864C14.2184 10.864 14.2864 10.8659 14.3543 10.864C14.6508 10.847 14.7113 10.9037 14.6999 11.1983C14.6886 11.5325 14.4941 11.7308 14.1617 11.7308C10.1242 11.7308 6.08659 11.7308 2.04902 11.7308C1.67133 11.7308 1.39939 11.4494 1.43149 11.1095C1.4466 10.949 1.50136 10.8584 1.69965 10.8602C3.82419 10.8678 5.94872 10.8659 8.07137 10.8659C10.0977 10.8659 12.1241 10.8659 14.1504 10.8659V10.864ZM15.4988 5.78589C15.4195 6.34301 15.0795 6.75089 14.564 6.97563C14.2354 7.11729 13.8822 7.20416 13.5385 7.31179C13.3648 7.36654 13.3742 7.24002 13.3742 7.13239C13.3742 6.68857 13.3742 6.24478 13.3742 5.801C13.3742 5.36474 13.3799 4.92853 13.3723 4.49227C13.3686 4.31854 13.4233 4.25056 13.6046 4.25434C14.1844 4.26187 14.7623 4.25813 15.342 4.25621C15.4553 4.25621 15.563 4.26379 15.5573 4.41485C15.5422 4.87186 15.563 5.33454 15.4988 5.78589Z"
          fill="currentColor"
        />
        <path
          d="M10.2322 15.6813C10.0906 15.043 9.79789 14.4802 9.35976 13.9987C9.18977 13.8117 8.98395 13.6531 8.7781 13.5039C8.62134 13.3887 8.4325 13.3774 8.27199 13.5039C7.56382 14.0704 6.9916 14.7314 6.79898 15.653C6.56101 16.7842 7.38438 17.8436 8.50427 17.8398C9.69022 17.8342 10.4872 16.8389 10.2303 15.6813H10.2322ZM8.54013 16.9447C8.0076 16.9504 7.57326 16.448 7.66013 15.9174C7.74887 15.3754 8.03968 14.9429 8.42684 14.5728C8.46462 14.5369 8.59301 14.5312 8.627 14.5652C8.99906 14.9372 9.30688 15.349 9.39375 15.8872C9.47871 16.4178 9.05568 16.941 8.53826 16.9447H8.54013Z"
          fill="currentColor"
        />
        <path
          d="M10.2322 15.6813C10.0906 15.043 9.79789 14.4802 9.35976 13.9987C9.18977 13.8117 8.98395 13.6531 8.7781 13.5039C8.62134 13.3887 8.4325 13.3774 8.27199 13.5039C7.56382 14.0704 6.9916 14.7314 6.79898 15.653C6.56101 16.7842 7.38438 17.8436 8.50427 17.8398C9.69022 17.8342 10.4872 16.8389 10.2303 15.6813H10.2322ZM8.54013 16.9447C8.0076 16.9504 7.57326 16.448 7.66013 15.9174C7.74887 15.3754 8.03968 14.9429 8.42684 14.5728C8.46462 14.5369 8.59301 14.5312 8.627 14.5652C8.99906 14.9372 9.30688 15.349 9.39375 15.8872C9.47871 16.4178 9.05568 16.941 8.53826 16.9447H8.54013Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
};
