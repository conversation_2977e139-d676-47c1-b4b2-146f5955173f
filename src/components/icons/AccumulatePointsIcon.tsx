// Libraries
import React from "react";

interface AccumulatePointsIconProps extends React.SVGProps<SVGSVGElement> {}

export const AccumulatePointsIcon: React.FC<AccumulatePointsIconProps> = (
  props
) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M2.85938 14.1636C3.37602 14.1636 3.79445 14.5826 3.79492 15.0991V15.6196C3.79492 16.359 3.79626 16.8674 3.82324 17.2632C3.84965 17.65 3.89841 17.8616 3.96191 18.0151C4.18351 18.5501 4.60863 18.976 5.14355 19.1978C5.29706 19.2613 5.50868 19.31 5.89551 19.3364C6.29124 19.3634 6.79976 19.3638 7.53906 19.3638H8.05957L8.15527 19.3687C8.62693 19.4167 8.99477 19.8151 8.99512 20.2993C8.99512 20.7839 8.62717 21.1829 8.15527 21.231L8.05957 21.2358H7.53906L6.56445 21.2319C6.27029 21.228 6.00573 21.2198 5.76855 21.2036C5.28595 21.1707 4.84638 21.1011 4.42676 20.9273C3.43334 20.5155 2.64401 19.7254 2.23242 18.7319C2.0587 18.3123 1.988 17.8727 1.95508 17.3901C1.92276 16.9159 1.92383 16.3333 1.92383 15.6196V15.0991C1.9243 14.5827 2.3429 14.1638 2.85938 14.1636ZM21.5791 14.1636C22.0957 14.1636 22.5152 14.5826 22.5156 15.0991V15.6196C22.5156 16.3333 22.5157 16.9159 22.4834 17.3901C22.4505 17.8728 22.3808 18.3123 22.207 18.7319C21.7954 19.7258 21.0055 20.5156 20.0117 20.9273C19.592 21.1011 19.1526 21.1707 18.6699 21.2036C18.1957 21.2359 17.6131 21.2358 16.8994 21.2358H16.3789C15.8624 21.2354 15.4434 20.816 15.4434 20.2993C15.4437 19.783 15.8626 19.3642 16.3789 19.3638H16.8994C17.6389 19.3638 18.1472 19.3634 18.543 19.3364C18.9299 19.31 19.1414 19.2613 19.2949 19.1978C19.8301 18.9761 20.2559 18.5503 20.4775 18.0151C20.541 17.8616 20.5898 17.6501 20.6162 17.2632C20.6432 16.8674 20.6436 16.359 20.6436 15.6196V15.0991C20.644 14.5828 21.0628 14.1639 21.5791 14.1636ZM22.6191 10.0044C23.1361 10.0044 23.5556 10.423 23.5557 10.9399C23.5557 11.4569 23.1361 11.8755 22.6191 11.8755H1.81934C1.30259 11.8753 0.883789 11.4567 0.883789 10.9399C0.88382 10.4232 1.30261 10.0046 1.81934 10.0044H22.6191ZM8.05957 0.644048C8.57628 0.644153 8.9949 1.06291 8.99512 1.57959C8.99512 2.09647 8.57642 2.51504 8.05957 2.51514H7.53906C6.79976 2.51514 6.29124 2.51646 5.89551 2.54346C5.50858 2.56991 5.29707 2.61855 5.14355 2.68213C4.60868 2.90378 4.18358 3.3289 3.96191 3.86377C3.89833 4.01728 3.84969 4.22883 3.82324 4.61573C3.79624 5.01145 3.79492 5.52 3.79492 6.25928V6.77979C3.79482 7.29664 3.37625 7.71534 2.85938 7.71534C2.34267 7.71514 1.92393 7.29651 1.92383 6.77979V6.25928C1.92383 5.54556 1.92273 4.96296 1.95508 4.48877C1.98803 4.00616 2.05861 3.56661 2.23242 3.14698C2.64407 2.1537 3.43347 1.36428 4.42676 0.952641C4.8464 0.77882 5.28593 0.708246 5.76855 0.675298C6.24275 0.642945 6.82533 0.644047 7.53906 0.644048H8.05957ZM16.8994 0.644048C17.6131 0.644048 18.1957 0.642982 18.6699 0.675298C19.1525 0.708224 19.5921 0.778918 20.0117 0.952641C21.0052 1.36423 21.7953 2.15357 22.207 3.14698C22.3808 3.56659 22.4504 4.00618 22.4834 4.48877C22.5158 4.96297 22.5156 5.54556 22.5156 6.25928V6.77979C22.5155 7.29664 22.096 7.71534 21.5791 7.71534C21.0625 7.71499 20.6437 7.29642 20.6436 6.77979V6.25928C20.6436 5.52 20.6432 5.01145 20.6162 4.61573C20.5898 4.22892 20.5411 4.01727 20.4775 3.86377C20.2558 3.32886 19.8299 2.90373 19.2949 2.68213C19.1414 2.61862 18.9298 2.56987 18.543 2.54346C18.1472 2.51648 17.6388 2.51514 16.8994 2.51514H16.3789C15.8624 2.51469 15.4434 2.09625 15.4434 1.57959C15.4436 1.06312 15.8625 0.6445 16.3789 0.644048H16.8994Z"
        fill="currentColor"
      />
    </svg>
  );
};
