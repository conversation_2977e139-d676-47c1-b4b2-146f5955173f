// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const PublicIcon: React.FC<IconProps> = (props) => {
  const iconProps = useIconProps(props);

  return (
    <svg
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <path
        d="M10.1504 2.0918C14.6818 2.0918 18.355 5.765 18.355 10.2964C18.355 14.8278 14.6818 18.501 10.1504 18.501C5.619 18.501 1.9458 14.8278 1.9458 10.2964C1.9458 5.765 5.619 2.0918 10.1504 2.0918ZM10.4507 11.4172L7.60452 16.3481C8.7597 16.8315 10.0285 16.9766 11.263 16.7666C11.0449 15.2334 11.268 13.6699 11.9062 12.2589L10.4507 11.4172ZM13.3371 13.0835C12.9113 14.0913 12.7457 15.1899 12.8555 16.2784C13.8513 15.8268 14.7189 15.1343 15.38 14.2633L13.3371 13.0835V13.0835ZM7.57334 9.75654C6.6705 11.0148 5.4284 11.9902 3.99203 12.5691C4.42597 13.7433 5.18627 14.7692 6.18348 15.526L9.02966 10.5967L7.57334 9.75654V9.75654ZM16.6829 9.64988L16.4712 9.74833C15.5616 10.1977 14.7693 10.8531 14.1575 11.6625L16.2021 12.8431C16.6268 11.8353 16.7921 10.7372 16.6829 9.64906V9.64988ZM3.58672 10.2964C3.58672 10.5146 3.59739 10.7312 3.6179 10.9437C4.61517 10.4949 5.48358 9.80257 6.14328 8.93034L4.09869 7.75051C3.75965 8.55641 3.58557 9.42209 3.58672 10.2964V10.2964ZM14.1173 5.06679L11.2712 9.99611L12.7275 10.8371C13.6303 9.57814 14.8727 8.60207 16.3096 8.02291C15.9062 6.93353 15.2214 5.97047 14.3249 5.2317L14.1173 5.06679V5.06679ZM10.1504 3.73272C9.77053 3.73272 9.39886 3.76554 9.03704 3.82707C9.25548 5.3602 9.03234 6.92384 8.3938 8.33468L9.85012 9.17565L12.6963 4.24469C11.8904 3.90565 11.0247 3.73157 10.1504 3.73272ZM7.44535 4.31524L7.28864 4.38745C6.35783 4.8399 5.54665 5.5052 4.92079 6.32948L6.96374 7.5093C7.38933 6.50177 7.55493 5.40346 7.44535 4.31524V4.31524Z"
        fill="currentColor"
      />
    </svg>
  );
};
