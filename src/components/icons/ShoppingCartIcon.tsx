// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const ShoppingCartIcon: React.FC<IconProps> = (props) => {
  const iconProps = useIconProps(props);

  return (
    <svg
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <path
        d="M10.1167 2.36133C11.4223 2.36133 12.6744 2.87997 13.5976 3.80317C14.5208 4.72637 15.0395 5.97849 15.0395 7.28409V8.10455H18.3213V9.74547H17.3638L16.7428 17.1977C16.7257 17.4027 16.6322 17.5939 16.4808 17.7332C16.3295 17.8726 16.1313 17.95 15.9256 17.9501H4.30785C4.10212 17.95 3.90394 17.8726 3.75258 17.7332C3.60123 17.5939 3.50775 17.4027 3.49068 17.1977L2.86877 9.74547H1.91211V8.10455H5.19395V7.28409C5.19395 5.97849 5.7126 4.72637 6.6358 3.80317C7.55899 2.87997 8.81112 2.36133 10.1167 2.36133ZM15.7172 9.74547H4.51543L5.06268 16.3092H15.1699L15.7172 9.74547ZM10.9372 11.3864V14.6682H9.29625V11.3864H10.9372ZM7.65533 11.3864V14.6682H6.01441V11.3864H7.65533ZM14.219 11.3864V14.6682H12.5781V11.3864H14.219ZM10.1167 4.00225C9.27472 4.00225 8.46494 4.32587 7.85486 4.90617C7.24478 5.48647 6.88107 6.27906 6.83897 7.12L6.83487 7.28409V8.10455H13.3986V7.28409C13.3986 6.4421 13.0749 5.63232 12.4946 5.02224C11.9143 4.41216 11.1217 4.04845 10.2808 4.00635L10.1167 4.00225Z"
        fill="currentColor"
      />
    </svg>
  );
};
