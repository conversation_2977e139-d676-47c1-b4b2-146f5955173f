// Libraries
import React from "react";

// Hooks
import { useIconProps } from "./hooks/useIconProps";

// Types
import { IconProps } from "./types";

export const HeaderTextLogo: React.FC<IconProps> = ({
  width = 153,
  height = 14,
  ...props
}) => {
  const iconProps = useIconProps({
    ...props,
    width,
    height,
  });

  return (
    <svg
      viewBox="0 0 153 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...iconProps}
    >
      <path
        d="M4.93144 0.1875V5.46336H10.6143V0.1875H15.5458V13.6981H10.6143V7.88994H4.93144V13.6981H0V0.1875H4.93144Z"
        fill="currentColor"
      />
      <path
        d="M25.2249 0.1875V13.6981H20.2935V0.1875H25.2249Z"
        fill="currentColor"
      />
      <path
        d="M42.2661 2.97452C41.8278 2.84928 41.3268 2.72403 40.7475 2.59879C40.1683 2.47355 39.5421 2.41093 38.8376 2.41093C37.1781 2.41093 35.8944 2.81796 35.002 3.64769C34.1097 4.47743 33.6557 5.60462 33.6557 7.04491C33.6557 7.78071 33.7496 8.42258 33.9531 8.97052C34.1566 9.51845 34.4384 9.97246 34.8142 10.3325C35.1899 10.7083 35.6282 10.9901 36.1449 11.1936C36.6615 11.3971 37.2407 11.538 37.8513 11.6006C38.0548 11.6319 38.2897 11.6476 38.5871 11.6476C38.8689 11.6476 39.0881 11.6476 39.2446 11.6476V8.6261H36.7867V6.34042H43.863V12.8844C43.0802 13.1192 42.2035 13.3384 41.2172 13.5575C40.2309 13.7767 39.0568 13.8863 37.7104 13.8863C36.2388 13.8863 34.9238 13.7141 33.7653 13.354C32.6068 12.9939 31.6361 12.5086 30.8534 11.8981C30.0549 11.2719 29.46 10.5517 29.0373 9.72197C28.6146 8.89224 28.4111 7.99988 28.4111 7.04491C28.4111 6.08993 28.6146 5.21323 29.006 4.35219C29.3974 3.5068 30.008 2.75534 30.8064 2.11347C31.6048 1.4716 32.6068 0.954975 33.8122 0.579246C35.002 0.203518 36.411 0 38.0235 0C38.9002 0 39.7769 0.0469637 40.6223 0.156551C41.4677 0.266139 42.3131 0.422692 43.1428 0.657522L42.2818 2.95886L42.2661 2.97452Z"
        fill="currentColor"
      />
      <path
        d="M53.4939 0.1875V5.46336H59.1768V0.1875H64.1083V13.6981H59.1768V7.88994H53.4939V13.6981H48.5625V0.1875H53.4939Z"
        fill="currentColor"
      />
      <path
        d="M73.7874 0.1875V11.1776H80.2061V13.6981H68.856V0.1875H73.7874Z"
        fill="currentColor"
      />
      <path
        d="M92.7406 0.1875L99.8794 13.6981H94.6035L93.2572 10.8801H87.543L86.1497 13.6981H82.7524L89.8913 0.1875H92.7562H92.7406ZM88.5449 8.75098H92.2396L90.4079 4.93108L88.5449 8.75098Z"
        fill="currentColor"
      />
      <path
        d="M105.34 0.1875L113.903 7.76469V0.1875H117.206V13.6981H114.248L105.684 6.12088V13.6981H102.381V0.1875H105.34Z"
        fill="currentColor"
      />
      <path
        d="M127.557 0.1875C128.246 0.1875 128.903 0.187503 129.545 0.203159C130.187 0.203159 130.813 0.26578 131.424 0.344057C132.034 0.422334 132.629 0.547573 133.208 0.719782C133.788 0.891991 134.367 1.12682 134.915 1.42427C135.572 1.78435 136.073 2.20704 136.433 2.69236C136.793 3.17768 137.075 3.67864 137.263 4.16396C137.451 4.64928 137.561 5.11894 137.607 5.54163C137.654 5.96433 137.67 6.29309 137.67 6.52792C137.67 6.70012 137.67 6.93496 137.654 7.21676C137.654 7.49855 137.592 7.82731 137.514 8.17173C137.435 8.53181 137.326 8.90753 137.169 9.31457C137.013 9.72161 136.793 10.113 136.512 10.52C136.23 10.9271 135.885 11.3028 135.463 11.6785C135.04 12.0543 134.539 12.3674 133.928 12.6648C133.49 12.8683 133.067 13.0405 132.629 13.1814C132.191 13.3223 131.721 13.4163 131.204 13.4945C130.688 13.5728 130.109 13.6198 129.467 13.6511C128.825 13.6824 128.073 13.6981 127.228 13.6981H121.952V0.1875H127.557ZM126.884 2.48884V11.4124H128.887C129.232 11.4124 129.576 11.3811 129.905 11.3028C130.249 11.2402 130.578 11.1149 130.891 10.9271C131.204 10.7549 131.486 10.4887 131.721 10.1287C131.956 9.76858 132.144 9.31457 132.285 8.73532C132.426 8.15607 132.488 7.45158 132.488 6.59054C132.488 5.57294 132.363 4.77452 132.097 4.21092C131.846 3.64733 131.518 3.24029 131.111 2.98981C130.703 2.72367 130.249 2.58277 129.733 2.5358C129.216 2.48884 128.7 2.47318 128.167 2.47318H126.837L126.884 2.48884Z"
        fill="currentColor"
      />
      <path
        d="M150.637 3.03714C150.214 2.91189 149.698 2.78665 149.071 2.6301C148.445 2.4892 147.756 2.41093 147.02 2.41093C146.441 2.41093 145.987 2.4892 145.643 2.66141C145.298 2.83362 145.126 3.05279 145.126 3.31893C145.126 3.58507 145.298 3.83556 145.643 4.03908C145.987 4.2426 146.426 4.47743 146.927 4.72792L148.836 5.65158C149.181 5.80814 149.557 6.01165 149.995 6.26214C150.418 6.49697 150.825 6.77877 151.2 7.10753C151.576 7.43629 151.889 7.81202 152.14 8.23471C152.39 8.65741 152.531 9.15838 152.531 9.69066C152.531 10.2229 152.406 10.7396 152.14 11.2405C151.874 11.7415 151.467 12.1799 150.934 12.5869C150.386 12.9783 149.713 13.2914 148.883 13.5419C148.054 13.7767 147.099 13.902 145.972 13.902C145.001 13.902 144.077 13.8237 143.216 13.6671C142.355 13.5106 141.478 13.2444 140.602 12.9L141.619 10.6613C142.293 10.8961 142.95 11.084 143.592 11.2562C144.249 11.4127 144.97 11.5067 145.752 11.5067C146.019 11.5067 146.269 11.491 146.535 11.4597C146.786 11.4284 147.02 11.3658 147.208 11.2875C147.396 11.2092 147.568 11.0996 147.678 10.9744C147.803 10.8492 147.866 10.6926 147.866 10.5047C147.866 10.2386 147.741 9.98811 147.474 9.75328C147.208 9.53411 146.723 9.25231 146.034 8.92355L144.202 8.06251C143.983 7.96858 143.67 7.81202 143.248 7.59284C142.825 7.37367 142.418 7.09188 142.011 6.74746C141.604 6.40304 141.244 6.01165 140.946 5.55765C140.649 5.10364 140.492 4.58702 140.492 3.99211C140.492 3.39721 140.633 2.91189 140.931 2.42658C141.228 1.94126 141.651 1.51857 142.214 1.1585C142.778 0.798426 143.451 0.516628 144.249 0.313108C145.048 0.109589 145.956 0 146.958 0C148.555 0 150.105 0.250488 151.592 0.735803L150.684 3.02148L150.637 3.03714Z"
        fill="currentColor"
      />
    </svg>
  );
};
