// Libraries
import React from "react";

interface ArrowLeftIconProps extends React.SVGProps<SVGSVGElement> {}

export const ArrowLeftIcon: React.FC<ArrowLeftIconProps> = (props) => {
  const { ...restProps } = props;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-arrow-left"
      {...restProps}
    >
      <path d="m12 19-7-7 7-7" />
      <path d="M19 12H5" />
    </svg>
  );
};
