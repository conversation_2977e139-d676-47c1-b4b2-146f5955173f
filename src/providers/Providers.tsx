// Libraries
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { SystemNotificationModal } from "components";
import { APP_CONFIG } from "constant";
import { useNavigateWithSearch } from "hooks";
import { MotionConfig } from "motion/react";
import React, { useEffect, useRef } from "react";
import { useImmer } from "use-immer";

interface ProvidersProps { }

export const Providers: React.FC<React.PropsWithChildren<ProvidersProps>> = (
  props
) => {
  const { children } = props;
  const [state, setState] = useImmer({
    errorMessage: "",
  });

  const { errorMessage } = state;

  const queryClient = useRef<QueryClient>()

  if (!queryClient.current) {

    // Create a client
    queryClient.current = new QueryClient({
      defaultOptions: {
        queries: {
          refetchOnWindowFocus: false,
          retry: 1,
          staleTime: 60 * 1000,
          refetchOnReconnect: true,
          throwOnError(error) {
            console.log("queryClient", error)

            setState((draft) => {
              draft.errorMessage = error?.message;
            });

            return false;
          },
        },
      },
    });
  }

  return (
    <QueryClientProvider client={queryClient.current}>
      <MotionConfig
        transition={{
          duration: 0.3,
          ease: "easeInOut",
        }}
      >
        {children}
      </MotionConfig>

      <SystemNotificationModal
        showCloseButton={false}
        visible={!!errorMessage}
        title="Thông báo"
        content={
          errorMessage || APP_CONFIG.SYSTEM_ERROR_MESSAGES.maintenance
        }
        retryButtonProps={{
          onClick: () => {
            setState((draft) => {
              draft.errorMessage = "";
            });
            window.location.reload();
          },
        }}
      />
    </QueryClientProvider>
  );
};
