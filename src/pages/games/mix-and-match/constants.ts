// Libraries
import Bean from "assets/images/mix-and-match/ingredients/bean.png";
import Ice from "assets/images/mix-and-match/ingredients/ice.png";
import Milk from "assets/images/mix-and-match/ingredients/milk.png";
import Sugar from "assets/images/mix-and-match/ingredients/sugar.png";
import Tea from "assets/images/mix-and-match/ingredients/tea.png";
import Coconut from "assets/images/mix-and-match/ingredients/coconut.png";
import Jelly from "assets/images/mix-and-match/ingredients/jelly.png";
import LotusSeed from "assets/images/mix-and-match/ingredients/lotus-seed.png";
import Mochi from "assets/images/mix-and-match/ingredients/mochi.png";
import Mango from "assets/images/mix-and-match/ingredients/mango.png";
import MilkTeaBrown from "assets/images/mix-and-match/ingredients/milk-tea-brown.png";
import MilkTeaGreen from "assets/images/mix-and-match/ingredients/milk-tea-green.png";

// Schemas
import { Mission } from "schemas";

// Sounds
import BeanSound from "assets/sound-effects/bean.mp3";
import MilkSound from "assets/sound-effects/milk.mp3";
import TeaSound from "assets/sound-effects/tea.mp3";
import IceSound from "assets/sound-effects/ice.mp3";
import SeedSound from "assets/sound-effects/seed.mp3";
import SugarSound from "assets/sound-effects/sugar.mp3";
import CreamSound from "assets/sound-effects/cream.mp3";

export const INGREDIENT_KEY = {
  BEAN: "bean",
  MILK: "milk",
  TEA: "tea",
  LOTUS_SEED: "lotus-seed",
  ICE: "ice",
  JELLY: "jelly",
  MOCHI: "mochi",
  SUGAR: "sugar",
  COCONUT: "coconut",
  MANGO: "mango",
  MILK_TEA_BROWN: "milk_tea_brown",
  MILK_TEA_GREEN: "milk_tea_green",
  LEMON: "lemon",
} as const;

export const INGREDIENTS = [
  {
    key: INGREDIENT_KEY.BEAN,
    title: "Cà phê",
    image: Bean,
    color: "#E68D3D",
    sound: {
      src: BeanSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.MILK,
    title: "Sữa",
    image: Milk,
    color: "#DA4457",
    sound: {
      src: MilkSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.TEA,
    title: "Lá trà",
    image: Tea,
    color: "#356A3C",
    sound: {
      src: TeaSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.LOTUS_SEED,
    title: "Hạt sen",
    image: LotusSeed,
    color: "#F5C545",
    sound: {
      src: SeedSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.ICE,
    title: "Đá",
    image: Ice,
    color: "#E58E8E",
    sound: {
      src: IceSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.JELLY,
    title: "Thạch",
    image: Jelly,
    color: "#E68D3D",
    sound: {
      src: SeedSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.MOCHI,
    title: "Mochi",
    image: Mochi,
    color: "#BEB28D",
    sound: {
      src: CreamSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.SUGAR,
    title: "Đường",
    image: Sugar,
    color: "#E68D3D",
    sound: {
      src: SugarSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.COCONUT,
    title: "Dừa",
    image: Coconut,
    color: "#356A3C",
    sound: {
      src: MilkSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.MANGO,
    title: "Xoài",
    image: Mango,
    color: "#F5C545",
    sound: {
      src: SeedSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.MILK_TEA_BROWN,
    title: "Trà sữa nâu",
    image: MilkTeaBrown,
    color: "#E68D3D",
    sound: {
      src: MilkSound,
      volume: 1,
    },
  },
  {
    key: INGREDIENT_KEY.MILK_TEA_GREEN,
    title: "Trà sữa xanh",
    image: MilkTeaGreen,
    color: "#356A3C",
    sound: {
      src: MilkSound,
      volume: 1,
    },
  },
];

const NORMAL_SELECTION_INGREDIENTS = [
  INGREDIENT_KEY.BEAN,
  INGREDIENT_KEY.MILK,
  INGREDIENT_KEY.TEA,
  INGREDIENT_KEY.LOTUS_SEED,
  INGREDIENT_KEY.ICE,
  INGREDIENT_KEY.JELLY,
];

const LTO_MOCHI_SELECTION_INGREDIENTS = [
  INGREDIENT_KEY.MOCHI,
  INGREDIENT_KEY.SUGAR,
  INGREDIENT_KEY.COCONUT,
  INGREDIENT_KEY.MANGO,
  INGREDIENT_KEY.MILK_TEA_BROWN,
  INGREDIENT_KEY.MILK_TEA_GREEN,
];

const LTO_MANGO_SELECTION_INGREDIENTS = [
  INGREDIENT_KEY.BEAN,
  INGREDIENT_KEY.MILK,
  INGREDIENT_KEY.COCONUT,
  INGREDIENT_KEY.MANGO,
  INGREDIENT_KEY.ICE,
  INGREDIENT_KEY.JELLY,
];

export const MISSIONS: Mission[] = [
  {
    id: 1,
    title: "Nhiệm vụ Trà Sen Vàng",
    code: "TRA_SEN_VANG",
    description:
      "Trời hôm nay nóng quá, <strong>((userName))</strong> thử làm một ly trà thanh mát cùng lớp kem béo ngậy nha.",
    ingredients: NORMAL_SELECTION_INGREDIENTS,
    correctIngredients: [
      INGREDIENT_KEY.TEA,
      INGREDIENT_KEY.LOTUS_SEED,
      INGREDIENT_KEY.ICE,
    ],
    type: "normal",
  },
  {
    id: 2,
    title: "Nhiệm vụ Freeze Trà Xanh",
    code: "FREEZE_TRA_XANH",
    description:
      "<strong>((userName))</strong> ơi, pha tặng Highlands một ly Freeze mát lạnh với vị trà xanh thơm lừng nha.",
    ingredients: NORMAL_SELECTION_INGREDIENTS,
    correctIngredients: [
      INGREDIENT_KEY.TEA,
      INGREDIENT_KEY.MILK,
      INGREDIENT_KEY.ICE,
    ],
    type: "normal",
  },
  {
    id: 3,
    title: "Nhiệm vụ Cà Phê Phin",
    code: "CA_PHE_PHIN",
    description:
      "Buổi sáng thiếu ngủ, <strong>((userName))</strong> ơi thử làm ngay ly cà phê đậm đà nào &gt;.&lt;",
    ingredients: NORMAL_SELECTION_INGREDIENTS,
    correctIngredients: [
      INGREDIENT_KEY.BEAN,
      INGREDIENT_KEY.SUGAR,
      INGREDIENT_KEY.ICE,
    ],
    type: "normal",
  },
  {
    id: 4,
    title: "Nhiệm vụ PhinDI",
    code: "PHIN_DI",
    description:
      "<strong>((userName))</strong> ơi, pha tặng Highlands ly PhinĐI đặc biệt thêm hạnh nhân thơm bùi nha!",
    ingredients: NORMAL_SELECTION_INGREDIENTS,
    correctIngredients: [
      INGREDIENT_KEY.BEAN,
      INGREDIENT_KEY.MILK,
      INGREDIENT_KEY.JELLY,
    ],
    type: "normal",
  },
  {
    id: 5,
    title: "Nhiệm vụ LTO Trà sữa Matcha Mochi",
    code: "LTO_MATCHA_MOCHI",
    description:
      "Hãy làm ly Trà xanh thơm ngon kết hợp với sữa béo ngậy, topping dai dai - giải nhiệt mà vẫn ngọt ngào.",
    ingredients: LTO_MOCHI_SELECTION_INGREDIENTS,
    correctIngredients: [
      INGREDIENT_KEY.MILK_TEA_GREEN,
      INGREDIENT_KEY.MOCHI,
      INGREDIENT_KEY.ICE,
    ],
    type: "lto",
    availableDates: ["2025-07-01 00:00:00", "2025-07-31 23:59:59"],
  },
  {
    id: 6,
    title: "Nhiệm vụ LTO Trà sữa Hojicha Mochi",
    code: "LTO_HOJICHA_MOCHI",
    description:
      "<strong>((userName))</strong> thử làm một ly có vị thơm nhẹ đến từ hương vị trà rang nhật hòa quyện cùng mochi sóng sánh.",
    ingredients: LTO_MOCHI_SELECTION_INGREDIENTS,
    correctIngredients: [
      INGREDIENT_KEY.MILK_TEA_BROWN,
      INGREDIENT_KEY.MOCHI,
      INGREDIENT_KEY.ICE,
    ],
    type: "lto",
    availableDates: ["2025-07-01 00:00:00", "2025-07-31 23:59:59"],
  },
  {
    id: 7,
    title: "Nhiệm vụ LTO PhinDi Xoài Dừa",
    code: "LTO_PHIN_DI_XOAI_DUA",
    description:
      "Bạn ơi thử làm một ly PhinĐI hoàn hảo giữa lớp sữa xoài dừa béo thanh và lớp cà phê Phin đậm đà nha.",
    ingredients: LTO_MANGO_SELECTION_INGREDIENTS,
    correctIngredients: [
      INGREDIENT_KEY.BEAN,
      INGREDIENT_KEY.MANGO,
      INGREDIENT_KEY.COCONUT,
    ],
    type: "lto",
    availableDates: ["2025-07-01 00:00:00", "2025-07-31 23:59:59"],
  },
];

export const RESULT_THEME = {
  default: {
    background: "#F6EFE6",
    textColor: "#DC5634",
    logoColor: "#A43534",
    primaryButton: {
      background: "#DC5634",
      color: "#F6EFE6",
    },
    secondaryButton: {
      color: "#356A3C",
    },
  },
  ["#F6EFE6"]: {
    background: "#F6EFE6",
    textColor: "#DC5634",
    logoColor: "#A43534",
    primaryButton: {
      background: "#DC5634",
      color: "#F6EFE6",
    },
    secondaryButton: {
      color: "#356A3C",
    },
  },
  ["#356A3C"]: {
    background: "#356A3C",
    textColor: "#F6EFE6",
    logoColor: "#F6EFE6",
    primaryButton: {
      background: "#DC5634",
      color: "#F6EFE6",
    },
    secondaryButton: {
      color: "#F6EFE6",
    },
  },
  ["#E68D3D"]: {
    background: "#E68D3D",
    textColor: "#F6EFE6",
    logoColor: "#F6EFE6",
    primaryButton: {
      background: "#F6EFE6",
      color: "#DC5634",
    },
    secondaryButton: {
      color: "#F6EFE6",
    },
  },
  ["#BEB28D"]: {
    background: "#BEB28D",
    textColor: "#356A3C",
    logoColor: "#F6EFE6",
    primaryButton: {
      background: "#356A3C",
      color: "#F6EFE6",
    },
    secondaryButton: {
      color: "#356A3C",
    },
  },
  ["#F5C545"]: {
    background: "#F5C545",
    textColor: "#356A3C",
    logoColor: "#F6EFE6",
    primaryButton: {
      background: "#356A3C",
      color: "#F5C545",
    },
    secondaryButton: {
      color: "#356A3C",
    },
  },
  ["#DC5634"]: {
    background: "#DC5634",
    textColor: "#F6EFE6",
    logoColor: "#F6EFE6",
    primaryButton: {
      background: "#F6EFE6",
      color: "#DC5634",
    },
    secondaryButton: {
      color: "#F6EFE6",
    },
  },
  ["#F6EFE5"]: {
    background: "#F6EFE5",
    textColor: "#356A3C",
    logoColor: "#A43534",
    primaryButton: {
      background: "#356A3C",
      color: "#F6EFE5",
    },
    secondaryButton: {
      color: "#356A3C",
    },
  },
};
