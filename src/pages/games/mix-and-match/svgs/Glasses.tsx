// Libraries
import React, { memo } from "react";
import styled from "styled-components";

interface GlassesProps extends React.SVGProps<SVGSVGElement> {}

const GlassesSvg = styled.svg`
  animation: asset-rotate 2s steps(2, start) infinite reverse;

  @keyframes asset-rotate {
    0% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(-10deg);
    }
    75% {
      transform: rotate(15deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }
`;

export const Glasses: React.FC<GlassesProps> = memo((props) => {
  return (
    <GlassesSvg
      id="Layer_2"
      data-name="Layer 2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 94.49 76.89"
      {...props}
    >
      <g id="Layer_1-2" data-name="Layer 1">
        <path
          fill="#dc5634"
          strokeWidth={0}
          d="M18.73,24.94c-3.5.38-3.82-1.4-5.56,3.63-1.26,3.64-1.81,10.51-2.71,14.7-.57,2.68-1.16,4.38-1.67,7.38-.29,1.67-1.07,3.55-1.08,5.24,1.67-1.3,3.17-2.82,4.98-3.95,2.2-1.37,4.6-1.42,6.49-2.33,5.42-2.6,5.66-3.2,11.59-4.79,2.58-.69,7.69-3.4,9.99-1.12,1.03,1.02,2.96,6.92,3.93,7.16,2.8-3.02,8.2-3.53,11.91-5.15-2.51-2.59-.81-5.8-1.51-8.79l27.17-13.14-7.52-8.17-14.13-10.5-1.1-.21-2.33,3.08-4.14-.77c.65-1.84,1.45-6.06,3.16-7.01.3-.17.79.36,1.24.26,3.71-.84,3.48-.66,6.8,1.39,8.93,5.53,16.27,12.19,23.58,19.66,1.4,1.43,3.03,1.87,4.26,3.7,4.13,6.16,2.78,16.33-2.92,21.17-2.05,1.74-12.21,7.51-14.81,8.52-6.41,2.48-12.92-.52-16.57-6.13-3.43,2.73-8.8,1.49-12.25,4.75,1.57,1.06,1.84,9.81,1.34,10.81-.39.77-3.35,4.22-4.13,4.96-3.93,3.75-16.1,8.23-21.53,7.51-7.45-.99-12.44-9.87-15.98-15.79l-2.53.94C1.71,60.96.24,59.47,0,58.03c-.08-.52,1.82-8.88,2.14-10.3,1.52-6.76,3.7-17.62,6.84-23.61.24-.46.04-1.27.41-1.77,3.38-2.6,7.5-3.8,11.26-1.19l-.32,3.28c-.41.51-1.03.45-1.59.51ZM39.12,47.4c-2.02,1.44-4.84,1.4-7.04,2.26-3.37,1.32-7.01,2.82-10.36,4.23-4.43,1.87-7.93,5.39-12.3,7.31l5.9,9.63c7.52,2.45,17.87-1.11,23.16-6.73.48-.51,2.97-4.31,3.03-4.85.47-4.08-.72-8.15-2.39-11.87ZM80.05,27.92c-6.86,1.79-13.65,7.13-20.55,9.39,1.35,5.5,3.29,13.34,10.46,13.42,3.34.03,12.52-2.21,14.84-4.46.48-.46,3.71-5.2,3.8-5.67s-.66-7.95-.87-8.74c-.59-2.19-1.86-4.63-4.5-4.48-.2.01-3,.5-3.18.55Z"
        />
      </g>
    </GlassesSvg>
  );
});

Glasses.displayName = "Glasses";
