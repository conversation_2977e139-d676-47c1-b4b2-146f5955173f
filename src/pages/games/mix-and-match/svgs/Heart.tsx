// Libraries
import React, { memo } from "react";
import styled from "styled-components";

interface HeartProps extends React.SVGProps<SVGSVGElement> {}

const HeartSvg = styled.svg`
  animation: animateHeart 2s steps(2, start) infinite;
  // Scale and rotate the heart
  @keyframes animateHeart {
    0% {
      transform: scale(1) rotate(0deg);
    }
    50% {
      transform: scale(1.2) rotate(10deg);
    }
    100% {
      transform: scale(1) rotate(0deg);
    }
  }
`;

export const Heart: React.FC<HeartProps> = memo((props) => {
  return (
    <HeartSvg
      viewBox="0 0 84 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M32.596 25.7237C37.0722 21.3944 45.483 13.2407 51.3309 11.4729C53.1719 10.9317 63.3154 9.30824 65.012 9.30824C68.1886 9.30824 70.0296 10.9678 71.8706 13.3851C73.3867 15.3333 83.3859 32.795 83.819 34.4546C84.9381 38.7479 80.7507 56.4622 78.5126 60.9719C70.7516 76.738 47.4684 92.3958 33.6428 102.786C29.7804 105.673 11.9841 124.108 9.52939 124C5.66691 121.872 1.76833 118.011 1.33515 113.357C0.613193 105.889 6.67765 90.9527 6.60546 81.7528C6.46107 62.2707 -5.74005 21.4665 3.32054 5.23143C3.93421 4.14909 6.56936 0.577369 7.50791 0.216589C14.4026 -2.52534 32.235 18.5803 32.5599 25.6876L32.596 25.7237ZM16.6407 13.349C10.9372 10.3545 10.5762 37.7377 10.7928 40.2631C11.1899 45.2419 12.9226 49.0662 13.4641 53.2151C15.3773 67.971 17.7958 82.6548 16.8934 97.4828C22.7052 90.9527 30.7189 87.3449 37.4692 82.0054C42.0537 78.3615 45.6635 73.7074 50.1035 69.9192C60.0666 61.3327 66.7086 59.8535 71.1847 45.2419C72.737 40.2271 74.975 25.3269 69.1994 22.621C65.6257 23.7034 59.8861 23.7034 56.8177 24.7496C56.0236 25.0382 45.6996 33.6609 44.1113 35.104C42.0176 37.0522 35.4838 46.8293 33.1375 46.6489C27.2535 43.5102 22.4164 36.3306 21.6944 29.5841C20.9003 22.1881 22.9939 19.843 16.6407 13.3129V13.349Z"
        fill="var(--color-primary)"
      />
    </HeartSvg>
  );
});

Heart.displayName = "Heart";
