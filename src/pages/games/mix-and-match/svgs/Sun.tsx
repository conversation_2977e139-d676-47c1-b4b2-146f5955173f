// Libraries
import React, { memo } from "react";
import { motion } from "motion/react";
import styled from "styled-components";

interface SunProps extends React.SVGProps<SVGSVGElement> {}

const SunSvg = styled(motion.svg)`
  .animation-rotate {
    transform-origin: center center;
    animation: rotate 3s steps(2, start) infinite;
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(-45deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }
`;

export const Sun: React.FC<SunProps> = memo((props) => {
  return (
    <SunSvg
      id="Layer_2"
      data-name="Layer 2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 133.36 136.9"
      {...(props as any)}
    >
      <g id="Layer_1-2" data-name="Layer 1" className="animation-rotate">
        <g>
          <motion.path
            strokeWidth={0}
            fill="#dc5634"
            d="M76.83,55.05c.18.4.43,6.77.24,7.25-.22.54-2.91,4.91-3.27,5.26-.39.38-5.32,3.29-5.71,3.34-2.3.32-5.05-1.4-7.25-.97.11-1.11-1.2-2.26-.24-3.21,1.78-1.75,5.36-.83,6.39-1.55,1.31-.92,3.06-5.26,4.39-6.59.21-.39,0-.62-.13-.95-.58-1.43-4.07-4.19-4.81-5.99-.61-.4-5.26-.47-5.94-.18-.5.21-4.26,3.67-4.89,4.33-.72.75-2.81,2.98-2.95,3.82-.06,4.73-.06,12.74,3.1,16.58.73.89,8.63,6.37,9.8,6.9.89.4,6.88,2.07,7.6,1.99,1.01-.11,6.81-2.23,7.44-2.88,5.8-5.95,6.2-13.04,3.51-20.55-2.04-5.7-4.74-8.47-9.51-12.05-14.77-11.09-32.65-4.63-38.46,12.37-3.87,11.33-.5,18.02,8.02,25.5,3.99,3.5,7.25,5.57,12.34,7.32,2.3.79,5.99,1.14,7.85,1.96.68.3,2.03,2.29,2.81,2.83-.02.17-2.74,1.84-2.93,1.9-1.17.36-4.36-1.57-5.73-2.1-10.73-4.08-23.96-9.41-27.92-21.51-1.46-4.47-1.44-16.38.31-20.58.63-1.52,6.24-9.23,7.53-10.51,4.42-4.41,13.07-7.49,19.29-7.21,5.74.26,14.13,1.2,18.88,4.46,3.83,2.63,10.35,11.46,12.42,15.68,1.07,2.19,2.7,11.76,2.41,14.11-.14,1.18-1.81,5.63-2.41,6.8-.35.69-3.92,5.55-4.34,5.84-1.74,1.19-10.89,3.48-12.96,3.28-1.13-.11-4.08-1.08-5.29-1.5-8.4-2.89-16.23-9.78-18.88-18.4-1.54-5.01-1.4-9.18.86-14.01,1.72-3.68,6.45-7.88,10.33-9.09.81-.25,6.83-.43,7.56-.22.78.22,4.94,3.12,5.77,3.82.57.48,4.63,4.4,4.77,4.71Z"
          />
          <path
            strokeWidth={0}
            fill="#dc5634"
            d="M101.3,85.78c.56-.06,4.93,2.51,5.98,2.97,5.05,2.24,9.91,4.45,14.89,6.87,2.35,1.14,7.43,2.79,9.2,4.15.62.48,2.25,3.21,1.95,3.96-.44.55-1.08,1.17-1.82.92-3.48-1.76-6.91-3.65-10.46-5.28-5.85-2.67-13.08-5.02-18.51-8.22-1.56-.92-2.41-2.1-3.35-3.61-.01-.4,1.75-1.72,2.12-1.76Z"
          />
          <rect
            strokeWidth={0}
            fill="#dc5634"
            x="65.56"
            y=".21"
            width="6.32"
            height="24.17"
            transform="translate(1.17 -5.34) rotate(4.49)"
          />
          <path
            strokeWidth={0}
            fill="#dc5634"
            d="M66.27,112.61c-1.2,8.13-2.54,16.06-1.81,24.29l-5.41-1.47c-.7-8.44.31-16.66,2.23-24.9l4.98,2.08Z"
          />
          <path
            strokeWidth={0}
            fill="#dc5634"
            d="M23.32,90.44l-20,8.05c-1.31.16-2.52-4.32-3.33-5.26l20.6-8.08,2.72,5.29Z"
          />
          <path
            strokeWidth={0}
            fill="#dc5634"
            d="M6.33,28.53l18.93,12.41,1.87,3.54-1.04,1.37-18.95-12.27c-1.63-1.64-1.33-3.01-.81-5.05Z"
          />
          <path
            strokeWidth={0}
            fill="#dc5634"
            d="M129.23,33.28c.11.2-1.73,1.46-2.13,1.6-3.06,1.11-8.48,1.51-12.07,2.5-5.66,1.56-12.06,4.09-17.25,6.83-.64.34-1.13,1.15-1.28,1.21-.73.29-4.17.25-4.81-.62l3.14-1.81c2.66-1.21,5.29-2.55,7.97-3.7,5.28-2.27,19.8-7.8,25.01-6.56.5.12.93.39,1.41.54Z"
          />
        </g>
      </g>
    </SunSvg>
  );
});

Sun.displayName = "Sun";
