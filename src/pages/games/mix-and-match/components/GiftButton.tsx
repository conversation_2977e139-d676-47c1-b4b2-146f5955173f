// Libraries
import React, { useCallback } from "react";

// Components
import { SecondaryButton } from "./SecondaryButton";

// Sound
import { sounds } from "sounds";

// Hooks
import { useNavigateWithSearch } from "hooks";

interface GiftButtonProps {}

export const GiftButton: React.FC<GiftButtonProps> = () => {
  const navigate = useNavigateWithSearch();

  const onRedirectVoucherListing = useCallback(() => {
    sounds.openPackage.play();
    navigate("/gift", {
      newParams: {
        tab: "redeemed",
        voucherType: "voucher",
      },
    });
  }, [navigate]);

  return (
    <SecondaryButton onClick={onRedirectVoucherListing}>
      Tú<PERSON> quà hè của bạn
    </SecondaryButton>
  );
};
