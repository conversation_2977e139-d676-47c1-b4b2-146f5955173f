// Libraries
import {
  Checkbox as AMACheck<PERSON>,
  CheckboxProps as AMACheckboxProps,
} from "@antscorp/ama-ui";
import {
  animate,
  motion,
  MotionValue,
  useMotionValue,
  useTransform,
} from "motion/react";
import React, { useEffect, useState } from "react";
import styled from "styled-components";
import { interpolate } from "flubber";

interface CheckboxProps extends AMACheckboxProps {}

const StyledCheckBox = styled(AMACheckbox)`
  --font-size: 12px;
  color: var(--color-secondary);

  line-height: 1.2;
  font-weight: 500;
`;

const PATHS = {
  checked:
    "M41.5933 8.65562C39.5433 8.26562 36.1933 7.50562 34.1733 8.46562C29.2833 10.7956 26.3433 15.0556 23.7933 19.6756C22.3133 22.3556 20.9333 25.0956 19.6133 27.8556C17.5433 24.8756 18.2233 25.3556 15.6633 22.7056C14.1933 21.1856 10.4033 21.1056 8.45335 20.9556C7.95335 20.9156 3.21335 20.8456 4.11335 21.7856C9.90335 27.7856 11.4333 31.2456 14.6933 38.9256C15.4433 40.7056 25.8133 42.3656 26.5933 40.4956C29.0933 34.4456 31.8233 28.4956 34.9033 22.7156C37.5233 17.7856 40.3633 13.0556 45.5733 10.5756C46.8833 9.94562 41.8233 8.69562 41.6033 8.65562H41.5933Z",
  unchecked:
    "M48.5906 13.5808C47.1812 11.3001 46.2584 9.01998 44.0049 6.73985C37.8629 4.00303 35.5304 1.7226 29.4671 2.17898C22.4709 1.72279 22.466 2.17891 17.34 3.09115C12.205 4.45904 11.2719 6.28346 8.0216 8.10837C5.67491 10.3882 4.2755 13.5808 2.41919 16.7737C1.01827 18.1416 0.553013 30.4556 1.48484 33.1925C3.8179 39.1213 5.675 39.1213 11.272 45.5068C11.9716 47.1305 26.8693 49.037 27.5969 47.3312C33.1894 46.4187 32.2613 46.8754 36.9299 43.6828C41.1276 39.5778 42.9936 37.7534 46.2585 32.2804C47.4805 31.7057 49.9899 19.966 50 20.4224L48.5906 13.5808Z",
};

const paths = [PATHS.unchecked, PATHS.checked];

const getIndex = (_: string, index: number) => index;

function useFlubber(progress: MotionValue<number>, paths: string[]) {
  return useTransform(progress, paths.map(getIndex), paths, {
    mixer: (a, b) => interpolate(a, b, { maxSegmentLength: 2 }),
  });
}

export const Checkbox: React.FC<CheckboxProps> = (props) => {
  const { ...restProps } = props;

  const [pathIndex, setPathIndex] = useState(0);
  const progress = useMotionValue(pathIndex);
  const path = useFlubber(progress, paths);

  useEffect(() => {
    const animation = animate(progress, pathIndex, {
      duration: 0.3,
      ease: "easeInOut",
    });

    setPathIndex(props.checked ? 1 : 0);

    return () => animation.stop();
  }, [pathIndex, progress, props.checked]);

  return (
    <StyledCheckBox
      icon={() => (
        <div className="relative flex items-center justify-center">
          <svg
            width="30"
            height="28"
            viewBox="0 0 65 63"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M31.1716 0.34329C25.9016 1.09329 20.6716 2.53329 16.0416 5.15329C6.32165 10.6633 -0.118351 21.6233 0.00164886 32.7933C0.121649 43.9633 6.89165 54.8533 16.9416 59.7333C26.9916 64.6133 39.8516 63.1233 48.4016 55.9333C52.8616 52.1833 56.0716 47.1633 58.8416 42.0333C61.6016 36.9233 64.0416 31.4433 64.2216 25.6433C64.8216 7.12329 47.1816 -1.92671 31.1716 0.34329Z"
              fill="var(--color-secondary)"
            />
          </svg>

          <svg
            className="absolute"
            width="22"
            height="22"
            viewBox="0 0 50 50"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <motion.path d={path} fill="var(--color-background-game)" />
          </svg>
        </div>
      )}
      {...restProps}
    />
  );
};
