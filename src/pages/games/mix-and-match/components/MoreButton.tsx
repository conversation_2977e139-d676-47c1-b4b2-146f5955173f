// Libraries
import React, { memo } from "react";
import styled from "styled-components";

interface MoreButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  width?: number;
  height?: number;
}

const MoreButtonWrapper = styled.div`
  color: var(--color-primary);
`;

export const MoreButton: React.FC<MoreButtonProps> = memo((props) => {
  const { width = 46, height = 41, ...restProps } = props;

  return (
    <MoreButtonWrapper {...restProps}>
      <svg
        data-name="more-button"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 115.22 97.28"
        width={width}
        height={height}
      >
        <g>
          <path
            fill="currentColor"
            strokeWidth={0}
            d="M54.98,0v6c6.73,0,13.5.8,19.59,2.3,6.79,1.68,14.49,3.59,21.4,10.07,14.19,13.32,16.34,36.76,9.45,50.28-3.66,7.19-10.33,12.88-19.81,16.89-8.86,3.75-19.54,5.74-30.89,5.74-20.96,0-39.49-6.87-45.06-16.7-4.07-7.18-4.2-21.09-2.96-31.72.93-7.97,1.54-13.24,4.57-18.44C17.99,12.89,34.33,6,54.98,6V0M54.98,0C35.24,0,14.87,6.33,6.08,21.4c-3.66,6.29-4.39,12.51-5.35,20.77-.36,3.07-2.78,23.93,3.7,35.37,7.11,12.55,28.33,19.74,50.28,19.74,23.04,0,46.89-7.93,56.05-25.91,8.49-16.68,4.78-42.85-10.69-57.38-8.04-7.55-16.91-9.75-24.06-11.52C69.5.87,62.29,0,54.98,0h0Z"
          />
          <g>
            <path
              fill="currentColor"
              strokeWidth={0}
              d="M72.39,27.03c-8.8,1.27-16.73.66-24.96-1.47-5.2-1.34-12.87.15-17.2,1.89-3.38,1.35-6.73,4.12-1.47,5.48,12.88,3.33,27.54,3.86,41.24,1.89,4.39-.63,12.78-2.06,13.35-5.17.54-2.99-7.56-3.09-10.96-2.6Z"
            />
            <path
              fill="currentColor"
              strokeWidth={0}
              d="M84.26,46.16c-1.88-2.94-6.77-3.58-9.92-2.94-7.13,1.45-14.41,2.11-21.74,1.6-7.09-.49-13.99-2.31-21.1-1.38-9.63,1.26-5.2,10.48,2.83,9.43,7.65-1,15.83,1.35,23.59,1.52,7.73.17,15.38-.69,22.9-2.23,3.01-.61,5.22-3.21,3.43-6Z"
            />
            <path
              fill="currentColor"
              strokeWidth={0}
              d="M85.5,62.74c-.66-2.69-4.52-4.77-6.43-5.06-8.31-1.29-14.76,3.89-22.65,5.86-4.51,1.12-8.7-1.05-13.11-2.04-4.62-1.03-9.31-1.39-13.98-1.24-2.3.08-2.63,1.94-1.46,4.15,1.31,2.46,4.78,4.22,7.03,4.15,4.46-.15,8.85.22,13.23,1.35,4.06,1.05,7.97,2.63,12.17,2.29,7.44-.61,14.56-7.65,22.02-6.49,1.81.28,3.91-.04,3.2-2.96Z"
            />
          </g>
        </g>
      </svg>
    </MoreButtonWrapper>
  );
});

MoreButton.displayName = "MoreButton";
