// Libraries
import { SpinLoading } from "@antscorp/ama-ui";
import React, { memo } from "react";
import styled, { css } from "styled-components";
import { motion } from "motion/react";

// Define the props for the PrimaryButton component
interface PrimaryButtonProps
  extends React.ComponentProps<typeof PrimaryButtonWrapper> {
  width?: number; // Optional width of the button
  height?: number; // Optional height of the button
  loading?: boolean; // Indicates if the button is in a loading state
  variant?: "short" | "long" | "supperLong"; // Variant of the button (short or long)
  backgroundColor?: string; // Optional background color for the button
}

// Styled component for the button wrapper
const PrimaryButtonWrapper = styled(motion.button)<{ $loading?: boolean }>`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Mansalva";
  font-size: 23px;
  color: var(--color-background-game);
  cursor: pointer;
  transition: all 0.3s ease-in-out;

  &:active {
    transform: scale(0.95); // Scale down on click
  }

  .primary-button {
    &__content {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      z-index: 1; // Ensure content is above the SVG background
    }
  }

  // Apply styles when the button is disabled or in a loading state
  ${({ disabled, $loading }) =>
    (disabled || $loading) &&
    css`
      opacity: 0.5;
      pointer-events: none; // Disable interactions
    `}
`;

const variants = {
  short: {
    viewBox: "0 0 419.4 112.99",
    path: "M21.67,15.54C49.66-7.51,83.03,1.35,160.1,3.79c45.05,1.43,84.06,0,105.39-.78,74.48-2.73,103.21-8.9,129.71,11.9,4.81,3.78,23.09,18.12,24.14,39.7,1.08,22.2-16.72,38.64-20.06,41.73-23.5,21.71-52.43,16.7-135.18,14.7-53.75-1.29-109.73-.82-117.6-.75-84.98.77-113.49,5.04-132.07-16.4-1.08-1.24-18.11-21.49-13.7-45.79C3.93,30.46,16.85,19.51,21.67,15.54Z",
    width: 150,
    height: 40,
  },
  long: {
    viewBox: "0 0 495 99",
    path: "M20.0855 13.6036C46.0317 -6.57111 76.965 1.1837 148.407 3.31933C190.168 4.57095 331.579 3.31933 351.36 2.63663C420.402 0.247168 447.034 -5.15319 471.599 13.0522C476.058 16.3607 493.003 28.9119 493.976 47.8C494.977 67.2308 478.477 81.6201 475.381 84.3247C453.597 103.327 426.779 98.9415 350.072 97.191C300.247 96.0619 143.096 96.4733 135.8 96.5345C57.0257 97.2085 30.5975 100.946 13.3742 82.1803C12.3731 81.0949 -3.4134 63.3709 0.674577 42.1021C3.64091 26.6625 15.6175 17.0784 20.0855 13.6036Z",
    width: 193,
    height: 41,
  },
  supperLong: {
    viewBox: "0 0 617 100",
    path: "M25.0856 13.7549C57.4907 -6.64419 96.1245 1.19686 185.352 3.35625C237.508 4.62179 414.121 3.35625 438.827 2.66595C525.056 0.249917 558.317 -5.2105 588.998 13.1974C594.566 16.5427 615.73 29.2335 616.945 48.3317C618.196 67.9786 597.588 82.5279 593.721 85.2625C566.514 104.476 533.021 100.042 437.218 98.272C374.989 97.1303 178.718 97.5463 169.606 97.6082C71.2215 98.2897 38.2144 102.069 16.7035 83.0943C15.4532 81.9969 -4.26313 64.0758 0.842504 42.5704C4.54727 26.9591 19.5053 17.2684 25.0856 13.7549Z",
    width: 245,
    height: 41,
  },
};

<svg
  width="629"
  height="99"
  viewBox="0 0 629 99"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
>
  <path
    d="M25.5735 13.6174C58.6089 -6.57775 97.994 1.18489 188.956 3.32269C242.127 4.57557 422.175 3.32269 447.362 2.63929C535.267 0.247418 569.176 -5.1584 600.453 13.0654C606.13 16.3772 627.705 28.9412 628.944 47.8484C630.219 67.2988 609.21 81.7026 605.268 84.4099C577.532 103.431 543.387 99.0415 445.721 97.2893C382.282 96.159 182.194 96.5708 172.905 96.6321C72.6067 97.3068 38.9576 101.048 17.0284 82.2634C15.7537 81.1769 -4.34604 63.435 0.85889 42.1447C4.63571 26.6895 19.8846 17.0957 25.5735 13.6174Z"
    fill="#DC5634"
  />
</svg>;

// PrimaryButton functional component
export const PrimaryButton: React.FC<PrimaryButtonProps> = memo(
  ({
    width,
    height,
    children,
    loading,
    variant = "short",
    backgroundColor = "var(--color-primary)",
    ...restProps
  }) => {
    return (
      <PrimaryButtonWrapper $loading={loading} {...restProps}>
        {/* SVG background for the button */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox={variants[variant].viewBox}
          width={width || variants[variant].width}
          height={height || variants[variant].height}
        >
          <path
            strokeWidth={0}
            fill={backgroundColor}
            d={variants[variant].path}
          />
        </svg>
        {/* Button content */}
        <div className="primary-button__content">
          {loading ? (
            <SpinLoading
              className="-mt-[60%]"
              style={{
                "--size": "24px",
                "--color": "#fff",
              }}
            />
          ) : (
            (children as any)
          )}
        </div>
      </PrimaryButtonWrapper>
    );
  }
);

PrimaryButton.displayName = "PrimaryButton";
