// Libraries
import React, { memo } from "react";
import styled from "styled-components";
import { motion } from "motion/react";

interface SecondaryButtonProps
  extends React.ComponentProps<typeof SecondaryButtonWrapper> {
  width?: number;
  height?: number;
}

const SecondaryButtonWrapper = styled(motion.div)`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Mansalva";
  font-size: 20px;
  color: var(--color-primary);
  cursor: pointer;

  .secondary-button {
    &__content {
      position: absolute;
      z-index: 1;
    }
  }
`;

export const SecondaryButton: React.FC<SecondaryButtonProps> = memo((props) => {
  const { width = 193, height = 41, children, ...restProps } = props;

  return (
    <SecondaryButtonWrapper {...restProps}>
      <svg
        viewBox="0 0 495 99"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
      >
        <path
          d="M400.51 6C423.54 6 455.71 7.22 469.16 17.77C481.14 27.17 487.48 37.37 488 48.09C488.3 54.3 486.67 66.69 472.36 79.91C459.9 91.42 424.64 92.89 395.32 92.89C379.01 92.89 361.27 92.38 342.5 91.85C334.54 91.62 326.32 91.39 318.06 91.19C310.68 91.01 242.3 90.6 177.15 90.53C174.63 90.53 172.12 90.53 169.67 90.53C161.84 90.53 154.34 90.54 147.17 90.55C140.6 90.56 134.31 90.57 128.27 90.57C94.6 90.57 65.1 90.32 45.21 87.19C27.01 84.33 17.02 79.35 11.8 70.53C4.02 57.38 6.35 43.3 6.37 43.18C6.98 39.81 8.85 29.56 22.61 18.23C35.86 7.32 69.36 6.06 93.41 6.06C112.47 6.06 133.74 7 154.31 7.92C165.73 8.43 177.54 8.95 188.86 9.31C200.08 9.67 214.68 9.85 232.26 9.85C266.87 9.85 305.72 9.13 319.35 8.63C327.26 8.34 335.25 8 342.98 7.68C363.42 6.82 382.72 6 400.52 6M400.51 0C375.65 0 347.37 1.6 319.12 2.63C306.2 3.1 267.77 3.85 232.25 3.85C216.43 3.85 201.19 3.7 189.04 3.32C157.31 2.31 123.19 0.07 93.4 0.07C59.83 0.07 31.76 2.92 18.78 13.61C14.64 17.02 3.27 26.66 0.450002 42.11C0.450002 42.11 -2.49 58.21 6.62 73.59C19.31 95.03 53.77 96.58 128.26 96.58C140.88 96.58 154.64 96.54 169.65 96.54C172.1 96.54 174.6 96.54 177.12 96.54C240.81 96.61 310.48 97.02 317.89 97.2C345.38 97.86 371.88 98.9 395.3 98.9C433.58 98.9 463.65 96.11 476.42 84.33C479.34 81.63 494.93 67.24 493.98 47.81C493.06 28.92 477.06 16.37 472.85 13.06C459.7 2.74 433.02 0.01 400.5 0.01L400.51 0Z"
          fill="currentColor"
        />
      </svg>
      <span className="secondary-button__content">
        {children as React.ReactNode}
      </span>
    </SecondaryButtonWrapper>
  );
});

SecondaryButton.displayName = "SecondaryButton";
