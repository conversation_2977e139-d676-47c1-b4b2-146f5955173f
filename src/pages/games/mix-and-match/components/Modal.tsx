// Libraries
import React from "react";
import {
  Modal as AMAModal,
  ModalProps as AMAModalProps,
} from "@antscorp/ama-ui";
import styled from "styled-components";

interface ModalProps extends AMAModalProps {}

const StyledModal = styled(AMAModal)`
  --color-primary: #dc5634;
  --color-main-primary: #dc5634;
  --color-secondary: #356a3c;

  color: var(--color-secondary);

  .adm-modal-content {
    color: var(--color-secondary);
  }

  .adm-button {
    --color: var(--color-primary);
  }
`;

export const Modal: React.FC<ModalProps> = (props) => {
  const { ...restProps } = props;

  return <StyledModal {...restProps} />;
};
