// Libraries
import React, { memo, useCallback, useMemo, useState } from "react";
import { AnimatePresence, motion } from "motion/react";
import styled from "styled-components";
import { useImmer } from "use-immer";
import clsx from "clsx";

// Hooks
import { useDelay, useNavigateWithSearch, useUserInfo } from "hooks";

// Components
import { MoreButton } from "./MoreButton";
import { CloseIcon, ConditionalRenderer, HeaderTextLogo } from "components";

// Assets
import imgIntro4 from "assets/images/mix-and-match/img-intro-4.webp";
import imgIntro3 from "assets/images/mix-and-match/img-intro-3.webp";
import imgCup from "assets/images/mix-and-match/cup.webp";

// SVGs
import { Glasses, Sun } from "../svgs";

// Utils
import { maskPhoneNumber, padToTwoDigits } from "utils";
import { PrimaryButton } from "./PrimaryButton";
import { SecondaryButton } from "./SecondaryButton";

// Styled
import { CloseButton } from "styled";

// Constants
import { ROUTES } from "constant";

// Sounds
import { sounds } from "sounds";

// Queries
import { useGetLeaderBoard } from "queries";

interface LeaderBoardProps {
  moreButtonProps?: React.ComponentProps<typeof MoreButton>;
  primaryButtonProps?: React.ComponentProps<typeof PrimaryButton>;
}

const LeaderBoardWrapper = styled(motion.div)`
  position: absolute;
  max-width: var(--max-width);
  inset: 0;
  z-index: 1000;
  background-color: var(--color-background-game);
  padding-top: var(--header-padding-top);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  overflow: auto;
  text-align: center;

  .close-button {
    position: absolute;
    top: var(--header-padding-top);
    right: 24px;
  }
`;

const LeaderBoardTableWrapper = styled(motion.div)`
  color: var(--color-main-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: var(--adm-font-size-main);

  .leaderboard-table {
    width: 100%;

    tr {
      td,
      th {
        padding: 8px 0;
        border: 2px solid var(--color-main-primary);
      }

      th:first-child,
      td:first-child {
        border-left: none;
      }
      th:last-child,
      td:last-child {
        border-right: none;
      }

      &:first-child {
        th {
          border-top: none;
        }
      }

      &:last-child {
        td {
          border-bottom: none;
        }
      }

      &.current-user {
        td {
          /* background-color: var(--color-secondary); */
          color: var(--color-secondary);
          font-weight: bold;
        }
      }
    }
  }
`;

const BannerWrapper = styled(motion.div)`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-main-primary);
  width: 100%;

  .img-intro {
    &__asset-1 {
      top: -31%;
      z-index: 2;
      transform-origin: bottom;
    }
    &__asset-2 {
      top: 19%;
      z-index: 3;
    }
    &__cup {
      position: absolute;
      top: 0;
      left: 0;
    }
    &__sun {
      position: absolute;
      right: 14%;
      top: -42%;
    }
  }
`;

const Banner = memo(() => {
  const isShowEffect = useDelay(1000);

  return (
    <BannerWrapper className="mt-5">
      <div className="relative h-xs:left-[-6%] left-[-10%]">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-[45px] h-xs:text-[36px] leading-none font-mansalva"
        >
          Vị Hè <br /> Trong Tay
        </motion.div>
        {isShowEffect && (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
            <Sun width={40} className="img-intro__sun" />
            <motion.img
              animate={{
                rotate: [20, 0, -10],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: [0, 1, 0, 1],
                repeatType: "reverse",
              }}
              className="img-intro__cup"
              src={imgCup}
              width={27}
              height={29.65}
            />
          </motion.div>
        )}
      </div>
      <motion.img
        initial={{ opacity: 0, y: "10%", rotate: 20 }}
        animate={{ opacity: 1, y: "0%", rotate: 0 }}
        transition={{ duration: 0.5, ease: "easeInOut", delay: 0.3 }}
        className="absolute img-intro__asset-1 h-xs:w-[13.2%] h-xs:right-[29%] w-[16%] right-[27%]"
        src={imgIntro4}
      />
      <motion.img
        initial={{ opacity: 0, scale: 1.5, rotate: 20 }}
        animate={{ opacity: 1, scale: 1, rotate: 0 }}
        transition={{ duration: 0.5, ease: "easeInOut", delay: 0.6 }}
        className="absolute img-intro__asset-2 h-xs:w-[17%] h-xs:right-[18%] w-[20%] right-[15%]"
        src={imgIntro3}
      />
      {isShowEffect && (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
          <Glasses width={30} className="absolute right-[19%] top-[-10%]" />
        </motion.div>
      )}
    </BannerWrapper>
  );
});

Banner.displayName = "Banner";

const LeaderBoardTable = memo(() => {
  const { userInfo } = useUserInfo();

  const { data: leaderBoardData, isLoading } = useGetLeaderBoard({
    args: {
      phoneNumber: userInfo?.phoneNumber || "",
    },
    options: {
      refetchOnMount: "always",
    },
  });

  // Memos
  const leaderBoardItems = useMemo(() => {
    return leaderBoardData?.data || [];
  }, [leaderBoardData]);

  return (
    <ConditionalRenderer
      isLoading={isLoading}
      isEmpty={!leaderBoardItems.length}
      emptyProps={{
        image: <div />,
        title: "🍧 Chưa ai mix đủ độ mát!",
        description: (
          <>
            Bảng xếp hạng vẫn đang chill một mình… <br /> Vào phá đảo và ghi
            danh thôi
          </>
        ),
      }}
    >
      <LeaderBoardTableWrapper
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ ease: "easeInOut", delay: 0.5 }}
        className="px-4 xs:px-8 font-mansalva w-full"
      >
        {/* Render your leaderboard table here */}
        <table className="leaderboard-table">
          <thead>
            <tr>
              <th>Hạng</th>
              <th>Số điện thoại</th>
              <th>Điểm - Thời gian chơi</th>
            </tr>
          </thead>
          <tbody>
            {/* Example data, replace with actual leaderboard data */}
            {leaderBoardItems.map((item, index) => {
              return (
                <tr
                  key={item.userId}
                  className={clsx({
                    "current-user": item.phone === userInfo?.phoneNumber,
                  })}
                >
                  <td>
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: (index + 8) * 0.1 }}
                    >
                      {padToTwoDigits(item.rank)}
                    </motion.div>
                  </td>
                  <td>
                    <motion.span
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: (index + 8) * 0.1 }}
                    >
                      {maskPhoneNumber(item.phone)}
                    </motion.span>
                  </td>
                  <td>
                    <motion.span
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: (index + 8) * 0.1 }}
                    >
                      {`${item.score} điểm - ${item.playTime} giây`}
                    </motion.span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </LeaderBoardTableWrapper>
    </ConditionalRenderer>
  );
});

LeaderBoardTable.displayName = "LeaderBoardTable";

export const LeaderBoard: React.FC<LeaderBoardProps> = memo((props) => {
  const { moreButtonProps, primaryButtonProps } = props;
  const { children: primaryButtonChildren } = primaryButtonProps || {};

  const navigate = useNavigateWithSearch();

  const [state, setState] = useImmer({
    isOpenLeaderBoard: false,
  });

  // Variables
  const { isOpenLeaderBoard } = state;

  const onClickOpenLeaderBoard = useCallback(() => {
    sounds.bubblePop.play();

    setState((draft) => {
      draft.isOpenLeaderBoard = !draft.isOpenLeaderBoard;
    });
  }, [setState]);

  const onClose = useCallback(() => {
    setState((draft) => {
      draft.isOpenLeaderBoard = false;
    });
  }, [setState]);

  return (
    <>
      <MoreButton {...moreButtonProps} onClick={onClickOpenLeaderBoard} />

      <AnimatePresence>
        {isOpenLeaderBoard && (
          <LeaderBoardWrapper
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <CloseButton className="close-button" onClick={onClose}>
              <CloseIcon />
            </CloseButton>

            <HeaderTextLogo className="shrink-0 mb-8" color={"#A43534"} />

            <div className="flex flex-col justify-center flex-1 w-full gap-20">
              <Banner />

              <LeaderBoardTable />
            </div>

            <motion.div className="shrink-0 py-8 flex flex-col gap-4">
              <PrimaryButton variant="long" {...primaryButtonProps}>
                {primaryButtonChildren}
              </PrimaryButton>
              <SecondaryButton
                style={{
                  color: "#356A3C",
                }}
                onClick={() => {
                  sounds.openPackage.play();
                  navigate(ROUTES.GIFT.path, {
                    newParams: {
                      tab: "redeemed",
                      voucherType: "voucher",
                    },
                  });
                }}
              >
                Túi quà hè của bạn
              </SecondaryButton>
            </motion.div>
          </LeaderBoardWrapper>
        )}
      </AnimatePresence>
    </>
  );
});

LeaderBoard.displayName = "LeaderBoard";
