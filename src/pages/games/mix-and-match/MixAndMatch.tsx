// Libraries
import { AnimatePresence } from "motion/react";
import React, { useEffect, useRef } from "react";
import { useRecoilState } from "recoil";
import { useImmer } from "use-immer";
import { How<PERSON>, Howl } from "howler";

// State
import { mixAndMatchState, mixAndMatchStateDefault } from "./state";

// Screens
import { GameScreen, ResultScreen, StartScreen } from "./screens";

// Components
import { VolumeControl } from "components";

// Styled
import { MixAndMatchWrapper } from "./styled";

interface MixAndMatchProps {}

export const MixAndMatch: React.FC<MixAndMatchProps> = (props) => {
  // Hooks
  const [{ currentScreen, allocatedVoucher }, setMixAndMatch] =
    useRecoilState(mixAndMatchState);

  const [state, setState] = useImmer({
    isShowGameScreen: false,
    isShowResultScreen: false,
  });
  const { isShowGameScreen, isShowResultScreen } = state;

  // Sounds
  const sounds = useRef({
    bgMusic: new Howl({
      src: ["https://st-media-template.antsomi.com/file/summer.mp3"],
      volume: 0.4,
      // html5: true,
      autoplay: false,
      preload: true,
      // onend: () => {
      //   sounds.current.bgMusic.play();
      // },
    }),
  });

  useEffect(() => {
    if (!sounds.current.bgMusic.playing()) {
      Howler.ctx.resume();
      sounds.current.bgMusic.play();
    }
  }, []);

  useEffect(() => {
    if (currentScreen === "game" && !sounds.current.bgMusic.playing()) {
      Howler.ctx.resume();
      sounds.current.bgMusic.play();
    }
  }, [currentScreen]);

  useEffect(() => {
    return () => {
      sounds.current.bgMusic.stop();
      sounds.current.bgMusic.unload();

      setMixAndMatch(mixAndMatchStateDefault);
    };
  }, [setMixAndMatch]);

  return (
    <MixAndMatchWrapper className="w-full h-full flex items-center justify-center">
      <AnimatePresence
        onExitComplete={() => {
          setState((draft) => {
            draft.isShowGameScreen = true;
          });
        }}
      >
        {currentScreen === "start" && <StartScreen />}
      </AnimatePresence>
      <AnimatePresence
        onExitComplete={() => {
          setState((draft) => {
            draft.isShowResultScreen = true;
          });
        }}
      >
        {currentScreen === "game" && isShowGameScreen && <GameScreen />}
      </AnimatePresence>
      <AnimatePresence>
        {currentScreen === "result" &&
          isShowResultScreen &&
          allocatedVoucher && <ResultScreen />}
      </AnimatePresence>

      <VolumeControl />

      {/* <ResultScreen /> */}
      {/* <GameScreen /> */}
    </MixAndMatchWrapper>
  );
};
