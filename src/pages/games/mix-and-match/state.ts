// Libraries
import { atom } from "recoil";

// Types
import { AllocateVoucher } from "schemas";

type MixAndMatchScreen = "start" | "game" | "result";

type MixAndMatchState = {
  currentScreen?: MixAndMatchScreen;
  allocatedVoucher: AllocateVoucher | null;
};

export const mixAndMatchStateDefault: MixAndMatchState = {
  currentScreen: "start",
  allocatedVoucher: null,
};

export const allocatedVoucherDefault: AllocateVoucher = {
  webContents: {} as any,
  status: false,
  responseTime: "",
  serverName: "",
  serverIP: "",
  serverTime: 1,
  storyId: 1,
  variantId: 1,
};

export const mixAndMatchState = atom<MixAndMatchState>({
  key: "mixAndMatchState",
  default: mixAndMatchStateDefault,
});
