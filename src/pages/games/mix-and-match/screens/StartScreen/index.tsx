// Libraries
import { motion } from "motion/react";
import React, { memo, useCallback, useEffect, useState } from "react";
import { useRecoilState } from "recoil";
import styled from "styled-components";
import { closeApp } from "zmp-sdk";
import { useImmer } from "use-immer";
import { Howl } from "howler";

// Assets
import logo from "assets/images/mix-and-match/highlands-logo.png";
import PickSound from "assets/sound-effects/pick.mp3";

// State
import { mixAndMatchState } from "../../state";

// Components
import { ImagesIntro, RequestPermission } from "./components";
import {
  Checkbox,
  MoreButton,
  PrimaryButton,
  SecondaryButton,
  Modal,
  LeaderBoard,
  GiftButton,
} from "pages/games/mix-and-match/components";

// Hooks
import { useMixAndMatchLocalStorage } from "../../hooks";
import {
  useAppConfig,
  useNavigateWithSearch,
  useRemainPlays,
  useRequestZaloPermissions,
  useUserInfo,
  useViewPage,
} from "hooks";

// Constants
import { APP_CONFIG, EVENT_CONFIG, ZMA_ERROR } from "constant";
import { sounds } from "sounds";
import { padToTwoDigits } from "utils";

interface StartScreenProps {}

const StartScreenWrapper = styled(motion.div)`
  .start-screen {
    &__logo {
      margin-top: 55px;
      height: auto;
    }

    &__title {
      font-family: "Mansalva";
      text-align: center;
      line-height: 1;
      /* font-size: 48px; */
      /* margin-top: 16px; */
    }
  }
`;

const Footer = styled(motion.div)`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  max-height: 200px;
  padding: 0px 10px 24px 10px;
  z-index: 100;
`;

export const StartScreen: React.FC<StartScreenProps> = memo((props) => {
  // Hooks
  const navigate = useNavigateWithSearch();
  const {
    appSettings,
    gameValidation,
    isLoading: isLoadingAppConfig,
  } = useAppConfig();
  const [{ isAcceptRule }, setMixAndMatchLocalStorage] =
    useMixAndMatchLocalStorage();
  const [mixAndMatch, setMixAndMatch] = useRecoilState(mixAndMatchState);
  const { requestZaloPermissions } = useRequestZaloPermissions({
    cdpEventConfig: {
      pageCate: EVENT_CONFIG.MIX_AND_MATCH,
    },
    onFail({ step, error }) {
      if (["flowOA", "allowPhone"].includes(step)) {
        setState((draft) => {
          draft.isShowRequestPermissionMessage = true;
        });
      }

      // If state code equal -203 it means user request so many times, then show limit Request message
      if (error.code === -203) {
        setState((draft) => {
          draft.isShowLimitRequestMessage = true;
          draft.isShowRequestPermissionMessage = false;
        });
      }

      setState((draft) => {
        draft.isLoading = false;
      });
    },
    onFinish() {
      setState((draft) => {
        draft.isLoading = false;
        draft.isShowRequestPermissionMessage = false;
      });

      setMixAndMatch((prev) => ({
        ...prev,
        currentScreen: "game",
      }));

      const pick = new Howl({
        src: PickSound,
        volume: 0.5,
      });

      pick.play();
    },
  });

  useViewPage({
    pageCate: EVENT_CONFIG.MIX_AND_MATCH,
    pageType: "home",
  });

  const { remainPlays, isCanPlay } = useRemainPlays({
    apiConfig: {
      endPoint: "/mix-match/can-allocate",
    },
  });

  // State
  const [state, setState] = useImmer({
    isLoading: false,
    isShowRequestPermissionMessage: false,
    isShowLimitRequestMessage: false,
    isAccept: false,
  });

  // Variables
  const {
    isLoading,
    isShowRequestPermissionMessage,
    isShowLimitRequestMessage,
  } = state || {};
  const { systemErrorMessages } = appSettings?.globals || {};
  const {
    gameEndMessage,
    bonusPlays = APP_CONFIG.GAMES.MIX_AND_MATCH.BONUS_PLAYS,
  } = appSettings?.games?.mixAndMatch || {};

  useEffect(() => {
    if (isAcceptRule) {
      setState((draft) => {
        draft.isAccept = true;
      });
    }
  }, [isAcceptRule, setState]);

  // Handlers
  const onClickStart = useCallback(() => {
    setState((draft) => {
      draft.isLoading = true;
    });
    requestZaloPermissions();
  }, [requestZaloPermissions, setState]);

  const onRedirectVoucherListing = useCallback(() => {
    sounds.openPackage.play();
    navigate("/gift", {
      newParams: {
        tab: "redeemed",
        voucherType: "voucher",
      },
    });
  }, [navigate]);

  return (
    <>
      <StartScreenWrapper
        className="w-full h-full flex flex-col items-center overflow-auto hide-scrollbar"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <img
          className="start-screen__logo w-[30%] h-xs:w-[25%]"
          src={logo}
          alt="Highlands Coffee Logo"
        />

        <div className="flex flex-col h-full w-full items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 2 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{
              delay: 0.3,
            }}
            className="start-screen__title text-primary text-[65px] h-xs:text-[48px]"
          >
            Vị Hè <br /> Trong Tay
          </motion.div>

          <ImagesIntro />

          <Footer
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              delay: 1.2,
            }}
            className="mt-10 xxs:!px-[50px]"
          >
            <div className="flex flex-col w-full justify-center">
              <PrimaryButton
                variant="supperLong"
                loading={isLoading}
                disabled={!state.isAccept}
                onClick={onClickStart}
              >
                Chiến game ngay
              </PrimaryButton>
              <div className="flex items-center justify-center gap-2 mt-5">
                <GiftButton />

                <LeaderBoard
                  primaryButtonProps={{
                    style: {
                      display: state.isAccept ? "flex" : "none",
                    },
                    variant: "long",
                    children: "Chiến game ngay",
                    onClick: onClickStart,
                  }}
                />
              </div>
              <div className="mx-auto text-primary my-4 font-medium text-center leading-[1.2]">
                Bạn còn {remainPlays} lượt chơi
                {/* {isCanPlay ? (
                `Bạn còn ${padToTwoDigits(remainPlays)} lượt chơi`
              ) : (
                <>
                  Bạn đã hết lượt chơi hôm nay <br /> Đến Highlands - nhận thêm{" "}
                  {padToTwoDigits(bonusPlays)} lượt chơi <br /> cho mỗi lượt mua
                  hàng có tích điểm nhaaa!
                </>
              )} */}
              </div>
            </div>

            <Checkbox
              checked={state.isAccept}
              onChange={(checked) => {
                if (checked) {
                  setMixAndMatchLocalStorage((prev) => ({
                    ...prev,
                    isAcceptRule: true,
                  }));
                }

                setState((draft) => {
                  draft.isAccept = checked;
                });
              }}
            >
              Tôi xác nhận đã đọc hiểu, đồng ý với các <br /> điều kiện và điều
              khoản của trò chơi.
            </Checkbox>
          </Footer>
        </div>
      </StartScreenWrapper>

      {/* Notification Modal */}
      <Modal
        title="Thông báo"
        getContainer={document.body}
        content={
          <div className="text-center">
            {systemErrorMessages?.limitRequest || ZMA_ERROR["-203"]?.message}
          </div>
        }
        closeOnMaskClick={false}
        onClose={() => closeApp()}
        visible={isShowLimitRequestMessage}
        actions={[
          {
            key: "close",
            text: "Đóng",
            primary: true,
            onClick: () => closeApp(),
          },
        ]}
      />

      <Modal
        title="Thông báo"
        getContainer={document.body}
        content={
          <div
            className="text-center"
            dangerouslySetInnerHTML={{
              __html: gameValidation.mixAndMatch.notAvailableMessage,
            }}
          />
        }
        onClose={() => closeApp()}
        visible={!gameValidation.mixAndMatch.isValid && !isLoadingAppConfig}
        actions={[
          {
            key: "start",
            text: "Quà tặng của tôi",
            primary: true,
            onClick: () => onRedirectVoucherListing(),
          },
        ]}
      />

      <RequestPermission
        visible={isShowRequestPermissionMessage}
        onClickStart={onClickStart}
      />
    </>
  );
});

StartScreen.displayName = "StartScreen";
