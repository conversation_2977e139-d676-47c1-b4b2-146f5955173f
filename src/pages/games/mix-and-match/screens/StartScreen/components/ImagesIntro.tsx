// Libraries
import React, { memo } from "react";
import { motion } from "motion/react";
import styled from "styled-components";

// Images
import imgIntro1Default from "assets/images/mix-and-match/img-intro-1.webp";
import imgIntro2Default from "assets/images/mix-and-match/img-intro-2.webp";
import imgIntro5Default from "assets/images/mix-and-match/img-intro-5.webp";
import imgCup from "assets/images/mix-and-match/cup.webp";

// Svg
import { Glasses, Sun } from "pages/games/mix-and-match/svgs";
import { useAppConfig } from "hooks";

interface ImagesIntroProps {}

const ImagesIntroWrapper = styled(motion.div)`
  /* min-height: 180px; */
  padding-bottom: 20px;

  .img-intro {
    &__asset-1 {
      z-index: 2;
      transform-origin: bottom;
    }
    &__asset-2 {
      top: 20px;
      z-index: 3;
    }
    &__sun {
      position: absolute;
      left: 10%;
      z-index: 1;
    }
    &__cup {
      position: absolute;
      left: 35%;
      top: 184px;
      height: fit-content;
      z-index: 5;
    }
    &__glasses {
      position: absolute;
      right: 34%;
      z-index: 4;
    }
  }
`;

export const ImagesIntro: React.FC<ImagesIntroProps> = memo((props) => {
  const { appSettings } = useAppConfig();
  const { introImages } = appSettings?.games.mixAndMatch.home || {};
  const [imgIntro1 = imgIntro1Default, imgIntro2 = imgIntro2Default] =
    introImages || [];

  return (
    <ImagesIntroWrapper className="relative flex mt-5 justify-center w-full">
      <motion.img
        initial={{ opacity: 0, y: "10%", rotate: 20 }}
        animate={{ opacity: 1, y: "0%", rotate: 0 }}
        transition={{ duration: 0.5, ease: "easeInOut", delay: 0.3 }}
        className="absolute img-intro__asset-1 h-xs:w-[24%] h-xs:left-[26%] w-[36%] left-[16%]"
        src={imgIntro1}
      />
      <motion.img
        initial={{ opacity: 0, scale: 1.5, rotate: 20 }}
        animate={{ opacity: 1, scale: 1, rotate: 0 }}
        transition={{ duration: 0.5, ease: "easeInOut", delay: 0.6 }}
        className="relative img-intro__asset-2 w-[46%] right-[-44%] h-xs:w-[31%] h-xs:right-[-44%]"
        src={imgIntro2}
      />

      <motion.div
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
      >
        <Sun width={50} className="img-intro__sun h-xs:!left-[18%]" />
        <motion.div className="img-intro__glasses origin-center w-fit">
          <Glasses width={40} />
        </motion.div>
        <motion.img
          animate={{
            rotate: [20, 0, -10],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: [0, 1, 0, 1],
            repeatType: "reverse",
          }}
          className="img-intro__cup h-xs:!top-[110px]"
          src={imgCup}
          width={40}
          height={44}
        />
      </motion.div>
    </ImagesIntroWrapper>
  );
});

ImagesIntro.displayName = "ImagesIntro";
