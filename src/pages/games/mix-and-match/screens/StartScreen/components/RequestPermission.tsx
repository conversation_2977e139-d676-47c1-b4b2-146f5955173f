// Libraries
import React, { memo } from "react";
import { AnimatePresence, motion } from "motion/react";
import styled from "styled-components";

// Assets
import logo from "assets/images/mix-and-match/highlands-logo.png";

// Hooks
import { useAppConfig } from "hooks";

// Constants
import { APP_CONFIG } from "constant";

// Components
import { PrimaryButton } from "pages/games/mix-and-match/components";

// Svg
import { Glasses, Heart, Sun } from "pages/games/mix-and-match/svgs";

interface RequestPermissionProps {
  visible?: boolean;
  onClickStart?: () => void;
}

// Styled component for the request permission modal
const RequestPermissionWrapper = styled(motion.div)`
  position: absolute;
  inset: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--color-background-game);

  .request-permission {
    &__logo {
      margin-top: 55px;
      width: 25%;
      height: auto;
    }

    &__container {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      box-shadow: 3px 3px 3px rgba(34, 34, 34, 0.3);
      margin-top: 10%;
      width: 75%;
      padding: 24px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: var(--color-secondary);
      z-index: 20;
    }

    &__sun {
      position: absolute;
      left: 0;
      z-index: 1;
    }
  }

  @media screen and (max-height: 600px) {
    .request-permission {
      &__container {
        width: 80%;
        font-size: 14px;
      }
    }
  }
`;

export const RequestPermission: React.FC<RequestPermissionProps> = memo(
  ({ visible, onClickStart }) => {
    const { appSettings } = useAppConfig();

    // Extracting configuration values
    const { games } = appSettings || {};
    const { requestPermissionMessage } = games?.mixAndMatch || {};

    return (
      <AnimatePresence>
        {visible && (
          <RequestPermissionWrapper
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {/* Logo */}
            <img
              className="request-permission__logo"
              src={logo}
              alt="Highlands Coffee Logo"
            />

            <div className="relative w-full flex flex-col items-center">
              {/* Animated assets */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
                className="absolute w-full h-full z-10"
              >
                <Sun width={100} className="request-permission__sun" />
                <Glasses
                  width={78}
                  className="absolute right-[15%] -bottom-[20%]"
                />
                <Heart width={24} className="absolute bottom-0 right-4" />
              </motion.div>

              {/* Modal content */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.5 }}
                className="request-permission__container"
              >
                <div className="mb-6 text-lg">
                  Điều kiện tham gia chương trình
                </div>
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      requestPermissionMessage ||
                      APP_CONFIG.GAMES.MIX_AND_MATCH.REQUEST_PERMISSION_MSG,
                  }}
                />
                <PrimaryButton
                  variant="short"
                  className="!text-xl mt-6"
                  onClick={onClickStart}
                >
                  Vào game ngay
                </PrimaryButton>
              </motion.div>
            </div>
          </RequestPermissionWrapper>
        )}
      </AnimatePresence>
    );
  }
);

RequestPermission.displayName = "RequestPermission";
