// Libraries
import React, { memo, useMemo } from "react";
import styled from "styled-components";
import { motion } from "motion/react";

// Constants
import { INGREDIENTS } from "pages/games/mix-and-match/constants";

// Components
import { IngredientItem } from "./IngredientItem";
import { useAppConfig } from "hooks";
import { useRecoilValue } from "recoil";
import { gameState } from "../state";

interface IngredientsProps {}

const IngredientsWrapper = styled(motion.div)`
  height: fit-content;

  .ingredients {
    &__item {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
  }
`;

export const Ingredients: React.FC<IngredientsProps> = memo(() => {
  const { appSettings } = useAppConfig();
  const { currentMission } = useRecoilValue(gameState);

  const { ingredients = INGREDIENTS } = appSettings?.games?.mixAndMatch || {};

  const selectionIngredients = useMemo(() => {
    if (currentMission && ingredients?.length) {
      return ingredients.filter((ingredient) =>
        currentMission.ingredients.includes(ingredient.key)
      );
    }

    return [];
  }, [currentMission, ingredients]);

  return (
    <IngredientsWrapper
      id="ingredients"
      className="w-full h-full grid grid-cols-3 gap-x-6 gap-y-2 p-6"
    >
      {selectionIngredients.map((ingredient, index) => (
        <IngredientItem
          key={ingredient.key}
          index={index}
          ingredient={ingredient}
        />
      ))}
    </IngredientsWrapper>
  );
});

Ingredients.displayName = "Ingredients";
