// Libraries
import React, { memo, useCallback, useEffect, useMemo } from "react";
import styled from "styled-components";
import {
  AnimatePresence,
  motion,
  SVGMotionProps,
  Variants,
} from "motion/react";
import { useDroppable } from "@dnd-kit/core";
import { useRecoilState, useRecoilValue } from "recoil";
import Typed from "typed.js";

// Assets
import MissionSticker from "assets/images/mix-and-match/mission-sticker.webp";

// State
import { gameState, isCupComplete } from "../state";
import { mixAndMatchState } from "pages/games/mix-and-match/state";
import { MISSIONS } from "pages/games/mix-and-match/constants";
import { useDelay, useUserInfo } from "hooks";
import { replaceTemplatePlaceholders } from "utils";

const StickerWrapper = styled(motion.div)`
  position: absolute;
  height: fit-content;
  z-index: 20;
  transform-origin: top center;
  /* transform-style: preserve-3d; */

  .mission-content {
    /* absolute inset-0 text-center text-main-primary flex justify-center font-mansalva */
    position: absolute;
    inset: 0;
    text-align: center;
    color: var(--color-main-primary);
    font-family: "Mansalva", sans-serif;
    /* padding: 40px 33px 0px 9px; */
    padding: 50px 52px 0px 15px;
    transform: rotate(-9deg);
    transform-style: preserve-3d;
  }

  @media screen and (max-height: 800px) {
    .mission-content {
      padding: 40px 33px 0px 9px;
    }
  }
`;

const EmptyCupSvg = styled(motion.svg)<{
  $isOver?: boolean;
}>``;

interface EmptyCupProps extends React.ComponentProps<typeof EmptyCupSvg> {}

const missionStickerVariants: Variants = {
  visible: {
    opacity: 1,
    rotateX: 0,
    transition: {
      duration: 0.5,
      delay: 0.3,
    },
  },
  swing: {
    rotate: [5, -5],
    rotateX: 0,
    opacity: 1,
    transition: {
      duration: 3,
      ease: "easeInOut",
      repeat: Infinity,
      repeatType: "reverse",
    },
  },
};

export const EmptyCup: React.FC<EmptyCupProps> = memo((props) => {
  const { isOver, setNodeRef } = useDroppable({
    id: "empty-cup",
  });
  const { userInfo } = useUserInfo();
  const [
    { selectedIngredients, isReadyToShake, currentMission, isShaking },
    setGame,
  ] = useRecoilState(gameState);
  const [mixAndMatch, setMixAndMatch] = useRecoilState(mixAndMatchState);
  const isShowMissionSticker = useDelay(1200);
  const isSwingMissionSticker = useDelay(1600);

  // Ref
  const missionContentRef = React.useRef<HTMLDivElement>(null);

  const variants: Variants = {
    visible: {
      opacity: 1,
      bottom: "-10%",
      transition: {
        duration: 0.5,
        delay: 0.8,
      },
    },
    hover: {
      opacity: 1,
      bottom: "-10%",
      scale: 1.1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 10,
      },
    },
    readyShaking: {
      opacity: 1,
      position: "absolute",
      bottom: "50%",
      y: "60%",
      transition: {
        duration: 0.5,
      },
    },
  };

  const animate = useMemo(() => {
    if (isReadyToShake) {
      return "readyShaking";
    }

    if (isOver) {
      return "hover";
    }

    return "visible";
  }, [isOver, isReadyToShake]);

  // Effects
  useEffect(() => {
    if (!missionContentRef.current || !currentMission) return;

    const typed = new Typed(missionContentRef.current, {
      strings: [
        replaceTemplatePlaceholders(currentMission?.description || "", {
          userName: userInfo?.name || "Bạn",
        }),
      ],
      typeSpeed: 10,
      showCursor: false,
      startDelay: 800,
    });

    return () => {
      typed.destroy();
    };
  }, [missionContentRef, currentMission, userInfo?.name, isShowMissionSticker]);

  // const onClickCup = useCallback(() => {
  //   if (!isReadyToShake) return;

  //   setGame((previous) => ({
  //     ...previous,
  //     isShaking: true,
  //   }));

  //   setTimeout(() => {
  //     setMixAndMatch((previous) => ({
  //       ...previous,
  //       currentScreen: "result",
  //     }));
  //   }, 2000);
  // }, [isReadyToShake, setGame, setMixAndMatch]);

  return (
    <motion.div
      initial={{
        opacity: 0,
        bottom: "-20%",
      }}
      animate={animate}
      variants={variants}
      className="absolute w-[80%] h-xs:w-[60%]"
    >
      <EmptyCupSvg
        viewBox="0 0 281 509"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        width={"100%"}
        height={"auto"}
        // onClick={onClickCup}
      >
        <mask
          id="mask0_73_50"
          style={{
            maskType: "luminance",
          }}
          maskUnits="userSpaceOnUse"
          x="25"
          y="138"
          width="232"
          height="364"
        >
          <path
            d="M256.23 139.68C255.33 145.43 254.2 151.16 252.8 156.85C251.6 161.77 250.25 166.69 248.63 171.52C247.32 175.42 245.5 179.67 241.65 182.1C241.41 182.25 241.29 182.38 241.26 182.5C238.76 184.69 235.37 185.83 232 186.46C227.77 187.26 223.4 187.58 219.1 187.85C209.65 188.44 200.19 188.18 190.8 187.15C189.32 186.99 188.77 187.89 189.52 189.01C190.38 190.29 192.36 191.36 194 191.54C204.05 192.65 214.22 192.89 224.33 192.23C229.45 191.89 235.32 191.63 240.42 190C238.44 225.09 236.44 260.49 234.43 296.19C232.3 333.91 230.15 371.99 227.98 410.42C227.35 421.53 224.98 463.56 224.35 474.73C217.05 481.67 203.62 492.53 184.04 498.32C171.61 502 162.44 501.94 145.45 501.67C113.8 501.18 77.5801 500.62 59.6501 477.11C57.8801 474.79 56.6501 472.72 55.8801 471.32C55.5901 457.79 55.0501 437.29 54.2301 412.27C53.5101 390.36 52.6601 368.51 51.8201 353.59C49.6701 315.45 46.7001 277.73 42.9301 240.44C41.2701 224.1 39.4601 207.85 37.4901 191.68C43.0901 194.69 49.2901 196.61 55.5301 198.17C64.2601 200.35 73.1201 202.16 82.0201 203.72C100.06 206.87 118.37 208.81 136.75 209.51C147.29 209.91 157.84 209.9 168.39 209.5C170.22 209.43 170.01 206.78 168.34 206.85C131.85 208.24 95.2701 204.83 60.0101 196.59C52.0701 194.73 44.0601 192.61 37.1001 188.52C36.8601 186.52 36.6101 184.52 36.3601 182.52C61.4401 188.71 87.4801 191.97 113.65 192.19C114.91 192.2 117.04 191.88 116.8 190.32C116.58 188.87 113.98 188.1 112.66 188.09C86.5901 187.86 60.7601 184.56 35.8201 178.28C35.8101 178.18 35.6801 178.16 35.4801 178.19C34.9801 178.06 34.4701 177.94 33.9701 177.81C33.7301 177.75 33.4501 177.71 33.1401 177.68C28.1901 172.9 27.6601 165.19 27.2201 158.94C26.7401 152.09 26.3101 145.25 25.8801 138.42C30.9001 142.97 38.1001 144.42 44.6801 146.02C52.7301 147.98 60.8901 149.56 69.1101 150.83C85.8901 153.43 102.88 154.77 119.9 155.56C152.47 157.07 185.77 157.94 217.99 151.84C226.78 150.18 235.46 147.97 243.91 145.11C247.92 143.75 252.63 142.26 256.22 139.69L256.23 139.68Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_73_50)">
          <motion.path
            initial={{
              d: "M93.1285 375.813C145.765 381.071 161.753 390.116 192.489 389.108C216.651 388.318 231.419 381.924 240.003 376.809C245.566 377.682 255.631 379.53 255.991 381.963C256.431 384.907 240.623 386.714 235.341 388.182C208.147 395.739 172.639 402.561 128.156 404.986C73.6486 407.961 12.3475 404.171 -27.4626 396.464C-31.9949 395.585 -85.0718 385.115 -70.4644 378.372C-55.2167 371.329 31.117 369.618 93.1185 375.812L93.1285 375.813Z",
              color: "var(--color-background-game)",
            }}
            {...(selectedIngredients?.[2]?.color && {
              animate: {
                d: "M93.1285 231.984C145.765 262.291 161.753 314.432 192.489 308.624C216.651 304.071 231.419 267.211 240.003 237.725C245.566 242.756 255.631 253.407 255.991 267.433C256.431 284.402 240.623 294.82 235.341 303.282C208.147 346.85 172.639 386.175 128.156 400.157C73.6486 417.304 12.3475 395.459 -27.4626 351.025C-31.9949 345.961 -85.0718 285.602 -70.4644 246.732C-55.2167 206.13 31.117 196.268 93.1185 231.973L93.1285 231.984Z",
                color: selectedIngredients[2].color,
              },
            })}
            transition={{
              duration: 0.5,
            }}
            fill="currentColor"
          />
          <motion.path
            initial={{
              d: "M175.092 425.952C118.097 430.701 99.0648 439.778 66.7187 438.371C41.2995 437.265 27.1056 430.531 19.189 425.182C13.0916 426.01 2.00636 427.781 1.05677 430.27C-0.0827434 433.283 16.2503 435.327 21.508 436.898C48.5864 444.977 84.661 452.404 131.241 455.434C188.327 459.148 254.168 456.013 298.13 448.596C303.137 447.752 361.792 437.664 347.868 430.573C333.325 423.167 242.224 420.358 175.082 425.952H175.092Z",
              color: "var(--color-background-game)",
            }}
            {...(selectedIngredients?.[1]?.color && {
              animate: {
                d: "M175.092 267.061C118.097 296.112 99.0648 351.64 66.7187 343.036C41.2995 336.269 27.1056 295.073 19.189 262.348C13.0916 267.412 2.00636 278.251 1.05677 293.478C-0.0827434 311.906 16.2503 324.414 21.508 334.021C48.5864 383.446 84.661 428.884 131.241 447.421C188.327 470.14 254.168 450.962 298.13 405.585C303.137 400.425 361.792 338.71 347.868 295.327C333.324 250.022 242.224 232.838 175.082 267.061H175.092Z",
                color: selectedIngredients[1].color,
              },
            })}
            transition={{
              duration: 0.5,
            }}
            fill="currentColor"
          />
          <motion.path
            initial={{
              d: "M24.1833 481.823C87.6159 482.831 112.883 526.298 167.333 529.192C202.535 531.065 254.194 524.49 328.5 489.5C342.395 494.199 411.951 502.192 385.962 524.618C380.45 529.372 369.036 531.753 350.159 538.588C303.563 555.452 245.362 570.089 177.798 573.601C126.66 576.259 69.7695 565.522 -43.7308 544.66C-136.334 527.641 -149.668 520.27 -147.848 514.798C-146.137 509.65 -128.571 506.004 -93.5184 498.923C-35.4978 487.2 -6.48755 481.337 24.1833 481.823Z",
              color: "var(--color-background-game)",
            }}
            {...(selectedIngredients?.[0]?.color && {
              animate: {
                d: "M24.12 301.1C87.53 304.09 100.02 372.17 154.45 380.75C189.64 386.3 241.28 366.81 315.56 263.09C329.45 277.02 411.75 361.48 385.77 427.96C380.26 442.05 368.85 449.11 349.98 469.37C303.4 519.36 245.22 562.75 177.68 573.16C126.56 581.04 69.69 549.21 -43.77 487.37C-136.34 436.92 -149.67 415.07 -147.85 398.85C-146.14 383.59 -128.58 372.78 -93.54 351.79C-35.54 317.04 -6.53999 299.66 24.12 301.1Z",
                color: selectedIngredients[0].color,
              },
            })}
            transition={{
              duration: 0.5,
            }}
            fill="currentColor"
          />
        </g>
        <path
          opacity="0.3"
          d="M256.23 139.68C255.33 145.43 254.2 151.16 252.8 156.85C251.6 161.77 250.25 166.69 248.63 171.52C247.32 175.42 245.5 179.67 241.65 182.1C241.41 182.25 241.29 182.38 241.26 182.5C238.76 184.69 235.37 185.83 232 186.46C227.77 187.26 223.4 187.58 219.1 187.85C209.65 188.44 200.19 188.18 190.8 187.15C189.32 186.99 188.77 187.89 189.52 189.01C190.38 190.29 192.36 191.36 194 191.54C204.05 192.65 214.22 192.89 224.33 192.23C229.45 191.89 235.32 191.63 240.42 190C238.44 225.09 236.44 260.49 234.43 296.19C232.3 333.91 230.15 371.99 227.98 410.42C227.35 421.53 224.98 463.56 224.35 474.73C219.42 480.17 210.99 488.13 198.55 493.99C181.88 501.84 166.96 501.69 148.66 501.5C130.26 501.31 116.92 498.43 105.3 495.86C76.3601 489.45 66.4301 481.78 61.4701 477.21C58.9401 474.88 57.0401 472.73 55.7701 471.21C55.4901 457.69 54.9701 437.22 54.1801 412.22C53.4901 390.34 52.6601 368.52 51.8201 353.61C49.6701 315.47 46.7001 277.75 42.9301 240.46C41.2701 224.12 39.4601 207.87 37.4901 191.7C43.0901 194.71 49.2901 196.63 55.5301 198.19C64.2601 200.37 73.1201 202.18 82.0201 203.74C100.06 206.89 118.37 208.83 136.75 209.53C147.29 209.93 157.84 209.92 168.39 209.52C170.22 209.45 170.01 206.8 168.34 206.87C131.85 208.26 95.2701 204.85 60.0101 196.61C52.0701 194.75 44.0601 192.63 37.1001 188.54C36.8601 186.54 36.6101 184.54 36.3601 182.54C61.4401 188.73 87.4801 191.99 113.65 192.21C114.91 192.22 117.04 191.9 116.8 190.34C116.58 188.89 113.98 188.12 112.66 188.11C86.5901 187.88 60.7601 184.58 35.8201 178.3C35.8101 178.2 35.6801 178.18 35.4801 178.21C34.9801 178.08 34.4701 177.96 33.9701 177.83C33.7301 177.77 33.4501 177.73 33.1401 177.7C28.1901 172.92 27.6601 165.21 27.2201 158.96C26.7401 152.11 26.3101 145.27 25.8801 138.44C30.9001 142.99 38.1001 144.44 44.6801 146.04C52.7301 148 60.8901 149.58 69.1101 150.85C85.8901 153.45 102.88 154.79 119.9 155.58C152.47 157.09 185.77 157.96 217.99 151.86C226.78 150.2 235.46 147.99 243.91 145.13C247.92 143.77 252.63 142.28 256.22 139.71L256.23 139.68Z"
          fill="white"
        />
        <path
          ref={setNodeRef as any}
          d="M263.98 125.01C263.51 123.66 262.49 122.4 261.26 121.33C258.46 118.9 254.6 117.22 251.19 115.59C247.52 113.84 243.73 112.28 239.88 110.87C232.15 108.04 224.14 105.87 216.05 104.07C200.07 100.52 183.73 98.09 167.38 96.53C151.14 94.98 134.75 94.44 118.41 95.27C102.18 96.1 85.9799 98.23 70.2099 102.09C62.3699 104.01 54.6499 106.3 47.0999 109.07C39.6499 111.8 31.9599 114.75 25.2399 118.85C21.9799 120.84 19.0199 123.32 16.9199 126.34C16.7299 126.61 16.6199 126.87 16.5699 127.11C15.8499 128.06 15.6999 129.38 15.9799 130.44C16.4699 132.27 17.7499 133.81 19.5699 134.61C20.0399 142.55 20.4599 150.51 21.1299 158.46C21.8299 166.69 23.6999 174.68 30.1099 180.78C29.6499 181.13 29.3099 181.5 29.3499 181.84C33.9999 218.68 37.8599 255.97 40.8899 293.68C43.9399 331.54 46.1599 369.83 47.5499 408.53C48.2999 429.33 48.7899 450.25 49.0599 471.28C48.4799 472.23 48.5799 473.39 49.0999 474.43C49.0999 474.56 49.0999 474.7 49.0999 474.83C49.0999 474.91 49.1999 474.92 49.3699 474.9C49.4599 475.04 49.5499 475.17 49.6599 475.3C57.5199 485.16 69.9799 489.85 81.4199 494.16C94.2599 498.99 107.47 503.01 120.95 505.6C145.94 510.41 172.29 510.03 196.34 501.09C209.15 496.33 220.93 489.27 230.86 479.86C231.59 479.17 231.7 478.26 231.47 477.37C231.79 477.17 231.98 476.92 232 476.6C234.18 437.33 236.34 398.43 238.48 359.9C240.59 321.94 242.67 284.32 244.74 247.06C245.88 226.45 247.02 205.94 248.15 185.53C248.26 185.34 248.26 185.11 248.17 184.86C248.14 184.67 248.04 184.5 247.89 184.35C247.61 183.96 247.17 183.54 246.65 183.17C247.22 183.07 247.74 182.9 248.14 182.65C252.07 180.17 254.13 176.47 255.61 172.45C257.39 167.59 258.71 162.57 259.95 157.6C262.59 147.05 264.35 136.37 265.29 125.64C265.31 125.36 264.77 125.16 263.98 125.04V125.01ZM252.81 156.85C251.61 161.77 250.26 166.69 248.64 171.52C247.33 175.42 245.51 179.67 241.66 182.1C241.43 182.25 241.3 182.38 241.27 182.49C238.77 184.68 235.38 185.82 232.01 186.45C227.78 187.24 223.41 187.57 219.11 187.84C209.66 188.43 200.2 188.17 190.81 187.14C189.33 186.98 188.78 187.88 189.53 189C190.39 190.28 192.37 191.35 194.01 191.53C204.06 192.64 214.23 192.88 224.34 192.22C229.46 191.88 235.33 191.62 240.44 189.99C238.46 225.08 236.46 260.47 234.45 296.18C232.32 333.9 230.17 371.98 228 410.41C226.8 431.74 225.59 453.17 224.37 474.72C224.22 474.83 224.08 474.94 223.94 475.07C207.7 490.46 185.71 498.72 163.66 500.9C138.48 503.38 113.19 498.09 89.5299 489.71C82.9999 487.4 76.4299 484.98 70.1699 481.99C64.7199 479.39 59.6799 476.07 55.8799 471.31C55.8399 471.27 55.7999 471.23 55.7699 471.18C55.7899 471.11 55.7999 471.04 55.7999 470.97C55.3299 431.43 53.9999 392.29 51.8199 353.58C49.6699 315.44 46.6999 277.72 42.9199 240.43C41.2599 224.09 39.4499 207.84 37.4799 191.67C43.0799 194.68 49.2799 196.6 55.5199 198.16C64.2499 200.34 73.1099 202.15 82.0099 203.7C100.05 206.85 118.36 208.79 136.74 209.49C147.28 209.89 157.83 209.88 168.38 209.49C170.21 209.42 170 206.77 168.33 206.84C131.85 208.23 95.2599 204.81 59.9999 196.57C52.0599 194.71 44.0499 192.59 37.0899 188.5C36.8399 186.5 36.5999 184.5 36.3499 182.5C61.4299 188.69 87.4699 191.95 113.64 192.17C114.9 192.18 117.03 191.86 116.79 190.3C116.57 188.85 113.97 188.08 112.65 188.07C86.5799 187.84 60.7499 184.54 35.8099 178.26C35.8099 178.26 35.8099 178.26 35.8099 178.25C35.7999 178.16 35.6699 178.14 35.4699 178.17C34.9699 178.04 34.4599 177.92 33.9599 177.79C33.7199 177.73 33.4399 177.68 33.1299 177.66C28.1799 172.88 27.6499 165.17 27.2099 158.92C26.7299 152.07 26.2999 145.23 25.8799 138.39C30.8999 142.94 38.0999 144.39 44.6799 145.99C52.7299 147.95 60.8899 149.53 69.1199 150.8C85.8899 153.4 102.89 154.74 119.91 155.53C152.48 157.04 185.78 157.91 218 151.81C226.79 150.15 235.47 147.94 243.92 145.08C247.94 143.72 252.64 142.23 256.23 139.66C255.34 145.41 254.2 151.14 252.8 156.83L252.81 156.85ZM256.84 135.47C253.68 138.87 248.64 140.69 244.3 142.22C236.71 144.9 228.91 147.04 221 148.71C205.59 151.97 189.83 153.48 174.09 154.05C158.15 154.62 142.21 154.27 126.29 153.67C109.77 153.05 93.2499 152.02 76.8999 149.93C67.6599 148.75 58.4699 147.22 49.4099 145.24C44.9899 144.27 40.5799 143.22 36.2399 141.99C32.4899 140.93 28.6299 139.61 25.8099 136.97C25.8099 136.84 25.7899 136.72 25.7899 136.59C38.3099 141.36 51.9199 143.61 65.4899 145.21C83.8599 147.37 102.37 148.75 120.93 149.35C139.57 149.95 158.25 149.77 176.89 148.8C194.06 147.9 211.38 146.64 228.15 143.02C236.48 141.22 244.64 138.85 252.45 135.67C253.89 135.08 255.5 134.49 257.07 133.8C257 134.36 256.92 134.91 256.85 135.47H256.84ZM256.81 129.7C250.16 133.23 242.4 135.54 235 137.42C219.56 141.35 203.45 142.83 187.5 143.84C169.88 144.96 152.21 145.37 134.57 145.07C116.82 144.77 99.0999 143.76 81.4799 142.03C65.5299 140.47 49.2699 138.67 34.2699 133.57C31.7199 132.7 29.2199 131.72 26.7899 130.64C26.8499 130.53 26.9099 130.42 26.9299 130.29C28.0199 124.38 34.6399 121.57 39.9199 119.8C47.6399 117.21 55.5399 115.18 63.4999 113.51C79.1299 110.23 94.9999 108.38 110.85 106.77C118.93 105.95 127.03 105.04 135.13 104.59C143.56 104.13 151.89 104.6 160.18 106.01C168.11 107.36 175.92 109.27 183.83 110.74C191.72 112.21 199.71 113.04 207.7 113.92C224.1 115.74 241.61 118.34 254.55 129.04C255.65 129.95 258.09 128.62 256.8 127.55C245.25 118 230.13 114.47 215.37 112.54C206.91 111.44 198.39 110.78 189.96 109.47C181.52 108.16 173.24 106.11 164.86 104.52C156.79 102.99 148.67 102.01 140.41 102.16C131.91 102.31 123.44 103.27 114.98 104.1C98.3399 105.74 81.6699 107.54 65.2299 110.86C56.1999 112.68 47.1899 114.91 38.4599 117.9C32.6999 119.87 25.9399 123.15 24.2999 129.26C23.7999 128.75 23.1799 128.34 22.5299 128.06C22.3599 127.77 22.1499 127.5 21.9199 127.27C22.3099 126.98 22.6399 126.65 22.8799 126.3C26.5799 120.98 33.6699 118.37 39.7099 115.83C46.6199 112.92 53.7699 110.47 61.0099 108.39C75.5599 104.2 90.6699 101.6 105.79 100.33C136.83 97.72 168.14 100.29 198.54 105.81C214.04 108.63 229.63 112.22 243.81 118.72C247.24 120.29 250.78 121.96 253.89 124.02C255.25 124.92 259.29 128.33 256.79 129.66L256.81 129.7Z"
          fill="#DC5634"
        />
        <path
          d="M134.93 295.82C141.72 298.57 149.69 299.23 156.93 298.52C159.98 298.22 166.57 298.07 168.69 295.39C169.14 294.82 168.78 293.9 168.56 293.36C167.5 290.8 163.57 289.13 161.36 287.78C158.09 285.77 154.9 283.99 151.07 283.35C146.85 282.64 142.4 283.63 138.2 284.14L142.92 286.86L142.79 286.56L142.49 289.07C144.05 287.38 148.4 284.26 148.78 281.86C149.34 278.36 142.96 276.61 140.7 275.57C131.78 271.47 120.11 277.11 112.07 281.7C111.42 282.07 111.43 282.87 111.49 283.49L111.52 283.8C111.65 285.2 113.55 286.51 114.74 286.93C121.35 289.21 127.7 292.39 134.01 295.39C138.19 297.38 140.71 293.3 136.4 291.25C129.48 287.96 122.52 284.42 115.27 281.92L118.49 285.05L118.46 284.74L117.88 286.53C122.43 283.93 127.49 282.13 132.52 280.73C135.46 279.91 137.1 279.48 139.77 280.42C141.53 281.04 143.24 282.01 144.94 282.79L141.72 279.66L141.77 279.95L142.35 278.16C140.47 280.2 138.58 282.23 136.7 284.27C135.95 285.08 135.95 285.77 136.4 286.78L136.53 287.08C137.21 288.6 139.6 290 141.25 289.8C143.67 289.51 146.09 289.16 148.52 288.92C150.72 288.7 152.55 288.81 154.63 289.58C155.56 289.92 161.85 292.82 162.16 293.58L162.03 291.55C162.6 290.83 160.35 291.78 159.54 291.97C157.46 292.45 155.35 292.78 153.22 292.96C147.34 293.45 140.98 293.06 135.46 290.83C130.71 288.91 131.24 294.34 134.93 295.84V295.82Z"
          fill="#DC5634"
        />
        <path
          d="M85.4697 301.58C90.1097 300.14 95.4897 300.19 100.27 300.26C104.72 300.33 109.17 300.71 113.58 301.34C116.5 301.76 119.4 302.29 122.28 302.94C124 303.32 127.04 304.93 128.7 304.22C129.64 303.82 129.66 303.1 129.53 302.18C128.89 297.69 121.27 293.84 117.94 291.6C115.05 289.67 111.95 286.98 108.29 287.1C105.23 287.2 102.16 288.54 99.2497 289.38L103.88 292.85L103.77 292.63L103.71 295.22C105.16 294 108.36 292.24 109.15 290.52C110.89 286.72 104.93 284.27 102.47 283.25C97.2497 281.07 96.3197 280.4 91.6797 283.53C87.0397 286.66 83.1797 290.68 80.3897 295.73C79.9297 296.57 80.6197 298.03 81.0997 298.69L81.2797 298.94C82.0197 299.96 83.9297 302.23 85.4597 301.58C86.9897 300.93 86.2997 298.94 85.5597 297.92L85.3797 297.67L86.0897 300.63C88.3997 296.44 91.4997 293.06 95.3797 290.29C96.4797 289.51 98.3497 287.71 99.7397 287.6C101.6 287.45 105.43 289.89 107.14 290.65L103.56 286.55L103.59 286.75L103.92 284.96C102.16 286.45 100.4 287.94 98.6397 289.43C97.9197 290.04 98.2297 291.31 98.5797 292.02L98.6897 292.24C99.3697 293.61 101.4 296.26 103.32 295.71C108.2 294.3 111.74 292.82 116.49 295.94C118.14 297.02 119.81 298.11 121.4 299.29C122.16 299.85 122.84 300.51 123.6 301.06C125.68 302.59 124.22 303.03 123.79 299.99L124.62 297.95C123.4 298.47 118.91 296.49 117.63 296.23C114.81 295.65 111.96 295.17 109.1 294.8C104.61 294.22 100.08 293.92 95.5497 293.93C91.0197 293.94 85.8297 293.93 81.3797 295.31C78.4697 296.21 82.8997 302.41 85.4597 301.61L85.4697 301.58Z"
          fill="#DC5634"
        />
        <path
          d="M138.77 257.09C111.24 257.29 76.8698 267.23 64.0198 294.18C52.3098 318.76 69.9398 343.06 91.1798 354.83C118.73 370.1 154.67 371.6 183.64 359.39C206.5 349.76 227.91 327.46 218.69 300.91C207.78 269.52 168.71 257.3 138.77 257.08C135.13 257.05 139.65 263.08 142.36 263.1C167.01 263.28 199.37 271.53 211.02 295.94C221.89 318.69 204.78 340.42 185.11 350.68C159.83 363.86 127.37 364.98 100.99 354.38C79.9698 345.93 60.0098 326.15 68.8798 301.78C79.1698 273.49 115.43 263.31 142.36 263.12C146.02 263.09 141.48 257.09 138.77 257.1V257.09Z"
          fill="#DC5634"
        />
        <path
          d="M98.9398 282.07C99.8698 281.73 97.9598 281.95 98.9498 281.91C99.2298 281.9 99.6598 282.04 99.8998 282.07C101.74 282.34 103.59 282.97 105.34 283.53C109.74 284.94 111.28 285.15 115.46 282.95C121.56 279.75 127.99 277.25 134.57 275.22C138.69 273.95 144.3 271.41 148.45 272.87C151.03 273.77 153.65 275.77 155.97 277.18C164.22 282.21 171.73 288.15 179.66 293.64C181.75 295.09 183.97 296.18 186.41 296.98C189.29 297.92 197.06 299.83 199.64 297.21C200.52 296.32 198.89 294.1 200.63 297.17C201.35 298.44 202.14 299.82 202.62 301.22C203.78 304.62 203.4 307.39 200.59 309.71C196.84 312.81 191.41 313.39 186.75 313.64C167.45 314.67 148.7 308.51 130.3 303.61C111.9 298.71 86.3798 292.77 70.0598 306.36L76.1598 310.88C78.1998 297.78 87.8698 288.41 98.9498 282.07C101.83 280.42 96.5198 274.94 94.0898 276.33C82.3598 283.04 71.9498 293 69.7898 306.89C69.3998 309.38 73.5498 313.36 75.8898 311.41C91.8598 298.12 118.21 305.43 136.07 310.51C153.93 315.59 171.97 320.65 190.36 319.66C197.53 319.27 209.03 317.35 209.87 308.33C210.42 302.44 204.25 288.67 196.82 290.58C191.63 291.92 187.57 293.04 182.45 290.19C178.43 287.95 174.79 284.49 171.03 281.83C164.81 277.42 158.42 273.2 151.75 269.49C149.56 268.27 147.04 266.54 144.52 266.19C139.5 265.48 133 268.44 128.26 269.96C123.77 271.4 119.35 273.04 115.07 275.02C112.08 276.4 109.24 278.29 106.06 279.15L108.25 279.33C104.45 277.98 98.2998 274.8 94.1098 276.35C91.1798 277.44 96.2698 283.09 98.9698 282.09L98.9398 282.07Z"
          fill="#DC5634"
        />
        <path
          d="M140.57 260.1C98.06 260.1 63.47 283.49 63.47 312.24C63.47 340.99 98.06 364.38 140.57 364.38C183.08 364.38 217.67 340.99 217.67 312.24C217.67 283.49 183.08 260.1 140.57 260.1Z"
          fill="#DC5634"
        />
        <motion.g
          initial={{ opacity: 0, y: "-10%" }}
          transition={{
            duration: 1,
            ease: "anticipate",
            delay: 0.2,
          }}
          {...(isReadyToShake && {
            animate: {
              opacity: 1,
              y: "0%",
            },
          })}
        >
          <path
            d="M261.81 139.47C270.36 137.66 272.83 126.42 272.09 119.86C270.15 102.66 265.83 89.98 262.23 79.42C258.14 67.43 255.37 61.7 253.68 58.7C239.92 34.26 217.08 22.86 206.13 17.57C195.45 12.41 186.83 10.09 179.84 8.18003C164.82 4.07003 140.3 -2.64996 110.46 3.02004C100.48 4.92004 69.14 11.32 43.89 38.5C41.96 40.57 26.23 58.07 18.36 82.7C17.06 86.78 16.24 90.19 14.84 96.11C11.48 110.24 13.31 107.17 11.59 130.21C11.52 131.19 11.31 133.89 12.85 135.08C13.45 135.54 14 135.54 16.01 136.16C17.28 136.56 18.31 136.95 19 137.23C19.75 136.59 20.06 136.01 19.85 135.8C17.68 133.69 13.57 133.38 13.59 132.77C13.4 131.28 13.43 129.86 13.78 128.59C12.9 100.57 20.5 82.5 26.22 72.39C29.51 66.58 40.39 44.28 62.36 27.8C121.56 -16.58 213.27 13.83 237.4 43.54C245.66 53.71 252.23 66.18 252.23 66.18C253.74 69.05 260.66 82.4 265.4 102.2C266.7 105.72 268.29 111.39 268.07 118.46C267.76 128.52 263.96 135.94 261.83 139.48L261.81 139.47Z"
            fill="#DC5634"
          />
          <path
            d="M162.52 17.28C159.08 14.64 154.5 13.83 150.57 13.79C150.02 13.79 149.2 13.83 149.69 14.61C150.16 15.35 151.92 15.42 152.58 15.42C154.01 15.43 160.56 16.31 159.75 19.45C159.39 20.83 157.58 21.14 156.67 21.48C155.23 22.03 153.77 22.52 152.31 22.97C146.53 24.73 140.59 25.65 134.64 25.72C128.79 25.78 122.95 25 117.25 23.34C116.09 23 114.66 22.8 113.66 21.91C112.59 20.95 112.41 19.29 113.4 18.12C114.26 17.11 115.54 16.83 116.63 16.48C118.14 16 119.66 15.57 121.19 15.21C128.02 13.56 135.01 13.13 141.93 13.86C142.34 13.9 143.61 14.09 143.52 13.28C143.44 12.57 141.28 12.32 140.96 12.29C133.15 11.47 125.3 11.94 117.6 13.8C115.7 14.26 113.78 14.76 111.92 15.44C110.83 15.84 109.54 16.37 108.86 17.65C106.91 21.32 111.97 23.21 113.78 23.81C128.41 28.61 144.08 28.63 158.66 23.51C160.14 22.99 162.24 22.66 163.45 21.34C164.78 19.89 163.68 18.18 162.54 17.3L162.52 17.28Z"
            fill="#DC5634"
          />
          <path
            d="M99.5201 20.49C79.6401 28.63 63.1001 44.16 53.3701 63.28C50.6301 68.66 48.5001 74.31 46.9201 80.14C46.2601 82.55 50.2801 82.56 50.8701 80.36C56.2201 60.71 68.9401 43.36 85.7501 31.94C90.5301 28.69 95.6501 25.98 101 23.79C103.4 22.81 101.9 19.51 99.5101 20.49H99.5201Z"
            fill="#DC5634"
          />
        </motion.g>
        <motion.path
          initial={{ opacity: 0, y: "10%" }}
          {...(isReadyToShake && {
            animate: { opacity: 1, y: "0%" },
          })}
          d="M211.99 379.6C211.41 378.68 210.76 377.78 210.09 376.92C209.81 376.56 207.89 374.36 207.87 374.86C206.82 402.35 205.77 429.84 204.72 457.33C204.7 457.76 205.47 458.8 205.65 459.07C206.23 459.99 206.88 460.89 207.55 461.75C207.83 462.11 209.75 464.31 209.77 463.81L212.92 381.34C212.94 380.91 212.17 379.87 211.99 379.6Z"
          fill="#DC5634"
        />
        <motion.g
          initial={{ opacity: 0 }}
          {...(isReadyToShake && {
            animate: { opacity: 1 },
            transition: { delay: 1 },
          })}
        >
          <motion.path
            initial={{ opacity: 0, y: "-5px" }}
            animate={{
              opacity: 1,
              y: "0px",
            }}
            transition={{
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            d="M60.02 504.73C54.69 503.46 48.9 501.6 44.38 498.41C42.01 496.74 41.17 494.3 39.44 492.11C37.96 490.23 35.82 489.06 33.9 487.7C25.13 481.48 27.29 468.89 27.78 459.64C28.15 452.52 28.53 445.41 28.9 438.29C28.97 436.95 23.43 439.11 23.35 440.79C23.01 447.18 22.68 453.58 22.34 459.97C22.04 465.62 21.42 471.37 21.85 477.03C22.2 481.6 23.36 486.36 26.87 489.57C28.76 491.3 31.16 492.39 33.01 494.16C34.89 495.95 35.66 498.53 37.52 500.35C39.66 502.45 42.5 503.76 45.22 504.94C48.38 506.31 51.66 507.38 55.01 508.18C56.25 508.48 57.75 507.59 58.73 506.91C58.98 506.74 61.1 504.98 60.02 504.72V504.73Z"
            fill="#DC5634"
          />
          <motion.path
            initial={{ opacity: 0, y: "-5px" }}
            animate={{
              opacity: 1,
              y: "0px",
            }}
            transition={{
              delay: 0.1,
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            d="M30.38 506.39C29.7 503.08 28.18 499.91 25.89 497.41C23.52 494.82 20.49 493.07 18.5 490.1C14.89 484.71 14.26 477.8 13.66 471.51C13.44 469.19 7.25998 470.28 7.49998 472.78C8.09998 479.12 8.84998 485.72 12.16 491.31C13.91 494.28 16.47 496.17 18.99 498.43C21.72 500.88 23.48 504.08 24.22 507.66C24.71 510.04 30.88 508.78 30.38 506.39Z"
            fill="#DC5634"
          />
          <motion.path
            initial={{ opacity: 0, y: "-5px" }}
            animate={{
              opacity: 1,
              y: "0px",
            }}
            transition={{
              delay: 0.2,
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            d="M13.1701 504.45C9.7701 502.19 7.5901 498.71 6.7501 494.74C6.2101 492.18 0.200103 494.01 0.780103 496.71C1.7801 501.44 4.6401 505.39 8.6501 508.05C10.7101 509.42 15.9701 506.3 13.1801 504.44L13.1701 504.45Z"
            fill="#DC5634"
          />
        </motion.g>
        <motion.g
          initial={{ opacity: 0 }}
          {...(isReadyToShake && {
            animate: { opacity: 1 },
            transition: { delay: 1 },
          })}
        >
          <motion.path
            initial={{ opacity: 0, y: "5px" }}
            animate={{
              opacity: 1,
              y: "-5px",
            }}
            transition={{
              duration: 0.5,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            d="M277.02 70.89C272.51 49.47 259.59 30.61 241.4 18.49C239.66 17.33 233.96 18.8 236.95 20.79C254.14 32.24 266.49 50.69 270.73 70.88C271.22 73.21 277.44 72.88 277.02 70.88V70.89Z"
            fill="#DC5634"
          />
          <motion.path
            initial={{ opacity: 0, y: "5px" }}
            animate={{
              opacity: 1,
              y: "-5px",
            }}
            transition={{
              duration: 0.5,
              delay: 0.1,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            d="M280.09 39.52C278.09 31.21 273.27 23.89 266.54 18.64C265.35 17.72 263.48 17.79 262.63 19.12C261.74 20.52 262.59 22.35 263.76 23.26C269.2 27.5 272.93 33.43 274.55 40.12C275.39 43.6 281.06 43.5 280.1 39.52H280.09Z"
            fill="#DC5634"
          />
        </motion.g>
      </EmptyCupSvg>
      <AnimatePresence>
        {!isReadyToShake && isShowMissionSticker && (
          <StickerWrapper
            initial={{ opacity: 0, rotateX: 90 }}
            animate={isSwingMissionSticker ? "swing" : "visible"}
            exit={{
              opacity: 1,
              rotateX: 90,
              transition: { duration: 0.3 },
            }}
            variants={missionStickerVariants}
            className="w-[95%] bottom-[21%] right-[-21%]"
          >
            <img src={MissionSticker} width={"100%"} />
            <motion.div
              animate={{ opacity: [1, 0.7, 1] }}
              transition={{
                duration: 1,
                delay: 1.5,
                repeat: Infinity,
                ease: "linear",
                repeatType: "reverse",
              }}
              className="mission-content flex flex-col gap-2 h-xs:!pt-[34px]"
            >
              <motion.div
                initial={{ opacity: 0, scale: 2, rotate: "30deg" }}
                animate={{ opacity: 1, scale: 1, rotate: "0deg" }}
                transition={{ duration: 0.5, delay: 0.5, ease: "easeInOut" }}
                className="!leading-none h-xs:text-base text-[22px]"
              >
                Nhiệm vụ <br /> hôm nay
              </motion.div>
              <div
                ref={missionContentRef}
                className="px-2 text-[15px] xs:text-[16px] h-xs:text-[11px]"
                // dangerouslySetInnerHTML={{
                //   __html: replaceTemplatePlaceholders(
                //     currentMission?.description || "",
                //     {
                //       userName: userInfo?.name || "",
                //     }
                //   ),
                // }}
              />
            </motion.div>
          </StickerWrapper>
        )}
      </AnimatePresence>
    </motion.div>
  );
});

EmptyCup.displayName = "EmptyCup";
