// Libraries
import React, { memo, useEffect } from "react";
import styled from "styled-components";
import { motion } from "motion/react";

// Assets
// import PrimaryLogo from "assets/images/logos/primary-logo.png";
import PrimaryLogo from "assets/images/mix-and-match/highlands-logo.webp";
import { usePathMorphing, useRandomLoadingProgress } from "hooks";

interface LoadingCupProps {
  onLoadingComplete?: () => void;
}

interface Cup extends React.SVGProps<SVGSVGElement> {
  isComplete?: boolean;
}

const GameLoadingWrapper = styled(motion.div)`
  position: absolute;
  inset: 0;
  z-index: 1000;
  background-color: var(--color-background-game);

  .game-loading {
    &__logo {
      width: 40%;
      position: absolute;
      top: 10%;
    }

    &__cup {
    }
  }
`;

const Cup: React.FC<Cup> = ({ isComplete, ...props }) => {
  return (
    <svg
      viewBox="0 0 129 281"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M96.8411 195.36C100.681 175.58 100.441 165.68 103.071 141.6C103.791 134.97 104.981 125.05 106.831 112.89C103.911 114.14 100.901 115.16 97.7711 115.84C97.0011 116.01 96.3111 115.97 95.6011 115.71C95.3511 117.72 95.1011 128.41 94.7111 130.39C89.4111 157.24 88.4411 185.12 84.3311 212.27C83.1611 219.99 81.9711 227.67 80.4011 235.32C79.2811 237.41 71.8411 239.68 69.4011 240.29C63.1211 241.88 41.5311 242.1 35.7911 239.45C33.5011 238.39 29.6211 235.93 26.8011 231.36C25.5711 229.37 24.7211 227.19 23.5211 216.16C22.7011 208.65 22.0811 202.95 21.8811 195.39C21.8211 193.19 21.7511 189.79 21.3011 185.02C21.1011 182.91 20.8011 180.64 20.3211 177.58C18.4611 165.95 19.6411 159.69 19.5911 148.68C19.5511 139.67 17.1511 121.8 17.2111 112.88C16.9411 112.79 16.6711 112.69 16.4011 112.6C14.7611 112.05 13.1211 111.5 11.5111 110.88C9.80109 110.22 8.15109 109.39 6.62109 108.38C7.37109 116.02 7.71109 122.22 7.89109 126.33C8.31109 136.05 8.00109 138.89 8.51109 150.12C8.66109 153.45 8.9311 157.45 9.4511 165.44C10.1311 175.8 10.4711 180.98 10.9111 185.86C11.7611 195.46 11.9311 193.63 13.9211 211.03C15.3711 223.69 15.7411 228.64 19.2811 234.92C22.1211 239.95 24.9411 242.18 26.0111 242.96C28.9511 245.12 32.2911 246.38 41.0011 247.43C51.2911 248.67 57.4711 249.42 66.0411 247.98C75.8311 246.33 80.8511 245.49 85.0011 241.35C89.5111 236.85 89.0511 232.4 93.1011 212.26C95.2411 201.62 95.2511 203.47 96.8311 195.33L96.8411 195.36Z"
        fill="#DC5634"
      />
      <motion.path
        // jump lid
        initial={{ y: 0 }}
        {...(isComplete && {
          animate: {
            y: [0, -10, 0],
            transition: {
              duration: 0.5,
              ease: "circInOut",
            },
          },
        })}
        d="M14.7508 114.4C14.8608 114.44 15.8408 114.8 15.4108 114.64C15.7408 114.76 16.0708 114.87 16.3908 114.98C17.1608 115.24 17.9308 115.49 18.6908 115.76C18.8708 115.82 19.0408 115.89 19.2208 115.95C19.9208 116.2 20.6208 116.45 21.4008 116.59C27.2408 117.67 36.5908 118.9 46.7008 119.84C58.1408 121.7 69.6908 122.39 81.2508 121.13C84.7508 120.95 87.8408 120.63 90.2008 120.1C91.4708 119.81 92.7208 119.33 93.9708 118.86C94.8008 118.65 95.6408 118.45 96.4708 118.21C96.4708 118.23 96.4708 118.24 96.4708 118.26C99.8208 117.48 103.061 116.38 106.181 114.94C106.641 114.72 107.111 114.49 107.561 114.26C107.561 114.25 107.561 114.24 107.561 114.23C108.141 113.93 109.001 113.41 109.861 112.59C114.101 108.52 113.421 101.59 112.591 94.24C111.751 86.81 109.891 81.33 108.331 76.76C107.151 73.3 106.311 70.89 104.641 67.81C103.941 66.53 101.341 61.9 96.1308 56.65C89.5508 50.02 87.0708 50.7 84.9108 46.56C83.0208 42.95 83.9808 40.67 84.3208 32.63C84.8608 19.7 85.1608 12.3 80.9608 7.39995C78.1108 4.06995 73.7208 2.90995 64.7908 1.46995C55.7908 0.0199524 50.3508 -0.850048 43.5108 1.35995C39.3308 2.70995 34.1208 4.36995 30.8108 9.11995C26.2908 15.61 26.2308 26.34 26.1408 44.31C26.1408 45.03 26.0708 46.87 25.0008 48.69C24.0808 50.25 22.5808 50.91 21.1708 51.78C18.3108 53.55 16.1208 56.42 14.0008 59.1C9.4808 64.81 5.5108 71.15 2.9608 78.2C-0.0892045 86.66 -0.769203 95.86 0.850797 104.72C1.0308 105.73 2.8708 106.84 3.5308 107.36C4.2708 107.94 5.09079 108.57 5.94079 109.1C6.53079 109.2 7.0908 109.4 7.4808 109.84C9.5408 112.11 11.7408 113.26 14.7808 114.42L14.7508 114.4ZM7.3308 100.07C7.1608 99.67 7.0408 99.29 6.9608 98.91C6.9008 99.15 6.8608 99.41 6.8108 99.71C6.8208 99.26 6.8808 98.83 6.9008 98.39C6.8408 97.8 6.8708 97.23 7.0208 96.72C7.9408 85.77 12.3408 75.51 18.9208 66.62C20.9908 63.82 23.1808 60.87 25.7208 58.56C28.1308 56.37 31.4608 55.71 32.4608 52.07C33.2908 49.05 33.0608 45.57 33.1008 42.46C33.1508 39.06 33.3008 35.66 33.3908 32.26C33.7008 20.27 33.9008 14.23 36.1908 11.52C38.5408 8.74995 41.6508 7.90995 45.5308 6.84995C51.6708 5.17995 56.6708 6.03995 62.0608 7.02995C70.3308 8.54995 75.3008 9.29995 77.3308 13.14C78.1908 14.76 78.2408 17.2 78.3508 22.06C78.4108 24.92 78.2508 25.83 78.0608 30.07C77.7708 36.79 77.6308 40.16 78.1708 43.49C78.8308 47.6 79.1608 50.46 81.4608 52.81C83.2408 54.63 85.2408 56.19 87.2608 57.68C88.1308 58.32 89.0108 58.95 89.9008 59.57C90.0208 59.65 90.1408 59.74 90.2508 59.82C90.5508 60.09 90.8408 60.38 91.1208 60.67C94.1608 63.85 95.8508 66.99 97.5308 70.63C99.1308 74.08 100.981 78.48 102.821 83.75C105.761 91.89 104.961 97.85 104.341 100.66C103.781 103.2 103.061 104.48 102.501 105.33C99.8708 109.33 95.4508 111.44 91.1008 111.01C90.7308 111.07 90.3708 111.11 90.0008 111.16C89.4508 111.36 88.8308 111.45 88.1208 111.42C75.3608 113.15 62.9908 113.03 50.0608 112.66C43.9408 112.49 37.5708 112.49 31.5408 111.21C27.9408 110.8 24.3708 109.99 20.8308 108.82C15.7108 107.13 9.6408 105.42 7.3308 100.07Z"
        fill="#DC5634"
      />
      <motion.path
        initial={{ opacity: 0, y: -5, x: -5 }}
        animate={{ opacity: 1, y: 0, x: 0 }}
        transition={{
          duration: 0.5,
          delay: 0.5,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        d="M117.941 223.89C117.541 229.99 117.151 236.26 114.561 241.9C112.271 246.89 108.401 250.92 103.841 253.91C93.2308 260.87 80.1508 261.33 67.8908 260.46C66.1208 260.33 68.6808 265.66 70.6808 265.8C83.4508 266.71 97.1308 266.06 108.041 258.56C112.491 255.5 116.331 251.33 118.581 246.39C121.281 240.49 121.731 233.87 122.151 227.49C122.241 226.12 121.171 224.55 120.151 223.71C119.491 223.17 118.041 222.46 117.951 223.88L117.941 223.89Z"
        fill="#DC5634"
      />
      <motion.path
        initial={{ opacity: 0, y: -5, x: -5 }}
        animate={{ opacity: 1, y: 0, x: 0 }}
        transition={{
          duration: 0.5,
          delay: 0.7,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        d="M123.65 260.46C123.45 261.18 123.16 261.97 122.67 263.01C122.56 263.25 122.44 263.5 122.31 263.74C122.29 263.79 122.16 264.02 122.11 264.1C121.86 264.53 121.59 264.96 121.3 265.38C120.12 267.1 119.04 268.27 117.33 269.69C114.49 272.04 110.51 273.79 106.6 274.13C105.06 274.26 103.96 275.51 103.63 276.99C103.4 278.04 103.73 280.22 105.29 280.08C116.19 279.13 125.3 271.12 128.27 260.68C128.62 259.46 128.08 257.68 126.61 257.59C125.03 257.5 124.01 259.16 123.64 260.45L123.65 260.46Z"
        fill="#DC5634"
      />
      <mask
        id="mask0_37_31"
        style={{
          maskType: "luminance",
        }}
        maskUnits="userSpaceOnUse"
        x="17"
        y="115"
        width="79"
        height="129"
      >
        <path
          d="M95.4317 118.49C95.2617 121.36 95.0217 125.46 94.7217 130.39C94.3617 136.18 91.2017 148.04 90.2017 161C89.2017 174.02 88.4217 184.16 86.7617 197.5C83.3417 224.87 83.94 233.9 78 237.5C75.74 238.86 71.0117 240.91 64.1917 241.86C54.8217 243.16 45.0117 244.51 35.8117 239.47C33.7317 238.33 29.6617 236.03 26.8217 231.37C25.6917 229.52 24.8817 227.47 23.7217 217.76C22.9417 211.21 22.1217 204.37 21.9017 195.4C21.8517 193.25 21.7817 189.81 21.3217 185.02C20.9517 181.16 20.4517 178.27 20.3317 177.59C19.3817 171.85 18.5 164.5 19.5 145.5C19.32 139.73 17.3617 124.18 17.2617 115.27C17.7317 115.43 18.2217 115.59 18.6917 115.75C18.8717 115.81 19.0517 115.88 19.2317 115.94C19.9317 116.19 20.6417 116.43 21.4117 116.58C27.2617 117.65 36.6017 118.89 46.7117 119.82C58.1517 121.68 69.7017 122.37 81.2517 121.11C82.8317 120.94 86.1017 121.04 90.2017 120.08C92.4117 119.56 94.2017 118.94 95.4217 118.47L95.4317 118.49Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_37_31)">
        <motion.path
          initial={{
            d: "M69.7624 167.63C44.5824 178.24 36.1724 198.54 21.8824 195.39C10.6524 192.92 4.38242 177.86 0.882421 165.91C-1.80758 167.76 -6.70758 171.72 -7.12758 177.29C-7.62758 184.02 -0.417579 188.6 1.91242 192.11C13.8724 210.17 29.8124 226.77 50.3924 233.55C75.6124 241.85 104.692 234.84 124.122 218.26C126.332 216.37 152.252 193.82 146.092 177.97C139.672 161.41 99.4224 155.14 69.7624 167.64V167.63Z",
          }}
          animate={{
            d: "M77 106.984C51.8508 124.014 30.7725 117.056 16.5 112C5.28375 108.035 4.52064 141.995 1.02493 122.814C-1.66178 125.783 -6.55578 132.14 -6.97526 141.08C-7.47465 151.883 -0.27348 159.234 2.05367 164.868C13.999 193.856 29.9195 220.501 50.4743 231.384C75.6634 244.706 104.708 233.455 124.114 206.842C126.321 203.808 152.21 167.613 146.057 142.172C139.645 115.591 106.624 86.9359 77 107V106.984Z",
          }}
          transition={{
            duration: 2,
            delay: 0.5,
            ease: "easeInOut",
          }}
          fill="#356A3C"
        />
        <motion.path
          initial={{
            d: "M25.5726 179.11C45.6826 180.06 49.6326 201.64 66.8926 204.36C78.0526 206.12 94.4226 199.94 117.973 167.05C122.373 171.47 148.473 198.25 140.233 219.32C138.483 223.79 134.873 226.03 128.883 232.45C114.113 248.3 95.6626 262.06 74.2526 265.36C58.0426 267.86 40.0126 257.77 4.04259 238.16C-25.2974 222.17 -29.5274 215.25 -28.9474 210.1C-28.4074 205.26 -22.8374 201.83 -11.7274 195.18C6.66259 184.16 15.8526 178.65 25.5826 179.11H25.5726Z",
          }}
          animate={{
            d: "M25.6242 139.598C45.7547 140.984 49.7087 172.473 66.9862 176.442C78.1576 179.01 94.5442 169.993 118.118 122C122.523 128.45 148.649 167.527 140.401 198.272C138.649 204.794 135.035 208.063 129.039 217.431C114.254 240.559 95.7855 260.637 74.3537 265.452C58.1272 269.1 40.0789 254.377 4.0723 225.763C-25.2976 202.43 -29.5319 192.333 -28.9513 184.818C-28.4107 177.755 -22.835 172.75 -11.7137 163.047C6.69496 146.967 15.8943 138.927 25.6342 139.598H25.6242Z",
          }}
          transition={{
            duration: 2,
            ease: "easeInOut",
          }}
          fill="#F5C545"
        />
      </g>
    </svg>
  );
};

export const GameLoading: React.FC<LoadingCupProps> = memo((props) => {
  const { onLoadingComplete } = props;
  const { progress } = useRandomLoadingProgress({
    duration: 0.7,
    autoStart: true,
    externalIsDone: true,
  });

  useEffect(() => {
    if (progress >= 100) {
      setTimeout(() => {
        onLoadingComplete?.();
      }, 500);
    }
  }, [onLoadingComplete, progress]);

  return (
    <GameLoadingWrapper
      exit={{ opacity: 0 }}
      id="game-loading"
      className="flex flex-col items-center justify-center"
    >
      <img className="game-loading__logo" src={PrimaryLogo} alt="" />

      <motion.div
        initial={{
          x: 8,
          y: 20,
        }}
        className="game-loading__cup flex justify-center relative"
      >
        <Cup width={"45%"} isComplete={progress >= 100} />
        <div className="absolute text-base font-redRose top-[25%] -translate-x-[5px] font-medium text-main-primary">{`${progress}%`}</div>
      </motion.div>
    </GameLoadingWrapper>
  );
});

GameLoading.displayName = "GameLoading";
