// Libraries
import React, { memo, useMemo } from "react";
import styled, { css } from "styled-components";
import { motion, Variants } from "motion/react";
import { useDraggable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import { useRecoilState } from "recoil";
import clsx from "clsx";

// State
import { gameState } from "../state";

// Assets
import FingerImg from "assets/images/mix-and-match/finger.webp";

// Schemas
import { Ingredient } from "schemas";
import { useDelay } from "hooks";

const StyledIngredientItem = styled(motion.div)<{
  $isTemporarySelected?: boolean;
  $isSelected?: boolean;
}>`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    position: relative;
    z-index: 10;
    transition: filter 0.3s ease-in-out;
  }

  ${({ $isTemporarySelected, $isSelected }) =>
    $isTemporarySelected &&
    !$isSelected &&
    css`
      img {
        filter: drop-shadow(2px 2px 3px rgba(0, 0, 0, 0.5));
        animation: sparkle 0.5s ease-in-out infinite alternate forwards;
      }
    `}

  .finger-indicator {
    position: absolute;
    bottom: -10px;
    right: -40px;
    z-index: 20;
    transform-origin: bottom right;
  }

  @keyframes sparkle {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0.7;
    }
  }
`;

interface IngredientItemProps
  extends React.ComponentProps<typeof StyledIngredientItem> {
  ingredient: Ingredient;
  index: number;
}

const ingredientItemVariants: Variants = {
  selected: {
    opacity: 1,
    scale: 2,
    rotate: 0,
    transition: {
      duration: 0.3,
      ease: "backInOut",
    },
  },
  shake: {
    rotate: [0, 10, -10, 0],
    opacity: 1,
    scale: 1,
    transition: {
      duration: 3,
      ease: "easeInOut",
      repeat: Infinity,
      repeatType: "reverse",
    },
  },
};

export const IngredientItem: React.FC<IngredientItemProps> = memo((props) => {
  const { ingredient, index } = props;

  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: `drag-${ingredient.key}`,
      data: {
        ingredient,
      },
    });
  const [{ temporarySelectedIngredient, selectedIngredients }] =
    useRecoilState(gameState);

  const isTemporarySelected =
    temporarySelectedIngredient?.key === ingredient.key;
  const isShowTutorial = !isDragging && isTemporarySelected;

  const style = transform
    ? {
        // Scale with translate
        transform: CSS.Transform.toString({
          ...transform,
          scaleX: 1.2,
          scaleY: 1.2,
        }),
        zIndex: 100,
      }
    : undefined;

  const isSelected = useMemo(() => {
    return selectedIngredients.some((item) => item.key === ingredient.key);
  }, [ingredient.key, selectedIngredients]);

  return (
    <StyledIngredientItem
      $isSelected={isSelected}
      $isTemporarySelected={isTemporarySelected}
      initial={{ opacity: 0, scale: 0.5 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2, delay: index * 0.1 }}
      viewport={{ once: true }}
      animate={!isTemporarySelected ? "shake" : "selected"}
      variants={ingredientItemVariants}
      key={ingredient.key}
      className="ingredients__item"
    >
      <img
        ref={setNodeRef}
        src={ingredient.image}
        alt={ingredient.title}
        className={clsx("xxs:!w-[80px] w-[70px] object-contain", {
          "!opacity-0 pointer-events-none": isSelected,
        })}
        style={style}
        {...listeners}
        {...attributes}
      />
      {isShowTutorial && !isSelected && (
        <>
          <motion.img
            initial={{ rotate: "20deg", opacity: 0 }}
            animate={{ rotate: "0deg", opacity: 1 }}
            transition={{
              duration: 1,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            src={FingerImg}
            width={46}
            className="finger-indicator"
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="absolute bg-white rounded-full px-5 py-1 text-primary z-[1000] shadow-md -top-5 font-semibold whitespace-nowrap"
          >
            {ingredient.title}
          </motion.div>
        </>
      )}
    </StyledIngredientItem>
  );
});

IngredientItem.displayName = "IngredientItem";
