// Libraries
import React, { memo, useCallback, useEffect } from "react";
import styled from "styled-components";
import { AnimatePresence, motion } from "motion/react";
import { useImmer } from "use-immer";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { vibrate } from "zmp-sdk";
import clsx from "clsx";
import { useRecoilState, useRecoilValue } from "recoil";
import { Howl, Howler } from "howler";
import * as Sentry from "@sentry/react";
import { SpinLoading } from "@antscorp/ama-ui";

// Components
import { EmptyCup, GameLoading, Ingredients } from "./components";
import { HeaderTextLogo, InlineMessageBox } from "components";
import {
  GiftButton,
  PrimaryButton,
  SecondaryButton,
} from "pages/games/mix-and-match/components";

// Assets
import CupImg from "assets/images/mix-and-match/cup.webp";
import CocktailShakerSound from "assets/sound-effects/cocktail-shaker.mp3";

// Sounds
import ErrorSound from "assets/sound-effects/error.mp3";
import CloseBottleSound from "assets/sound-effects/close-bottle.mp3";

// State
import { gameState, gameStateDefault, isCupComplete } from "./state";
import { allocatedVoucherDefault, mixAndMatchState } from "../../state";

// Hooks
import {
  useAppConfig,
  useDeepCompareEffect,
  useRegisterLoyaltyCustomer,
  useRemainPlays,
  useResponsive,
  useShakeDetector,
  useUserInfo,
  useViewPage,
} from "hooks";

// Constants
import { MISSIONS } from "../../constants";

// Utils
import {
  callCdpEvent,
  dayjs,
  formatEventDateTime,
  getRandomItem,
  padToTwoDigits,
} from "utils";

// Schemas
import { Ingredient, Mission } from "schemas";
import { APP_CONFIG, EVENT_CONFIG } from "constant";
import { useAllocateVoucherMixAndMatch, useGetRandomMission } from "queries";

interface GameScreenProps {}

const GameScreenWrapper = styled(motion.div)`
  position: relative;
  background-color: var(--color-background-game);
  padding-top: var(--header-padding-top);
  height: 100vh;
  overflow: hidden;
`;

export const GameScreen: React.FC<GameScreenProps> = memo((props) => {
  const [game, setGame] = useRecoilState(gameState);
  const [mixAndMatch, setMixAndMatch] = useRecoilState(mixAndMatchState);
  const cupComplete = useRecoilValue(isCupComplete);
  const { appSettings } = useAppConfig();
  const { userInfo } = useUserInfo();
  const { isCanPlay } = useRemainPlays({
    apiConfig: {
      endPoint: "/mix-match/can-allocate",
    },
  });
  const { isLargeMobile } = useResponsive();

  useRegisterLoyaltyCustomer();

  useViewPage({
    pageCate: EVENT_CONFIG.MIX_AND_MATCH,
    pageType: "choose_gift_box",
  });

  // DnD
  const mouseSensor = useSensor(MouseSensor, {
    // activationConstraint: {},
  });
  const touchSensor = useSensor(TouchSensor, {
    // activationConstraint,
  });
  const keyboardSensor = useSensor(KeyboardSensor, {});
  const sensors = useSensors(mouseSensor, touchSensor, keyboardSensor);

  // Variables
  const {
    isLoading,
    isShaking,
    isReadyToShake,
    isDetectUserShake,
    selectedIngredients,
    isOutOfVoucher,
  } = game;
  const { bonusPlays = APP_CONFIG.GAMES.MIX_AND_MATCH.BONUS_PLAYS } =
    appSettings?.games?.mixAndMatch || {};
  const { systemErrorMessages } = appSettings?.globals || {};

  // Refs
  const timeOutShakeRef = React.useRef<NodeJS.Timeout | null>(null);

  // Queries
  const { data: randomMissionData, isLoading: isLoadingMission } =
    useGetRandomMission({
      options: {
        refetchOnMount: "always",
        enabled: isCanPlay && !isLoading,
      },
    });
  const { mutateAsync: allocateVoucherMixAndMatch } =
    useAllocateVoucherMixAndMatch({
      options: {
        onSuccess({ code, errorCode }) {
          // If the code is not 200, it means that the voucher is out of stock
          if (code !== 200) {
            setGame((previous) => ({
              ...previous,
              isOutOfVoucher: true,
            }));
          }
        },
        onError() {
          setGame((previous) => ({
            ...previous,
            isOutOfVoucher: true,
          }));
        },
      },
    });

  // Effects
  useEffect(() => {
    return () => {
      setGame({
        ...gameStateDefault,
        isLoading: false,
      });
    };
  }, [setGame]);

  useEffect(() => {
    if (isReadyToShake) {
      if (isDetectUserShake && timeOutShakeRef.current) {
        clearTimeout(timeOutShakeRef.current);

        return;
      }

      timeOutShakeRef.current = setTimeout(() => {
        vibrate({
          milliseconds: 2000,
          type: "oneShot",
        });
        setGame((previous) => ({
          ...previous,
          isShaking: true,
        }));
      }, 3000);
    }
  }, [isDetectUserShake, isReadyToShake, setGame]);

  // Set random mission when user starts the game
  useEffect(() => {
    if (randomMissionData?.data) {
      setGame((previous) => ({
        ...previous,
        currentMission: randomMissionData.data.mission,
        sessionId: randomMissionData.data.sessionId,
      }));
    }
  }, [randomMissionData?.data, setGame]);

  // Set allocated voucher to default when the game is shaking
  useDeepCompareEffect(() => {
    if (isShaking) {
      const cocktailShaker = new Howl({
        src: CocktailShakerSound,
        volume: 1,
      });
      cocktailShaker.play();

      setTimeout(async () => {
        // Allocate voucher
        const requestBody = {
          idByOa: userInfo.idByOA!,
          phoneNumber: userInfo.phoneNumber,
          missionId: game.currentMission?.id || 0,
          selectedIngredients: selectedIngredients.map(
            (ingredient) => ingredient.key
          ),
          sessionId: game.sessionId || "",
          userName: userInfo.name || "",
        };

        const { data } = await allocateVoucherMixAndMatch({
          bodyData: {
            idByOa: userInfo.idByOA!,
            phoneNumber: userInfo.phoneNumber,
            missionId: game.currentMission?.id || 0,
            selectedIngredients: selectedIngredients.map(
              (ingredient) => ingredient.key
            ),
            sessionId: game.sessionId || "",
            userName: userInfo.name || "",
          },
        });

        if (data?.webContents) {
          const { globalTracking, promotion_code } =
            data.webContents.contents || {};

          // Call Global Tracking Event
          fetch(globalTracking?.impression);
          fetch(globalTracking?.view);

          callCdpEvent({
            data: {
              page_type: "gift_code",
              page_cate: EVENT_CONFIG.MIX_AND_MATCH,
            },
            dims: {
              promotion_code: {
                id: promotion_code,
              },
            },
            uId: userInfo?.id,
          });

          // Call sentry log
          Sentry.captureMessage(
            `[SUMMER GAME][MIX AND MATCH] Allocate Voucher`,
            {
              level: "info",
              extra: {
                request: JSON.stringify(requestBody),
                response: JSON.stringify(data),
              },
            }
          );

          setMixAndMatch((previous) => ({
            ...previous,
            allocatedVoucher: data,
            currentScreen: "result",
          }));
        }

        cocktailShaker.stop();
      }, 1000);
    }
  }, [
    allocateVoucherMixAndMatch,
    game.currentMission?.id,
    game.sessionId,
    isShaking,
    selectedIngredients,
    setGame,
    setMixAndMatch,
    userInfo,
  ]);

  const handleShake = useCallback(() => {
    // Check if the game is already shaking then prevent multiple shakes
    if (isShaking) return;

    vibrate({
      milliseconds: 2000,
      type: "oneShot",
    });
    setGame((previous) => ({
      ...previous,
      isShaking: true,
    }));
  }, [isShaking, setGame]);

  useShakeDetector(handleShake, {
    resetTimeout: 2000,
    requiredShakes: 5,
    threshold: 2000,
    interval: 150,
    enabled: isReadyToShake,
    showPermissionPrompt: false,
  });

  // Handlers
  const onDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      const dragIngredient = active.data.current?.ingredient as Ingredient;

      if (dragIngredient) {
        setGame((previous) => ({
          ...previous,
          temporarySelectedIngredient: dragIngredient,
        }));
      }
    },
    [setGame]
  );

  const onDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (over && selectedIngredients.length < 3) {
        const dragIngredient = active.data.current?.ingredient as Ingredient;

        if (dragIngredient?.sound) {
          Howler.ctx.resume();

          const audio = new Howl({
            src: dragIngredient.sound.src,
            volume: 1.2 /* dragIngredient.sound.volume || 1 */,
          });

          audio.play();
        }

        setGame((previous) => ({
          ...previous,
          selectedIngredients: [
            ...previous.selectedIngredients,
            dragIngredient,
          ],
        }));
      }
    },
    [selectedIngredients.length, setGame]
  );

  const onClickReset = useCallback(() => {
    setGame((previous) => ({
      ...previous,
      selectedIngredients: [],
      temporarySelectedIngredient: null,
    }));

    const errorSound = new Howl({
      src: ErrorSound,
      volume: 1,
    });
    errorSound.play();
  }, [setGame]);

  const onClickShake = useCallback(() => {
    setGame((previous) => ({
      ...previous,
      isReadyToShake: true,
    }));

    setTimeout(() => {
      const closeBottleSOund = new Howl({
        src: CloseBottleSound,
        volume: 1,
      });
      closeBottleSOund.play();
    }, 500);
  }, [setGame]);

  // Renders
  const renderContent = () => {
    if (isLoadingMission) {
      return (
        <SpinLoading
          className="m-auto"
          style={{
            "--size": "48px",
            "--color": "var(--color-main-primary)",
          }}
        />
      );
    }

    if (!isCanPlay) {
      return (
        <div className="flex flex-col h-full justify-center items-center gap-8">
          <div className="text-center text-primary font-mansalva text-lg">
            Bạn đã hết lượt chơi hôm nay <br /> Đến Highlands - nhận thêm{" "}
            <strong>{padToTwoDigits(bonusPlays)}</strong> lượt chơi <br /> cho
            mỗi lượt mua hàng có tích điểm nhaaa!
          </div>
          <GiftButton />
        </div>
      );
    }

    if (isOutOfVoucher) {
      return (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex flex-col h-full justify-center items-center gap-8"
        >
          <div
            className="text-center text-primary font-mansalva text-lg"
            dangerouslySetInnerHTML={{
              __html:
                systemErrorMessages?.outOfCode ||
                APP_CONFIG.SYSTEM_ERROR_MESSAGES.outOfCode,
            }}
          />
          <GiftButton />
        </motion.div>
      );
    }

    return (
      <>
        {!isLoading && (
          <>
            <HeaderTextLogo className="shrink-0" color="#A43534" />

            <AnimatePresence>
              {!isReadyToShake && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <Ingredients />

                  <div className="flex flex-col items-center gap-3 w-full">
                    <div className="font-mansalva h-xs:text-base text-lg text-primary">
                      Chọn 3 nguyên liệu
                    </div>
                    <div className="flex items-center justify-center w-full gap-1">
                      <motion.div
                        className="relative z-10"
                        initial={{ scale: 1 }}
                        animate={{ scale: cupComplete ? 1.05 : 1 }}
                        {...(cupComplete && {
                          animate: {
                            scale: 1.05,
                          },
                          transition: {
                            duration: 0.5,
                            repeatType: "reverse",
                            repeat: Infinity,
                          },
                        })}
                      >
                        <PrimaryButton
                          width={isLargeMobile ? 130 : 120}
                          height={isLargeMobile ? 40 : 36}
                          className="!text-base xs:!text-lg"
                          disabled={!cupComplete}
                          onClick={onClickShake}
                        >
                          Đậy nắp và lắc
                        </PrimaryButton>
                      </motion.div>

                      <motion.img
                        initial={{
                          scaleX: -1,
                        }}
                        {...(cupComplete && {
                          animate: {
                            // Shake
                            // x: ["0%", "5%", "-5%", "0%"],
                            rotate: [0, 5, -5, 0],
                            transition: {
                              duration: 0.5,
                              repeat: Infinity,
                              repeatType: "reverse",
                              ease: "easeInOut",
                            },
                          },
                        })}
                        src={CupImg}
                        width={40}
                        height={"auto"}
                      />
                    </div>
                    <AnimatePresence>
                      {!!selectedIngredients.length && (
                        <SecondaryButton
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="!text-base xs:!text-lg"
                          height={isLargeMobile ? 40 : 32}
                          onClick={onClickReset}
                        >
                          Chọn lại nguyên liệu
                        </SecondaryButton>
                      )}
                    </AnimatePresence>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {isReadyToShake && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1 }}
                className="flex items-center justify-center w-full text-center font-mansalva text-primary mt-[10%] gap-2"
              >
                <span className="h-xs:text-base text-lg">
                  Cầm chắc điện thoại <br /> lắc để pha chế vị hè nhé!
                </span>
                <motion.img
                  initial={{
                    scaleX: -1,
                  }}
                  animate={{
                    rotate: [0, 5, -5, 0],
                    transition: {
                      duration: 0.5,
                      repeat: Infinity,
                      repeatType: "reverse",
                      ease: "anticipate",
                    },
                  }}
                  src={CupImg}
                  width={40}
                  height={"auto"}
                />
              </motion.div>
            )}

            <motion.div
              {...(isShaking && {
                animate: {
                  rotate: [0, 5, -5, 0],
                  transition: {
                    duration: 0.5,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "anticipate",
                  },
                },
              })}
              className={clsx(
                "w-full h-full flex justify-center origin-center",
                {
                  "absolute inset-0": isReadyToShake,
                }
              )}
            >
              <EmptyCup />
            </motion.div>
          </>
        )}
      </>
    );
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
    >
      <GameScreenWrapper
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        id="game-screen"
        className="w-full h-full flex flex-col items-center px-6"
      >
        <AnimatePresence>
          {isLoading && isCanPlay && (
            <GameLoading
              onLoadingComplete={() => {
                setGame((previous) => ({
                  ...previous,
                  isLoading: false,
                }));
              }}
            />
          )}
        </AnimatePresence>

        {renderContent()}
      </GameScreenWrapper>
    </DndContext>
  );
});

GameScreen.displayName = "GameScreen";
