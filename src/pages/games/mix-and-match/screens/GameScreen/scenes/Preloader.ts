// Libraries
import { Scene } from "phaser";

import HighlandsLogo from "assets/images/mix-and-match/highlands-logo.png";

export class Preloader extends Scene {
  constructor() {
    super("Preloader");
  }

  init() {
    // //  A simple progress bar. This is the outline of the bar.
    // this.add
    //   .rectangle(this.scale.width / 2, this.scale.height / 2, 300, 32)
    //   .setStrokeStyle(1, 0xffffff);
    // const loadingText = this.make.text({
    //   x: this.scale.width / 2,
    //   y: this.scale.height / 2 - 50,
    //   text: "Loading...",
    //   style: {
    //     font: "20px monospace",
    //     color: "#ffffff",
    //   },
    // });
    // loadingText.setOrigin(0.5, 0.5);

    // //  This is the progress bar itself. It will increase in size from the left based on the % of progress.
    // const bar = this.add.rectangle(
    //   this.scale.width / 2 - 145,
    //   this.scale.height / 2,
    //   4,
    //   23,
    //   0xffffff
    // );

    // //  Use the 'progress' event emitted by the LoaderPlugin to update the loading bar
    // this.load.on("progress", (progress: number) => {
    //   console.log({ progress });

    //   //  Update the progress bar (our bar is 464px wide, so 100% = 464px)
    //   bar.width = 4 + 290 * 1;
    // });

    const cup = this.add.image(
      this.scale.width / 2,
      this.scale.height / 2,
      "loading-cup"
    );
  }

  preload() {
    // this.load.image("approved-icon", "/assets/approved-icon.png");
    // this.load.image("game-bg", "/assets/game-bg.webp");
    // this.load.spritesheet("platforms", "/assets/platforms.png", {
    //   frameWidth: 48,
    //   frameHeight: 16,
    // });
    this.load.image("highlands-logo", HighlandsLogo);
  }

  create() {
    // this.cameras.main.fadeOut(500, 0, 0, 0); // Mờ dần trong 1 giây
    // this.time.delayedCall(500, () => this.scene.start("Game")); // Chuyển scene sau khi fade xong
  }
}
