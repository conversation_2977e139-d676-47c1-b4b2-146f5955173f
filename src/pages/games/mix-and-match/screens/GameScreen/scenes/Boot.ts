// Libraries
import { Scene } from "phaser";

// Assets
import Highlands<PERSON>ogo from "assets/images/mix-and-match/highlands-logo.png";
import LoadingCup from "assets/svg/cup.svg";

export class Boot extends Scene {
  constructor() {
    super({ key: "Boot" });
  }

  preload() {
    this.load.image("highlands-logo", HighlandsLogo);
    this.load.image("loading-cup", LoadingCup);
    // this.load.image("bg", "/assets/bg-introduction.webp");
    // this.load.image("gd-logo", "/assets/gold-digging-logo.webp");
    // this.load.image("start-button", "/assets/start-button.webp");
    // this.load.image("shaker", "/assets/shaker.png");
  }

  create() {
    this.scene.start("Preloader");
  }
}
