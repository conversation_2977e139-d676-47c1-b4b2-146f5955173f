import { Scene } from "phaser";

export class Game extends Scene {
  constructor() {
    super("Game");
  }

  cup;
  waterShape;
  ingredientList: any[] = [];
  ingredientColors = [0xff4444, 0xff9933, 0xffee33];
  waveTime = 0;
  waterLevel = 0;
  waterColors: any[] = [];
  centerX;
  centerY;
  cupX;
  cupY;
  cupWidth = 120;
  cupHeight = 180;

  preload() {}

  create() {
    // Tính toán vị trí ly và các nguyên liệu
    this.centerX = this.cameras.main.width / 2;
    this.centerY = this.cameras.main.height / 2;

    this.cupX = this.centerX - this.cupWidth / 2;
    this.cupY = this.centerY;

    // Vẽ ly
    this.cup = this.add.graphics();
    this.cup.lineStyle(4, 0xffffff);
    this.cup.strokeRect(this.cupX, this.cupY, this.cupWidth, this.cupHeight);

    // Vẽ vùng nước
    this.waterShape = this.add.graphics();

    // Tạo 3 nguyên liệu kh<PERSON>c nhau
    this.ingredientColors.forEach((color, index) => {
      const ing: any = this.add
        .circle(this.centerX - 100 + index * 100, 100, 20, color)
        .setInteractive();
      ing.setData("color", color); // lưu màu vào object
      this.input.setDraggable(ing);
      this.ingredientList.push(ing);
    });

    // Xử lý kéo thả
    this.input.on("drag", (pointer, gameObject, dragX, dragY) => {
      gameObject.x = dragX;
      gameObject.y = dragY;
    });

    this.input.on("dragend", (pointer, gameObject) => {
      if (this.checkInCup(gameObject.x, gameObject.y)) {
        const color = gameObject.getData("color") || 0x00aaff;
        this.spawnWaterEffect(gameObject.x, gameObject.y, color);
        this.updateWaterLevel(color);
        gameObject.x = gameObject.input.dragStartX;
        gameObject.y = gameObject.input.dragStartY;
      }
    });
  }

  checkInCup(x, y) {
    return (
      x > this.cupX &&
      x < this.cupX + this.cupWidth &&
      y > this.cupY &&
      y < this.cupY + this.cupHeight
    );
  }

  spawnWaterEffect(x, y, color) {
    const drop = this.add.circle(x, y, 5, color);
    this.tweens.add({
      targets: drop,
      y: this.cupY + this.cupHeight - this.waterLevel - 10,
      alpha: 0,
      duration: 500,
      onComplete: () => drop.destroy(),
    });

    this.waveTime = 0; // reset sóng
  }

  updateWaterLevel(color) {
    this.waterLevel += 10;
    this.waterColors.push({ color, level: this.waterLevel }); // thêm lớp nước
  }

  drawWaterWave() {
    if (this.waterLevel === 0) return;

    this.waterShape.clear();

    let baseY = this.cupY + this.cupHeight;

    for (let i = this.waterColors.length - 1; i >= 0; i--) {
      const { color, level } = this.waterColors[i];
      const topY = baseY - 10; // mỗi lớp cao 10px
      const waveHeight = 5;
      const waveLength = 30;
      const points = 20;

      this.waterShape.fillStyle(color, 0.7);
      this.waterShape.beginPath();
      this.waterShape.moveTo(this.cupX, baseY);
      this.waterShape.lineTo(this.cupX, topY);

      for (let j = 0; j <= points; j++) {
        const x = this.cupX + (j / points) * this.cupWidth;
        const y =
          topY +
          Math.sin((j * 2 * Math.PI) / waveLength + this.waveTime) * waveHeight;
        this.waterShape.lineTo(x, y);
      }

      this.waterShape.lineTo(this.cupX + this.cupWidth, baseY);
      this.waterShape.closePath();
      this.waterShape.fillPath();

      baseY = topY;
    }
  }

  update(time, delta) {
    this.waveTime += 0.05;
    this.drawWaterWave();
  }
}
