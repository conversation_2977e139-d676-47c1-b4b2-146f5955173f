// Libraries
import { atom, selector } from "recoil";

// Schemas
import { Ingredient, Mission } from "schemas";

interface GameState {
  isLoading: boolean;
  temporarySelectedIngredient: Ingredient | null;
  selectedIngredients: Ingredient[];
  isReadyToShake: boolean;
  isShaking: boolean;
  isDetectUserShake: boolean;
  currentMission?: Mission;
  sessionId?: string;
  isOutOfVoucher?: boolean;
}

export const gameStateDefault: GameState = {
  isLoading: true,
  temporarySelectedIngredient: null,
  selectedIngredients: [],
  isReadyToShake: false,
  isShaking: false,
  isDetectUserShake: false,
  currentMission: undefined,
  sessionId: undefined,
  isOutOfVoucher: false,
};

export const gameState = atom<GameState>({
  key: "gameState",
  default: gameStateDefault,
});

export const isCupComplete = selector({
  key: "isCupComplete",
  get: ({ get }) => {
    const { selectedIngredients } = get(gameState);
    return selectedIngredients.length === 3;
  },
});
