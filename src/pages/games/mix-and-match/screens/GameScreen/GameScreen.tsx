// Libraries
import { AUTO, CANVAS, Game, Scale, Types } from "phaser";
import React, { memo, useLayoutEffect, useRef } from "react";
import { motion } from "motion/react";

// Components
import { Boot, Preloader, Game as GameScene } from "./scenes";

interface MainGameProps {}

const dpr = window.devicePixelRatio;

const gameConfig: Types.Core.GameConfig = {
  type: AUTO,
  scale: {
    mode: Scale.RESIZE,
    autoCenter: Scale.CENTER_BOTH,
  },
  render: {
    pixelArt: false,
    antialiasGL: true,
    antialias: true,
    roundPixels: true,
  },
  physics: {
    default: "arcade",
    arcade: {
      gravity: { y: 300, x: 0 },
    },
  },
  scene: [Boot, Preloader, GameScene /* MainMenu, */ /* GameScene */],
};
export const GameScreen: React.FC<MainGameProps> = memo((props) => {
  // Refs
  const game = useRef<Game | null>(null!);

  useLayoutEffect(() => {
    game.current = new Game({
      ...gameConfig,
      parent: "game-container",
    });

    return () => {
      if (game.current) {
        game.current.destroy(true);
      }
    };
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      id="game-container"
      className="w-full h-full"
    />
  );
});

GameScreen.displayName = "GameScreen";
