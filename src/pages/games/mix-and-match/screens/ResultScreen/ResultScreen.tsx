// Libraries
import React, { memo, useCallback, useEffect, useMemo } from "react";
import { motion, Variants } from "motion/react";
import styled from "styled-components";
import BarCode from "react-barcode";
import { useRecoilState } from "recoil";
import { Howl } from "howler";
import FireWorks from "react-canvas-confetti/dist/presets/fireworks";

// Components
import { HeaderTextLogo } from "components";
import {
  LeaderBoard,
  MoreButton,
  PrimaryButton,
  SecondaryButton,
} from "../../components";

// Assets
import AllocatedVoucher1 from "assets/images/mix-and-match/allocated-voucher/allocated-voucher-1.webp";
import Scheme1 from "assets/images/mix-and-match/schemes/bogo-caffe.webp";
import CameraShutterSound from "assets/sound-effects/camera-shutter.mp3";
import YaySound from "assets/sound-effects/yay.mp3";

// Hooks
import { useDelay, useNavigateWithSearch, useUserInfo } from "hooks";

// State
import { mixAndMatchState } from "../../state";

// Constants
import { RESULT_THEME } from "../../constants";
import { replaceTemplatePlaceholders } from "utils";

interface ResultScreenProps {}

const ResultScreenWrapper = styled(motion.div)`
  position: absolute;
  inset: 0;
  padding-top: var(--header-padding-top);

  .voucher-img {
    height: auto;
  }
`;

export const ResultScreen: React.FC<ResultScreenProps> = memo((props) => {
  const { userInfo } = useUserInfo();
  const isShowFlash = useDelay(1500);
  const navigate = useNavigateWithSearch();
  const [{ allocatedVoucher }, setMixAndMatch] =
    useRecoilState(mixAndMatchState);

  // Variables
  const {
    consolationMessage,
    consolationTitle,
    promotion_code,
    backgroundColor,
    image_url,
    score,
    id,
  } = allocatedVoucher?.webContents?.contents || {};

  const imageVariants: Variants = {
    visible: {
      x: [300, 200, 100, 0],
      y: [300, 200, 100, 0],
      rotate: [45, 20, -20, 0],
      transition: {
        duration: 1.5,
        ease: "easeInOut",
        times: [0, 0.25, 0.5, 0.75, 1],
        type: "tween",
      },
    },
    flash: {
      x: 0,
      y: 0,
      opacity: [1, 0, 1],
      transition: {
        duration: 0.8,
        ease: "circInOut",
        times: [0, 0.5, 1],
        type: "tween",
      },
    },
  };

  useEffect(() => {
    if (isShowFlash) {
      const cameraShutterSound = new Howl({
        src: CameraShutterSound,
        volume: 1,
      });

      cameraShutterSound.play();
    }
  }, [isShowFlash]);

  useEffect(() => {
    setTimeout(() => {
      const yaySound = new Howl({
        src: YaySound,
        volume: 1,
      });
      yaySound.play();
    }, 1000);
  }, []);

  // Memos
  const resultTheme = useMemo(() => {
    return RESULT_THEME[backgroundColor || "default"] || RESULT_THEME.default;
  }, [backgroundColor]);

  const onClickTermAndCondition = useCallback(() => {
    navigate(`/vouchers/${id}`, {
      newParams: {
        isOpenTAC: "true",
      },
    });
  }, [id, navigate]);

  const onClickPlayAgain = useCallback(() => {
    setMixAndMatch((previous) => ({
      ...previous,
      currentScreen: "game",
      allocatedVoucher: null,
    }));
  }, [setMixAndMatch]);

  return (
    <ResultScreenWrapper
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      style={{
        backgroundColor: resultTheme.background,
      }}
      className="flex flex-col items-center"
    >
      <HeaderTextLogo className="shrink-0 mb-8" color={resultTheme.logoColor} />

      <motion.img
        initial={{ x: 300, y: 300 }}
        animate={isShowFlash ? "flash" : "visible"}
        variants={imageVariants}
        className="voucher-img w-[70%] h-xs:w-[50%]"
        src={image_url}
        alt="Allocated Voucher"
      />

      {isShowFlash && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="flex flex-col w-full h-full mt-6 text-center px-4 xs:px-10 pb-10 overflow-auto hide-scrollbar"
          style={{
            color: resultTheme.textColor,
          }}
        >
          <div>
            <div className="font-semibold h-xs:text-xs mb-1">{`+${score} điểm vào thứ hạng của bạn`}</div>
            <div
              className="capitalize text-lg h-xs:text-sm font-normal"
              dangerouslySetInnerHTML={{
                __html: consolationTitle || "Chúc mừng bạn",
              }}
            ></div>
            <div
              className="text-lg h-xs:text-sm font-bold leading-[1.1]"
              dangerouslySetInnerHTML={{
                __html:
                  replaceTemplatePlaceholders(consolationMessage || "", {
                    userName: userInfo.name || "",
                  }) || "",
              }}
            />
            <div className="flex w-full items-center justify-center mt-4">
              <BarCode
                background="#ffffff"
                fontSize={12}
                fontOptions="bold"
                font="Roboto"
                value={`${promotion_code || ""}`.toUpperCase()}
                height={50}
                width={1.5}
              />
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1 }}
            className="flex flex-col items-center mt-12"
          >
            <PrimaryButton
              variant="supperLong"
              onClick={onClickPlayAgain}
              backgroundColor={resultTheme.primaryButton.background}
              style={{
                color: resultTheme.primaryButton.color,
              }}
            >
              Chơi tiếp
            </PrimaryButton>
            <div className="flex items-center justify-center gap-2 mt-5">
              <SecondaryButton
                style={{
                  color: resultTheme.secondaryButton.color,
                }}
                onClick={onClickTermAndCondition}
              >
                Túi quà hè của bạn
              </SecondaryButton>

              <LeaderBoard
                primaryButtonProps={{
                  variant: "long",
                  children: "Chơi tiếp",
                  onClick: onClickPlayAgain,
                }}
                moreButtonProps={{
                  style: {
                    color: resultTheme.secondaryButton.color,
                  },
                }}
              />
            </div>
          </motion.div>
        </motion.div>
      )}

      <FireWorks
        autorun={{
          delay: 1000,
          speed: 2,
          duration: 4000,
        }}
        decorateOptions={(options) => {
          return {
            ...options,
            colors: [
              "#FF0000",
              "#B71C1C",
              "#FF4D4D",
              "#FF5722",
              "#8B0000",
              "#FFD700",
              "#FFC107",
              "#FFECB3",
              "#FFB300",
              "#FFA000",
            ],
            particleCount: 200,
            shapes: ["circle", "square"],
            gravity: 1.5,
            zIndex: 1000,
          };
        }}
      />
    </ResultScreenWrapper>
  );
});

ResultScreen.displayName = "ResultScreen";
