// Libraries
import { motion } from "motion/react";
import React, { memo } from "react";
import BarCode from "react-barcode";
import { useRecoilValue } from "recoil";
import styled from "styled-components";
import { useNavigate } from "zmp-ui";

// Components
import { StarLogoIcon } from "components";

// State
import { affiliateState } from "../state";

// Assets
import Coupon1 from "assets/images/others/coupon_image.webp";

// Animations
import { commonVariants } from "../variants";
import { dayjs } from "utils";

const AllocatedVoucherWrapper = styled(motion.div)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  align-items: center;
  width: 100%;
  color: #e1d5c6;
  font-size: 13px;

  /* .logo {
    width: 7vh;
    z-index: 100;
    position: relative;
    margin-right: -72%;
    aspect-ratio: 1 / 1;
    font-size: 17px;

    .name {
      color: #fff8ef;
      z-index: 1000;
      position: absolute;
      font-weight: bold;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    svg {
      z-index: 99;

      width: 100%;
      height: 100%;
    }
  } */

  .image {
    width: 74%;
    aspect-ratio: 1 / 1;
    position: relative;
    margin-top: -30px;
    border-radius: 15px 15px 0 0;
    object-fit: cover;
    object-position: top;
  }

  .rectangle {
    z-index: 99;
    box-shadow: 1.45px 0px 7.23px 0px rgba(0, 0, 0, 0.1);
    overflow: visible;
    width: 83%;
    height: 23vh;
    background-color: var(--color-main-primary);
    border-radius: 8px 8px 15px 15px;
    align-items: center;
    display: flex;
    flex-direction: column;

    .barcode {
      width: 100%;
      height: 10vh;
      flex-shrink: 0;
      background-color: #fff8ef;
      width: 76%;
      padding: 0px 3px 0px 3px;

      border-radius: 0px 0px 15px 15px;
      box-shadow: 1.45px 0px 7.23px 0px rgba(0, 0, 0, 0.1);
      svg {
        height: 10vh;
        flex-shrink: 0;
        background-color: #fff8ef;
        width: 76%;
        padding: 0px 3px 0px 3px;

        border-radius: 0px 0px 15px 15px;
        text {
          fill: #6782d9;
        }
      }
    }
  }

  .details {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    gap: 10px;
    font-family: "Roboto", cursive;
  }

  // Responsive
  @media only screen and (max-height: 641px) {
    padding: 0 8px 0 8px;

    .details {
      font-size: 11px;
    }

    .image {
      width: 40vh;
    }
  }

  @media only screen and (min-height: 641px) and (max-height: 668px) {
    padding: 5px;
    .details {
      font-size: 12px;
    }
  }

  @media only screen and (min-height: 855px) {
    padding: 0;
    .details {
      font-size: 14px;
    }
  }
`;

export const AllocatedVoucher = memo(() => {
  const navigate = useNavigate();
  const { allocatedVoucher } = useRecoilValue(affiliateState) || {};
  const {
    scheme_id,
    promotion_code = "ERROR_CODE",
    start_date = "30.04.2025 | 11:53:29",
    expiry_date = "30.09.2025",
    image_url = "",
  } = allocatedVoucher?.webContents.contents || {};

  return (
    <AllocatedVoucherWrapper>
      {/* <motion.div
        className="logo"
        animate="zoomElastic"
        variants={commonVariants}
        transition={{ duration: 0.6, ease: "easeOut", delay: 1.5 }}
      >
        <StarLogoIcon color="#B1282F" />
        <span className="name">O2O</span>
      </motion.div> */}
      <motion.img
        src={image_url}
        className="image"
        animate="slideUp"
        variants={commonVariants}
        transition={{
          duration: 0.5,
          ease: "easeOut",
          delay: 1,
        }}
      />
      <motion.div
        className="rectangle"
        animate="zoomIn"
        variants={commonVariants}
        transition={{ duration: 0.4, ease: "easeOut", delay: 0.1 }}
      >
        <motion.div
          className="barcode"
          animate="flipDown"
          variants={commonVariants}
          initial={{
            rotateX: "180deg",
            transformOrigin: "top center",
          }}
          transition={{ duration: 0.4, ease: "easeOut", delay: 0.5 }}
        >
          <motion.div
            animate="revealLeftToRight"
            variants={commonVariants}
            initial={{
              clipPath: "inset(0 100% 0 0)",
              opacity: 0,
            }}
            transition={{ duration: 0.5, ease: "easeOut", delay: 0.8 }}
          >
            <BarCode
              background="#fff8ef"
              fontSize={13}
              fontOptions="bold"
              font="Roboto"
              value={`${promotion_code || ""}`.toUpperCase()}
              height={50}
              width={1.5}
            />
          </motion.div>
        </motion.div>
        <motion.div className="details">
          <span>
            Phát hành lúc: {dayjs(start_date).format("DD.MM.YYYY | HH:mm:ss")}
          </span>
          <span>Hạn sử dụng: {dayjs(expiry_date).format("DD.MM.YYYY")}</span>
          <span>Mã Coupon: {scheme_id}</span>
        </motion.div>
      </motion.div>
    </AllocatedVoucherWrapper>
  );
});

AllocatedVoucher.displayName = "AllocatedVoucher";
