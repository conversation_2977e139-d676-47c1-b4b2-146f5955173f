export const commonVariants = {
  zoomIn: {
    opacity: [0, 0.9, 1],
    z: [-200, -100, 0],
    scale: [0.5, 1.1, 1],
  },

  zoomPulse: {
    scale: [1, 1.03, 1],
  },

  slideFromLeft: {
    opacity: [0, 1, 1],
    scale: [0.95, 1.02, 1],
    x: ["-100%", "4%", 0],
  },

  slideFromRight: {
    opacity: [0, 1, 1],
    scale: [0.95, 1.02, 1],
    x: ["100%", "-4%", 0],
  },

  fadeIn: {
    opacity: [0, 1],
  },

  slideAndReturn: {
    opacity: [0, 1, 1, 1, 1],
    x: ["200%", "-200%", "10%", "10%", 0]
  },

  flipDown: {
    opacity: 1,
    rotateX: ['180deg', '0deg'],
    transformOrigin: "top center",
  },

  slideUp: {
    opacity: [0, 1],
    y: [100, 0],
  },

  slideUpBounce: {
    opacity: [0, 0.8, 1, 1, 1],
    y: [100, 30, 45, 45, 0],
  },

  zoomElastic: {
    opacity: [0, 0.8, 1],
    scale: [0.2, 1.3, 1],
  },

  revealLeftToRight: {
      opacity: 1,
      clipPath: "inset(0 0% 0 0)",
  }
};

const { zoomIn, slideFromLeft, zoomPulse, slideFromRight, fadeIn, slideUp, slideUpBounce, zoomElastic, flipDown } = commonVariants;

export const freeze1Variants = {
  hidden: { opacity: 0, x: "-120%", y: -300, rotate: -6.41 },
  dropFromLeft: {
    opacity: [0, 0.95, 1, 1, 1, 1],
    x: ["-120%", "-70%", "-65%", "-65%", "-65%", "-65%"],
    y: [-500, 110, 60, 90, 78, 85],
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
      times: [0, 0.35, 0.6, 0.75, 0.9, 1],
      delay: 1.2,
    },
  },
  wiggleLeft: {
    rotate: [-6.41, -6.41, -9, -4, -8, -5, -7, -5.5, -6.41, -6.41],
    transition: {
      duration: 2.2,
      ease: "easeInOut",
      times: [0, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 1],
      repeat: Infinity,
    },
  },
};

export const freeze2Variants = {
  hidden: { opacity: 0, x: "120%", y: -400, rotate: 6.62 },
  dropFromRight: {
    opacity: [0, 1, 1, 1, 1, 1],
    x: ["80%", "-30%", "-35%", "-35%", "-35%", "-35%"],
    y: [-600, 60, 10, 32, 22, 28],
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
      times: [0, 0.35, 0.6, 0.75, 0.9, 1],
      delay: 1.5,
    },
  }, 
  wiggleRight: {
    rotate: [6.62, 6.62, 9, 4, 8, 5.5, 7.2, 6, 6.62, 6.62],
    transition: {
      duration: 2.2,
      ease: "easeInOut",
      times: [0, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 1],
      repeat: Infinity,
      delay: 0.2,
    },
  },
};

export const buttonVariants = {
  hidden: { opacity: 0 },
  slideIn: {
    ...slideFromLeft,
    transition: { duration: 0.5, ease: "easeOut", delay: 0.7, times: [0, 0.6, 1], },
  },
  zoomPulse: {
    ...zoomPulse,
    transition: {
      duration: 1,
      ease: "easeInOut",
      repeat: Infinity,
      delay: 1.2,
    },
  },
  tap: {
    scale: 0.93,
  },
};