// Libraries
import { isEmpty } from "lodash-es";
import { easeOut, motion, useAnimation } from "motion/react";
import React, { useCallback, useEffect, useMemo, memo } from "react";
import styled from "styled-components";
import { useLocalStorage } from "usehooks-ts";
import { useImmer } from "use-immer";
import { MD5, SHA256 } from "crypto-js";
import { CalendarIcon, ChevronDownIcon } from "lucide-react";
import dayjs from "dayjs";
import { useRecoilState } from "recoil";
import { closeApp } from "zmp-sdk";

// Components
import {
  Button,
  CloseIcon,
  Text,
  InlineMessageBox,
  CustomCheckbox,
} from "components";
import { Form, Input, Modal } from "@antscorp/ama-ui-v1";
import {
  DatePicker as MobileDatePicker,
  Modal as ModalV2,
  Picker,
  Button as AmaButton,
  Checkbox,
} from "@antscorp/ama-ui";

// Hooks
import {
  useAppConfig,
  useNavigateWithSearch,
  useRequestZaloPermissions,
  useUserInfo,
  useViewPage,
} from "hooks";

// Constants
import {
  APP_CONFIG,
  EVENT_CONFIG,
  GENDER_OPTIONS,
  LOCAL_STORAGE_KEY,
  ZMA_ERROR,
} from "constant";

// States
import { affiliateState } from "../state";

// Assets
import highlandsLogo from "assets/images/logos/highlands-bean-logo.png";
import freezeTraXanh1 from "assets/images/others/freeze-tra-xanh-1.webp";
import freezeTraXanh2 from "assets/images/others/freeze-tra-xanh-2.webp";

// Utils
import { callCdpEvent, formatEventGender } from "utils";

// Animations
import {
  buttonVariants,
  commonVariants,
  freeze1Variants,
  freeze2Variants,
} from "../variants";

// Queries
import { useCheckUser } from "queries";
// import { useRemainPlays } from "hooks/useRemainPlays";

interface HomeProps {}

const HomeContainer = styled(motion.div)`
  padding-top: 8vh;
  color: var(--color-main-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.2;
  font-size: 34px;
  font-weight: bold;

  .remain {
    margin-top: 10px;
    font-size: 15px;
    font-weight: normal;
  }

  .zaui-btn {
    width: 230px;
    height: 45px;
    overflow: hidden;
    font-size: 15px;
    background: var(--color-main-primary);
    animation: none !important;
    margin-top: 10px;
    border: 1px solid var(--color-main-primary) !important;
    color: #fff8ef;

    &.zaui-btn-secondary {
      color: var(--color-main-primary);
      background-color: transparent !important;
    }
  }

  @media only screen and (max-width: 376px) {
    font-size: 30px;
    .remain {
      font-size: 14px;
    }
  }

  @media only screen and (min-width: 448px) {
    font-size: 37px;
  }

  img {
    position: absolute;

    &.freeze-1 {
      width: 56vh;
      z-index: 10;
      bottom: 50%;
      left: 50%;
      transform: translate(-65%, 85px) rotate(-6.41deg);
      will-change: opacity, transform;
    }

    &.freeze-2 {
      width: 50vh;
      z-index: 1;
      bottom: 50%;
      left: 50%;
      transform: translate(-35%, 28px) rotate(6.62deg);
      will-change: opacity, transform;
    }

    &.logo {
      width: 93%;
      bottom: -100%;
      left: 50%;
      transform: translateX(-50%);
      will-change: opacity;
    }

    // Responsive
    @media only screen and (max-height: 640px) {
      &.freeze-1 {
        width: 280px;
      }

      &.freeze-2 {
        width: 270px;
      }

      &.logo {
        max-width: 350px;
      }
    }

    @media only screen and (min-height: 640px) and (max-height: 668px) {
      &.freeze-1 {
        width: 330px;
      }

      &.freeze-2 {
        width: 325px;
      }
    }

    @media only screen and (min-height: 668px) and (max-height: 813px) {
      &.freeze-1 {
        width: 385px;
      }

      &.freeze-2 {
        width: 365px;
      }
    }
  }
`;

const FormContainer = styled(motion.div)`
  width: 100%;
  height: 100%;
  color: var(--color-main-primary);
  padding: 24px 10px;
  gap: 0;

  .close-button {
    position: absolute;
    z-index: 1000;
    color: #feeeeb;
    background-color: var(--color-main-primary);
    font-size: 5px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    padding: 7px;
    border-radius: 100%;
    top: -18px;
    left: 45%;
  }

  .zaui-btn {
    max-width: 200px;
    height: 42px;
    padding: 10px 20px;

    overflow: hidden;
    font-size: 13px;
    background: var(--color-main-primary) !important;
    animation: none !important;
    border: 1px solid var(--color-main-primary) !important;
    color: #dfd5c8 !important;
  }

  .ama-form-item {
    margin: 0;
    margin-top: 3%;

    .ama-form-item-label > label {
      color: var(--color-main-primary) !important;
      font-size: 13px;
      font-weight: bold;
    }

    .ama-form-item-control-input .ama-form-item-control-input-content {
      .ama-input,
      .ama-input-affix-wrapper,
      .ama-select .ama-select-selector {
        border-radius: 25px;
        background-color: #dfd5c8 !important;
        overflow: hidden;
        outline: none !important;
        border: none !important;
        box-shadow: none !important;
        font-size: 12px;
      }

      .ama-input-affix-wrapper {
        padding: 0 15px 0 0;
      }

      .ama-input {
        padding: 7px 20px;
      }
    }
  }

  .form-title {
    line-height: 1.2;
    font-size: 24px;
    font-family: "Red Rose", serif;
    font-weight: bold;
    color: var(--color-main-primary);
    text-align: center;
  }

  .form-description {
    padding: 2px 5px;
    line-height: 1.3;
    color: #3f3a3a;
    font-size: 12px;
    margin-bottom: 10px;
    text-align: center;
  }

  @media only screen and (max-height: 641px) {
    .close-button {
      width: 28px;
      height: 28px;
    }
    .ama-form-item {
      .ama-form-item-label > label {
        font-size: 12px;
      }

      input {
        font-size: 11px !important;
      }
    }
    .form-title {
      font-size: 23px;
    }

    .form-description {
      font-size: 11px;
    }
  }
`;

const ModalContainer = styled(Modal)`
  max-width: 400px;
  .ama-modal-content {
    background-color: #fff8ef !important;
    border-radius: 24px;
  }

  .ama-modal-root .ama-modal-mask {
    background-color: rgba(63, 58, 58, 81) !important;
  }

  .out-of-playtimes {
    margin-top: 0;
    padding-top: 5px;
  }

  @media only screen and (max-height: 641px) {
    .ama-modal-content {
      padding: 9px 10px 0 10px;
    }

    .out-of-playtimes {
      padding: 15px 10px 15px 10px;
    }
  }

  @media only screen and (max-width: 321px) {
    .out-of-playtimes {
      .text-main-primary {
        font-size: 13px !important;
      }
    }
  }
`;

type TState = {
  isModalOpen?: boolean;
  datePickerVisible?: boolean;
  genderPickerVisible?: boolean;
  sessionKey?: string;
  isShowLimitRequestMessage?: boolean;
  isShowRequestPermissionMessage?: boolean;
  isGameLoading?: boolean;
  isAllocatingVoucher?: boolean;
};

type TFormValues = {
  phone: string;
  dateOfBirth: any;
  gender: string;
};

export const Home: React.FC<HomeProps> = memo((props) => {
  // Hooks
  const [_, setAffiliate] = useRecoilState(affiliateState);
  const { userInfo } = useUserInfo();
  const [form] = Form.useForm();
  const navigate = useNavigateWithSearch();
  const {
    appSettings,
    gameValidation,
    isLoading: isLoadingAppConfig,
  } = useAppConfig();

  const values = Form.useWatch<TFormValues>([], form);
  const { systemErrorMessages } = appSettings?.globals || {};
  const { requestPermissionMessage } = appSettings?.games?.affiliate || {};

  const [cacheFormValues, setCacheFormValues] = useLocalStorage<{
    phone?: string;
    dateOfBirth?: string;
    gender?: string;
    isAcceptRule?: boolean;
  }>(LOCAL_STORAGE_KEY.AFFILIATE_FORM, {
    phone: undefined,
    dateOfBirth: "1990-01-01",
    gender: undefined,
    isAcceptRule: false,
  });

  // Queries
  const { mutateAsync: checkUser, isPending: isCheckUserPending } =
    useCheckUser({});

  // Hooks
  const { requestZaloPermissions } = useRequestZaloPermissions({
    onFail({ step, message, error }) {
      if (["flowOA", "allowPhone"].includes(step)) {
        setState((draft) => {
          draft.isShowRequestPermissionMessage = true;
        });
      }

      // If state code equal -203 it means user request so many times, then show limit Request message
      if (error.code === -203) {
        setState((draft) => {
          draft.isShowLimitRequestMessage = true;
          draft.isShowRequestPermissionMessage = false;
        });
      }

      setState((draft) => {
        draft.isGameLoading = false;
      });
    },
    async onFinish() {
      checkUser().then(({ data }) => {
        setState((draft) => {
          if (!data?.status) {
            setAffiliate((prev) => ({
              ...prev,
              currentScreen: "coupon-selection",
            }));
          } else {
            draft.isModalOpen = true;
          }
          draft.isGameLoading = false;
          draft.isShowRequestPermissionMessage = false;
        });
      });
    },
  });
  useViewPage({
    pageCate: EVENT_CONFIG.AFFILIATE,
    pageType: "home",
  });

  // const {
  //   isCanPlay,
  //   remainPlays,
  //   isLoading: isLoadingRemainPlays,
  // } = useRemainPlays({
  //   apiConfig: {
  //     endPoint: "/check-aff",
  //   },
  // });

  // States
  const [state, setState] = useImmer<TState>({
    isModalOpen: false,
    datePickerVisible: false,
    genderPickerVisible: false,
    isShowLimitRequestMessage: false,
    isShowRequestPermissionMessage: false,
    isAllocatingVoucher: false,
  });
  const {
    isModalOpen,
    isGameLoading,
    sessionKey,
    datePickerVisible,
    genderPickerVisible,
    isShowLimitRequestMessage,
    isAllocatingVoucher,
    isShowRequestPermissionMessage,
  } = state;

  // Memos
  const isSubmitDisabled = useMemo(() => {
    return !values?.phone || !values?.dateOfBirth || !values?.gender;
  }, [values]);

  // Effects
  useEffect(() => {
    if (!isEmpty(cacheFormValues)) {
      form.setFieldsValue({
        phone: cacheFormValues.phone,
        dateOfBirth: cacheFormValues.dateOfBirth
          ? dayjs(cacheFormValues.dateOfBirth).toDate()
          : undefined,
        gender: cacheFormValues.gender,
      });
    }
  }, [cacheFormValues, form]);

  useEffect(() => {
    if (userInfo?.phoneNumber) {
      setCacheFormValues((prev) => ({
        ...prev,
        phone: userInfo.phoneNumber,
      }));
    }
  }, [setCacheFormValues, userInfo.phoneNumber]);

  // Handlers
  const onCLickTakeDeals = useCallback(() => {
    setState((draft) => {
      draft.isGameLoading = true;
    });
    requestZaloPermissions();
  }, [requestZaloPermissions, setState]);

  const onClickMyVoucher = useCallback(() => {
    navigate("/gift", {
      newParams: {
        tab: "redeemed",
        voucherType: "voucher",
      },
    });
  }, [navigate]);

  const onFinishSubmit = useCallback(
    (values: TFormValues) => {
      const gender = formatEventGender(values?.gender);
      const eventData = {
        ec: "lead_form",
        ea: "submit",
        dims: {
          users: {
            user_id: userInfo?.id,
            identify_event: "submit_lead_miniapp",
            identify_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            id_by_oa: userInfo?.idByOA,
            name: userInfo?.name,
            phone: values?.phone,
            gender,
            birthday: dayjs(values?.dateOfBirth).format("YYYY-MM-DD"),
          },
          customers: {
            name: userInfo?.name,
            email: "",
            phone: values?.phone,
            customer_id: SHA256(values?.phone).toString(),
            zalo_uid: userInfo?.id,
            id_by_oa: userInfo?.idByOA,
            gender,
            birthday: dayjs(values?.dateOfBirth).format("YYYY-MM-DD"),
          },
        },
        items: [
          {
            type: "lead",
            id: MD5(
              `${dayjs().unix()}${values?.phone}${EVENT_CONFIG.AFFILIATE}`
            ).toString(),
            lead_event: EVENT_CONFIG.AFFILIATE,
            lead_locate: "miniapp",
            lead_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            name: userInfo?.name,
            phone: values?.phone,
            gender,
            birthday: dayjs(values?.dateOfBirth).format("YYYY-MM-DD"),
          },
        ],
        data: {
          identify_id: SHA256(values?.phone).toString(),
          identify_event: "submit_lead_miniapp",
          cdp_property_id: 565018501,
        },
      };

      callCdpEvent(eventData);

      setState((draft) => {
        draft.isModalOpen = false;
      });

      setCacheFormValues((prev) => ({
        ...prev,
        phone: values.phone,
        dateOfBirth: dayjs(values.dateOfBirth).format("YYYY-MM-DD"),
        gender: values.gender,
      }));

      setTimeout(() => {
        setAffiliate((prev) => ({
          ...prev,
          currentScreen: "coupon-selection",
        }));
      }, 500);
    },
    [
      setAffiliate,
      setCacheFormValues,
      setState,
      userInfo?.id,
      userInfo?.idByOA,
      userInfo?.name,
    ]
  );

  // Renders
  const renderModalContent = useCallback(() => {
    //     if (!isCanPlay) {
    //       return (
    //         <InlineMessageBox
    //           message={`Rất tiếc bạn đã hết lượt chơi. <br />
    // Mỗi ngày bạn có ${APP_CONFIG.GAMES.AFFILIATE.MAX_PLAY_TIMES} lượt chơi mới. Quay trở lại vào ngày mai để tiếp tục săn quà nhé!`}
    //           className="out-of-playtimes"
    //         >
    //           <AmaButton
    //             color="primary"
    //             block
    //             shape="rounded"
    //             onClick={onClickMyVoucher}
    //           >
    //             Quà tặng của tôi
    //           </AmaButton>
    //         </InlineMessageBox>
    //       );
    //     }

    return (
      <FormContainer>
        <CloseIcon
          className="close-button"
          onClick={() => setState({ isModalOpen: false })}
        />
        <div className="shrink-0 flex flex-col gap-2">
          <Text className="form-title">
            CHIA SẺ THÔNG TIN, NHẬN QUÀ LIỀN TAY
          </Text>
          <Text className="form-description">
            Highlands không hỏi gì nhiều đâu, chỉ cần vài dòng thông tin đơn
            giản để ship ưu đãi tới bạn liền nè. Nhập lẹ để chill cùng Highlands
            nhé!
          </Text>
        </div>
        <Form<TFormValues>
          form={form}
          layout="vertical"
          className="flex flex-col h-full w-full gap-2"
          initialValues={{
            phone: "",
            dateOfBirth: dayjs("1990-01-01").toDate(),
            gender: null,
          }}
          onFinish={onFinishSubmit}
        >
          <Form.Item<TFormValues>
            label="Số Điện Thoại"
            name="phone"
            className="relative"
          >
            <Input
              value={values?.phone}
              placeholder="Nhập số điện thoại"
              readOnly
            />
          </Form.Item>
          <Form.Item<TFormValues> label="Sinh Nhật" name="dateOfBirth">
            <div>
              <Input
                readOnly
                value={dayjs(values?.dateOfBirth).format("DD/MM/YYYY")}
                suffix={
                  <CalendarIcon
                    size={20}
                    color="#b5aa9d"
                    onClick={() =>
                      setState((draft) => {
                        draft.datePickerVisible = true;
                      })
                    }
                  />
                }
                onClick={() =>
                  setState((draft) => {
                    draft.datePickerVisible = true;
                  })
                }
              />
            </div>
          </Form.Item>
          <Form.Item<TFormValues> label="Giới Tính" name="gender">
            <div>
              <Input
                key={values?.gender}
                readOnly
                placeholder="Nam/Nữ/Khác"
                value={GENDER_OPTIONS?.[values?.gender]?.label}
                onClick={() =>
                  setState((draft) => {
                    draft.genderPickerVisible = true;
                  })
                }
                suffix={
                  <ChevronDownIcon
                    size={20}
                    color="#b5aa9d"
                    onClick={() =>
                      setState((draft) => {
                        draft.genderPickerVisible = true;
                      })
                    }
                  />
                }
              />
            </div>
          </Form.Item>
          <Form.Item style={{ marginTop: "15px" }} className="text-center">
            <Button
              disabled={isSubmitDisabled || !cacheFormValues.isAcceptRule}
              htmlType="submit"
            >
              CHỐT DEAL NGAY
            </Button>
          </Form.Item>

          <Form.Item noStyle>
            <Checkbox
              className="mt-2"
              checked={cacheFormValues.isAcceptRule}
              onChange={() =>
                setCacheFormValues((prev) => ({
                  ...prev,
                  isAcceptRule: !prev.isAcceptRule,
                }))
              }
            >
              Bằng việc chọn đồng ý, bạn sẽ tham gia vào chương trình thành viên
              Highlands Rewards.
            </Checkbox>
          </Form.Item>
        </Form>
      </FormContainer>
    );
  }, [
    form,
    values?.phone,
    values?.dateOfBirth,
    values?.gender,
    isSubmitDisabled,
    cacheFormValues.isAcceptRule,
    onFinishSubmit,
    setState,
    setCacheFormValues,
  ]);

  // Effects
  useEffect(() => {
    if (userInfo?.id) {
      // Set session ID
      setState((draft) => {
        draft.sessionKey = MD5(
          `${userInfo?.id}${dayjs().unix()}session`
        ).toString();
      });
    }
  }, [setState, userInfo]);

  // Animations
  const controlFreeze1 = useAnimation();
  const controlFreeze2 = useAnimation();
  const controlTakeDealButton = useAnimation();

  useEffect(() => {
    const animateSequence = async () => {
      await controlFreeze1.start(freeze1Variants.dropFromLeft);
      await controlFreeze1.start(freeze1Variants.wiggleLeft);
    };

    animateSequence();
  }, [controlFreeze1]);

  useEffect(() => {
    const animateSequence = async () => {
      await controlFreeze2.start(freeze2Variants.dropFromRight);
      await controlFreeze2.start(freeze2Variants.wiggleRight);
    };

    animateSequence();
  }, [controlFreeze2]);

  useEffect(() => {
    const animateSequence = async () => {
      await controlTakeDealButton.start("slideIn");
      await controlTakeDealButton.start("zoomPulse");
    };

    animateSequence();
  }, [controlTakeDealButton]);

  return (
    <>
      <HomeContainer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="flex flex-col h-full w-full overflow-hidden"
      >
        <motion.div
          style={{ width: "100%", textAlign: "center", perspective: 1000 }}
          variants={commonVariants}
          animate="zoomIn"
          transition={{
            duration: 0.5,
            ease: "easeInOut",
            times: [0, 0.6, 1],
            delay: 0.2,
            bounce: 0.2,
          }}
        >
          HÔM NAY BẠN GHÉ HIGHLANDS CHƯA?
        </motion.div>

        <motion.div
          variants={buttonVariants}
          animate={controlTakeDealButton}
          onTapStart={() => {
            controlTakeDealButton.start("tap");
          }}
          onTapCancel={() => {
            controlTakeDealButton.start("zoomPulse");
          }}
          onTap={() => {
            controlTakeDealButton.start("zoomPulse");
          }}
        >
          <Button
            style={{ marginTop: "20px" }}
            fullWidth
            onClick={onCLickTakeDeals}
            loading={isGameLoading}
          >
            LẤY DEAL, TỚI LIỀN
          </Button>
        </motion.div>

        <motion.div
          variants={commonVariants}
          animate="slideFromRight"
          transition={{
            duration: 0.5,
            ease: "easeOut",
            delay: 0.7,
            times: [0, 0.6, 1],
          }}
        >
          <Button fullWidth variant="secondary" onClick={onClickMyVoucher}>
            QUÀ TẶNG CỦA TÔI
          </Button>
        </motion.div>

        {/* <motion.div
          className="remain"
          variants={commonVariants}
          animate="fadeIn"
          transition={{
            duration: 0.5,
            delay: 2.3,
          }}
        >
          {`Bạn còn ${remainPlays} lượt chơi`}
        </motion.div> */}

        <div
          className="relative w-full"
          style={{ height: "100px", marginTop: "auto" }}
        >
          <motion.img
            className="freeze-1"
            src={freezeTraXanh1}
            initial="hidden"
            animate={controlFreeze1}
            variants={freeze1Variants}
          />
          <motion.img
            className="freeze-2"
            src={freezeTraXanh2}
            initial="hidden"
            animate={controlFreeze2}
            variants={freeze2Variants}
          />
          <motion.img
            className="logo"
            src={highlandsLogo}
            animate="fadeIn"
            transition={{ duration: 0.5, ease: "easeOut", delay: 0.6 }}
            variants={commonVariants}
          />
        </div>
      </HomeContainer>

      <ModalContainer
        centered
        open={isModalOpen}
        footer={null}
        closable={false}
        width={"88%"}
        maskProps={{
          style: {
            backgroundColor: "rgba(63, 58, 58, 0.8)",
          },
        }}
        onCancel={() => {
          setState((draft) => {
            draft.isModalOpen = false;
          });
        }}
      >
        {renderModalContent()}
      </ModalContainer>

      <MobileDatePicker
        visible={datePickerVisible}
        onClose={() =>
          setState((draft) => {
            draft.datePickerVisible = false;
          })
        }
        onConfirm={(value) => {
          // Call cdp tracking event date of birth
          callCdpEvent({
            ec: "field",
            ea: "input",
            data: {
              field_name: "birthday",
              field_value: dayjs(value).format("YYYY-MM-DD"),
              page_type: "form",
              page_cate: EVENT_CONFIG.AFFILIATE,
              session_key: sessionKey,
            },
            uId: userInfo?.id,
          });

          form.setFieldValue("dateOfBirth", value);
        }}
        value={values?.dateOfBirth}
        min={dayjs("1900/01/01").toDate()}
        max={dayjs().toDate()}
        onCancel={() =>
          setState((draft) => {
            draft.datePickerVisible = false;
          })
        }
      />

      <Picker
        visible={genderPickerVisible}
        value={[values?.gender]}
        columns={[Object.values(GENDER_OPTIONS) || []]}
        onCancel={() =>
          setState((draft) => {
            draft.genderPickerVisible = false;
          })
        }
        onConfirm={(value) => {
          // Call cdp tracking event field for gender
          callCdpEvent({
            ec: "field",
            ea: "input",
            data: {
              field_name: "gender",
              field_value: value,
              page_type: "form",
              page_cate: EVENT_CONFIG.AFFILIATE,
              session_key: sessionKey,
            },
            uId: userInfo?.id,
          });

          form.setFieldValue("gender", value[0]);
        }}
        onClose={() =>
          setState((draft) => {
            draft.genderPickerVisible = false;
          })
        }
      />

      {/* Notification Modal */}
      <ModalV2
        title="Thông báo"
        getContainer={document.body}
        content={
          <div className="text-center">
            {systemErrorMessages?.limitRequest || ZMA_ERROR["-203"]?.message}
          </div>
        }
        closeOnMaskClick={false}
        onClose={() => closeApp()}
        visible={isShowLimitRequestMessage}
        actions={[
          {
            key: "close",
            text: "Đóng",
            primary: true,
            onClick: () => closeApp(),
          },
        ]}
      />

      <ModalV2
        title="Điều khoản"
        getContainer={document.body}
        content={
          <div
            className="text-center"
            dangerouslySetInnerHTML={{
              __html:
                requestPermissionMessage ||
                APP_CONFIG.GAMES.AFFILIATE.REQUEST_PERMISSION_MSG,
            }}
          />
        }
        closeOnMaskClick={false}
        visible={isShowRequestPermissionMessage}
        actions={[
          {
            key: "start",
            text: "Bắt đầu",
            primary: true,
            onClick: () => {
              requestZaloPermissions();
            },
          },
        ]}
      />

      <ModalV2
        title="Thông báo"
        getContainer={document.body}
        content={
          <div
            className="text-center"
            dangerouslySetInnerHTML={{
              __html: gameValidation.affiliate.notAvailableMessage,
            }}
          />
        }
        onClose={() => closeApp()}
        visible={!gameValidation.affiliate.isValid && !isLoadingAppConfig}
        actions={[
          {
            key: "start",
            text: "Quà tặng của tôi",
            primary: true,
            onClick: () => onClickMyVoucher(),
          },
        ]}
      />
    </>
  );
});

Home.displayName = "Home";
