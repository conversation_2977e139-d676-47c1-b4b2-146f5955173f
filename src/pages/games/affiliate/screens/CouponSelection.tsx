// Libraries
import React, { useEffect, useRef, memo, useCallback, useMemo } from "react";
import { motion, useAnimation } from "motion/react";
import { useImmer } from "use-immer";
import { useRecoilState } from "recoil";
import * as Sentry from "@sentry/react";

// Styles
import styled from "styled-components";

// Assets
import Banner from "assets/images/others/affiliate-banner.webp";
import Coupon1 from "assets/images/others/coupon_1.webp";
import Coupon2 from "assets/images/others/coupon_2.webp";
import Coupon3 from "assets/images/others/coupon_3.webp";

// Components
import { Divider, Button as AmaButton } from "@antscorp/ama-ui";
import { But<PERSON>, InlineMessageBox, Swiper, SwiperSlide } from "components";

// Types
import { Swiper as ReactSwiper } from "swiper";

// States
import { affiliateState } from "../state";

// Constants
import { APP_CONFIG, EVENT_CONFIG, ROUTES } from "constant";

// Animations
import { buttonVariants, commonVariants } from "../variants";

// Queries
import { useAllocateVoucherAFF, useGetDealList } from "queries";
import { AllocateVoucher } from "schemas";

// Hooks
import {
  useAppConfig,
  useDeepCompareEffect,
  useNavigateWithSearch,
  useRegisterLoyaltyCustomer,
  useUserInfo,
  useViewPage,
} from "hooks";
import { useNavigate } from "react-router-dom";
import { AllocateVoucherAFFParams } from "services";
import { callCdpEvent, dayjs } from "utils";
import { SHA256 } from "crypto-js";
import { openWebview } from "zmp-sdk";

interface CouponSelectionProps {}

const CouponSelectionContainer = styled(motion.div)`
  padding-top: 1vh;
  color: var(--color-main-primary);
  display: flex;
  background-color: #fff8ef;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 1.2;
  gap: 7px;
  overflow: hidden;

  .zaui-btn {
    width: 165px;
    height: 41px;
    padding: 10px 20px;
    overflow: hidden;
    font-size: 13px;
    background: var(--color-main-primary);
    animation: none !important;
    border: 1px solid var(--color-main-primary) !important;
    color: #fff8ef;
    margin-top: 4vh;
    flex-shrink: 0;

    @media only screen and (max-height: 641px) {
      margin-bottom: 1vh;
    }
  }

  .adm-divider {
    border-color: var(--color-main-primary);
    width: 80%;
    padding: 0;
    margin: 0;
    margin-bottom: 5px;
  }

  .title {
    font-family: "Red Rose", cursive;
    font-size: 24px;
    font-weight: bold;
  }

  .sub-title {
    font-size: 14px;
    font-weight: bold;
  }

  .content {
    flex-grow: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    justify-content: center;
  }

  .footer {
    position: relative;
    width: 100%;
    flex-shrink: 0;
    margin-top: auto;
    margin-bottom: 23px;
    font-family: "Roboto", cursive;
    @media only screen and (max-height: 641px) {
      font-size: 11px;
    }
  }
  /* 
  img {
    width: 95%;
    height: auto;
  } */

  @media only screen and (max-width: 375px) {
    .title {
      font-size: 21px;
    }
    .sub-title {
      font-size: 11px;
    }
  }
`;

const CouponSelectionSwiper = styled(motion.div)`
  transition: opacity 0.5s ease-in-out;
  width: 100%;

  img {
    width: 100%;
    height: auto;
  }

  .swiper {
    overflow: visible;
    padding-top: 3vh;
  }

  .swiper-wrapper {
    .swiper-slide {
      width: 65%;

      .coupon {
        border-radius: 13px;
        position: relative;
        transition: all 0.5s ease-in-out;
        box-shadow: 1.3px 0px 6.51px 0px rgba(0, 0, 0, 0.1);
      }

      &.swiper-slide-active {
        z-index: 20;
        margin-bottom: 1vh;
        .coupon {
          transform: scale(1.1);
          opacity: 1;
        }
      }
    }
  }

  @media only screen and (max-height: 641px) {
    .swiper-wrapper {
      .swiper-slide {
        width: 160px;
      }
    }
  }

  @media only screen and (min-height: 641px) and (max-height: 668px) {
    .swiper-wrapper {
      .swiper-slide {
        width: 190px;
      }
    }
  }

  @media only screen and (min-height: 668px) and (max-height: 812px) {
    .swiper-wrapper {
      .swiper-slide {
        width: 200px;
      }
    }
  }
`;

type TState = {
  currentSlideIndex: number;
  allocatedVoucher?: AllocateVoucher;
  isOutOfVoucher?: boolean;
  consolationMessage: string;
  isAllocatingVoucher: boolean;
};

const initialState: TState = {
  currentSlideIndex: 0,
  allocatedVoucher: undefined,
  isOutOfVoucher: false,
  consolationMessage: "",
  isAllocatingVoucher: false,
};

export const CouponSelection: React.FC<CouponSelectionProps> = memo((props) => {
  const swiperRef = useRef<ReactSwiper | null>(null);
  const [_, setAffiliate] = useRecoilState(affiliateState);

  const navigate = useNavigateWithSearch();
  const { data: couponListData, isLoading: isLoadingCouponList } =
    useGetDealList();
  useViewPage({
    pageCate: EVENT_CONFIG.AFFILIATE,
    pageType: "choose_gift_box",
  });
  useRegisterLoyaltyCustomer();

  const { userInfo } = useUserInfo();
  const { appSettings } = useAppConfig();
  const {
    mutateAsync: allocateVoucher,
    isPending: isPendingAllocateVoucherAFF,
  } = useAllocateVoucherAFF({
    options: {
      onSuccess({ code, errorCode }) {
        // If the code is not 200, it means that the voucher is out of stock
        if (code !== 200) {
          setTimeout(() => {
            setState((draft) => {
              draft.isOutOfVoucher = true;
            });
          }, 1000);
        }
      },
      onError() {
        setState((draft) => {
          draft.isOutOfVoucher = true;
        });
      },
    },
  });

  // States
  const [state, setState] = useImmer<TState>(initialState);
  const {
    // allocatedVoucher,
    isAllocatingVoucher,
    isOutOfVoucher,
    currentSlideIndex,
    consolationMessage,
  } = state;

  // Variables
  const { systemErrorMessages } = appSettings?.globals || {};
  const { banners } = appSettings?.games?.affiliate || {};

  // Effects
  useEffect(() => {
    return () => {
      setState(initialState);
    };
  }, [setState]);

  // Memos
  const renderCouponList = useMemo(() => {
    if (isLoadingCouponList || !couponListData) return [];
    const dealList = couponListData?.data?.deals || [];

    if (dealList.length <= 6 && dealList.length > 1) {
      return Array(3)
        .fill(null)
        .flatMap(() => dealList);
    }

    return dealList || [];
  }, [isLoadingCouponList, couponListData]);

  // Handlers
  const onSlideChangeTransitionStart = (swiper: ReactSwiper) => {
    setState((draft) => {
      draft.currentSlideIndex = swiper.activeIndex;
    });
  };

  const onClickTakeCoupon = useCallback(async () => {
    if (isAllocatingVoucher) return;

    if (swiperRef.current) {
      swiperRef.current.allowTouchMove = false;
    }

    setState((draft) => {
      draft.isAllocatingVoucher = true;
    });

    const selectedDeal = renderCouponList[currentSlideIndex];

    const { phoneNumber, name, idByOA } = userInfo || {};

    const requestBody: AllocateVoucherAFFParams = {
      phoneNumber: `${phoneNumber || APP_CONFIG.TEST_PHONE}`,
      name: `${name || ""}`,
      idByOa: `${idByOA || ""}`,
      scheme_code: selectedDeal?.scheme || "",
    };

    const { data: giftSubmitData } = (await allocateVoucher(requestBody)) || {};

    if (giftSubmitData) {
      // Call global tracking
      const { globalTracking, promotion_code } =
        giftSubmitData?.webContents?.contents || {};

      // Call Global Tracking Event
      fetch(globalTracking?.impression);
      fetch(globalTracking?.view);

      callCdpEvent({
        data: {
          page_type: "gift_code",
          page_cate: EVENT_CONFIG.AFFILIATE,
        },
        dims: {
          promotion_code: {
            id: promotion_code,
          },
        },
        uId: userInfo?.id,
      });

      setAffiliate((prev) => ({
        ...prev,
        currentScreen: "results",
        allocatedVoucher: giftSubmitData,
      }));
    }

    // Call sentry log
    Sentry.captureMessage(`[AFFILIATE] Take deal`, {
      level: "info",
      extra: {
        request: JSON.stringify(requestBody),
        response: JSON.stringify(giftSubmitData),
      },
    });

    setState((draft) => {
      draft.isAllocatingVoucher = false;
    });
  }, [
    isAllocatingVoucher,
    userInfo,
    setState,
    allocateVoucher,
    setAffiliate,
    renderCouponList,
    currentSlideIndex,
  ]);

  const onClickRedirectVoucherList = useCallback(async () => {
    navigate(ROUTES.GIFT.path, {
      newParams: {
        tab: "redeemed",
        voucherType: "voucher",
      },
    });
  }, [navigate]);

  const onClickBanner = useCallback((banner) => {
    // Handle banner click
    if (banner?.link) {
      openWebview({
        url: banner.link,
        config: {
          style: "bottomSheet",
          leftButton: "back",
        },
      });
    }
  }, []);

  // Animations
  const controlTakeDealButton = useAnimation();
  useEffect(() => {
    const animateSequence = async () => {
      await controlTakeDealButton.start({
        ...commonVariants.zoomIn,
        transition: {
          duration: 0.5,
          delay: renderCouponList.length > 1 ? 2 : 1.3,
        },
      });
      await controlTakeDealButton.start("zoomPulse");
    };

    animateSequence();
  }, [controlTakeDealButton, renderCouponList]);

  // Renders
  const renderContent = () => {
    const voucherButton = (
      <AmaButton
        color="primary"
        block
        shape="rounded"
        onClick={onClickRedirectVoucherList}
      >
        Quà tặng của tôi
      </AmaButton>
    );

    if (isOutOfVoucher) {
      return (
        <InlineMessageBox
          message={`${
            systemErrorMessages?.outOfCode ||
            APP_CONFIG.SYSTEM_ERROR_MESSAGES.outOfCode
          }`}
        >
          {voucherButton}
        </InlineMessageBox>
      );
    }

    return (
      <motion.div className="content">
        {renderCouponList.length > 0 && (
          <CouponSelectionSwiper
            animate={renderCouponList.length > 1 ? "slideAndReturn" : "zoomIn"}
            transition={{
              duration: renderCouponList.length > 1 ? 1.5 : 0.7,
              times: [0, 0.55, 0.83, 0.85, 1],
              ease: [0.4, 0, 0.2, 1],
              delay: 0.5,
            }}
            variants={commonVariants}
          >
            <Swiper
              spaceBetween={30}
              slidesPerView={"auto"}
              centeredSlides
              loop={renderCouponList.length > 1 ? true : false}
              speed={1200}
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
              }}
              onSlideChange={(swiper) => {
                setState((draft) => {
                  draft.currentSlideIndex = swiper.realIndex;
                });
              }}
            >
              {renderCouponList.map((coupon, index) => (
                <motion.div key={index} className="relative">
                  <SwiperSlide key={index}>
                    <motion.img
                      src={coupon.image ? coupon.image : Coupon2}
                      alt="coupon"
                      className="coupon"
                    />
                  </SwiperSlide>
                </motion.div>
              ))}
            </Swiper>
          </CouponSelectionSwiper>
        )}
        <motion.div
          initial="hidden"
          variants={buttonVariants}
          animate={controlTakeDealButton}
          onTapStart={() => {
            controlTakeDealButton.start("tap");
          }}
          onTapCancel={() => {
            controlTakeDealButton.start("zoomPulse");
          }}
        >
          <Button
            fullWidth
            onClick={onClickTakeCoupon}
            loading={isPendingAllocateVoucherAFF}
          >
            CHỐT DEAL
          </Button>
        </motion.div>
      </motion.div>
    );
  };

  return (
    <CouponSelectionContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* <motion.img
        src={Banner}
        animate="zoomIn"
        variants={commonVariants}
        transition={{ duration: 0.4 }}
      /> */}
      <motion.div
        animate="zoomIn"
        variants={commonVariants}
        className="relative w-full flex justify-center overflow-hidden"
        transition={{ duration: 0.4, delay: 0.5 }}
      >
        <Swiper
          className="w-[90%] rounded-[18px]"
          loop
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          speed={1000}
        >
          {banners?.map((banner, index) => {
            return (
              <SwiperSlide key={index} onClick={() => onClickBanner(banner)}>
                <motion.img
                  src={banner?.image || Banner}
                  alt="banner"
                  className="w-full h-auto aspect-[3.1] object-cover object-center"
                />
              </SwiperSlide>
            );
          })}
        </Swiper>
      </motion.div>
      <Divider />
      <motion.div
        className="title"
        animate="slideFromLeft"
        variants={commonVariants}
        transition={{ duration: 0.6, delay: 0.4, times: [0, 0.6, 1] }}
      >
        DEAL XỊN Ê HỀ Ê HỀ
      </motion.div>
      <motion.div
        className="sub-title"
        animate="slideFromRight"
        variants={commonVariants}
        transition={{ duration: 0.6, delay: 0.4, times: [0, 0.6, 1] }}
      >
        QUẸT TRÁI HAY QUẸT PHẢI
      </motion.div>

      {renderContent()}
      <div className="footer">Highlands Coffee CopyRight ©</div>
    </CouponSelectionContainer>
  );
});

CouponSelection.displayName = "CouponSelection";
