// Libraries
import React, { memo, useCallback } from "react";
import { motion } from "motion/react";
import { useRecoilState, useRecoilValue } from "recoil";

// Styles
import styled from "styled-components";

// Components
import { Button } from "components";
import { AllocatedVoucher } from "../components";

// States
import { affiliateState } from "../state";

// Hooks
import { useNavigateWithSearch } from "hooks";

// Animations
import { commonVariants } from "../variants";

interface ResultProps {}

const ResultContainer = styled(motion.div)`
  padding-top: 4vh;
  color: var(--color-main-primary);
  display: flex;
  background-color: #fff8ef;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 1.2;
  gap: 7px;
  overflow: hidden;

  .zaui-btn {
    width: 180px;
    height: 41px;
    padding: 10px 20px;
    overflow: hidden;
    font-size: 13px;
    background: var(--color-main-primary);
    animation: none !important;
    border: 1px solid var(--color-main-primary) !important;
    color: #fff8ef;
    margin-top: 15px;

    flex-shrink: 0;

    @media only screen and (max-height: 641px) {
      margin-bottom: 1vh;
    }
  }

  .title {
    font-size: 24px;
    font-weight: bold;
  }

  .term-and-condition {
    color: #9d6e26;
    font-size: 14px;
    font-family: "Roboto", cursive;
    font-weight: 500;
    text-decoration: underline;
    margin-top: 5px;
  }

  .content {
    padding-top: 40px;
    flex-grow: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    justify-content: center;
  }

  .content-2 {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;
    position: relative;
    width: 100%;
    flex-shrink: 0;
    margin-top: auto;
    height: 22vh;
  }

  .footer {
    font-family: "Roboto", cursive;
    font-size: 12px;
    margin-bottom: 25px;
  }

  @media only screen and (max-height: 641px) {
    padding-top: 1.5vh;
    .footer {
      font-size: 11px;
      margin-bottom: 13px;
    }

    .term-and-condition {
      font-size: 13px;
    }

    .zaui-btn {
      width: 165px;
      height: 40px;
      font-size: 12px;
    }
  }

  @media only screen and (min-height: 641px) and (max-height: 668px) {
    padding-top: 2vh;
  }

  @media only screen and (max-width: 375px) {
    .title {
      font-size: 21px;
    }
    .footer {
      font-size: 11px;
    }
  }
`;

type TState = {};

export const Result: React.FC<ResultProps> = memo((props) => {
  const [_, setAffiliate] = useRecoilState(affiliateState);
  const navigate = useNavigateWithSearch();

  const { allocatedVoucher } = useRecoilValue(affiliateState) || {};
  const { id = "" } = allocatedVoucher?.webContents.contents || {};

  // Handlers
  const onClickMyVoucher = useCallback(() => {
    navigate("/gift", {
      newParams: {
        tab: "redeemed",
        voucherType: "voucher",
      },
    });
  }, [navigate]);

  const onClickTermAndCondition = useCallback(() => {
    navigate(`/vouchers/${id}`, {
      newParams: {
        isOpenTAC: "true",
      },
    });
  }, [navigate, id]);

  return (
    <ResultContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="title"
        animate="zoomIn"
        variants={commonVariants}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        VOUCHER CỦA BẠN
      </motion.div>
      <div className="content">
        <AllocatedVoucher />
      </div>
      <div className="content-2">
        <motion.div
          style={{ marginBottom: "auto" }}
          animate="fadeIn"
          variants={commonVariants}
          transition={{ duration: 0.7, ease: "easeOut", delay: 1.5 }}
        >
          <div className="term-and-condition" onClick={onClickTermAndCondition}>
            Điều khoản sử dụng
          </div>

          <Button fullWidth onClick={onClickMyVoucher}>
            QUÀ TẶNG CỦA TÔI
          </Button>
        </motion.div>

        <span className="footer">Highlands Coffee CopyRight ©</span>
      </div>
    </ResultContainer>
  );
});

Result.displayName = "Result";
