// Components
import { atom } from "recoil";

// Schemas
import { AllocateVoucher } from "schemas";


type Screen = "home" | 'coupon-selection' | 'results';

interface AffiliateState {
    currentScreen: Screen
    allocatedVoucher: AllocateVoucher | null
}

export const affiliateStateDefault: AffiliateState = {
    currentScreen: 'home',
    allocatedVoucher: null
}

export const affiliateState = atom({
    key: "affiliateState",
    default: affiliateStateDefault
})