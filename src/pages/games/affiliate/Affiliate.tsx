// Libraries
import React, { useEffect } from "react";
import { useRecoilState } from "recoil";
import { AnimatePresence } from "motion/react";
import { useImmer } from "use-immer";

// State
import { affiliateState, affiliateStateDefault } from "./state";

// Styles
import styled from "styled-components";

// Screens
import { CouponSelection, Home, Result } from "./screens";

interface AffiliateProps {}

const AffiliateWrapper = styled.div`
  padding-top: var(--zaui-safe-area-inset-top, 0px);
  width: 100%;
  height: 100%;
  background-color: #fff8ef;
  font-family: "Red Rose", cursive;
`;

export const Affiliate: React.FC<AffiliateProps> = () => {
  // Hooks
  const [affiliate, setAffiliate] = useRecoilState(affiliateState);

  // State
  const [state, setState] = useImmer({
    isShowCouponSelection: false,
    isShowResult: false,
  });

  // Variables
  const { currentScreen } = affiliate || {};
  const { isShowCouponSelection, isShowResult } = state;

  useEffect(() => {
    return () => {
      setAffiliate(affiliateStateDefault);
    };
  }, [setAffiliate]);

  return (
    <AffiliateWrapper id="affiliate" data-test="affiliate">
      <AnimatePresence
        onExitComplete={() =>
          setState((draft) => {
            draft.isShowCouponSelection = true;
          })
        }
      >
        {currentScreen === "home" && <Home />}
      </AnimatePresence>

      <AnimatePresence
        onExitComplete={() =>
          setState((draft) => {
            draft.isShowResult = true;
          })
        }
      >
        {currentScreen === "coupon-selection" && isShowCouponSelection && (
          <CouponSelection />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {currentScreen === "results" && isShowResult && <Result />}
      </AnimatePresence>

      {/* <Home /> */}

      {/* <CouponSelection /> */}

      {/* <Result /> */}
    </AffiliateWrapper>
  );
};
