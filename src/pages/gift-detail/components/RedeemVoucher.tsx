// Libraries
import { <PERSON><PERSON>, <PERSON> } from "@antscorp/ama-ui";
import { ConditionalRenderer } from "components";
import React, { memo, useCallback } from "react";
import styled from "styled-components";
import { generatePath, useNavigate } from "react-router-dom";
import clsx from "clsx";

// Images
import redeemVoucherSuccess from "assets/images/others/redeem-voucher-success.webp";

// Constants
import { ROUTES } from "constant";

// Schemas
import { AllocateVoucher } from "schemas";
import { useViewPage } from "hooks";

interface RedeemVoucherProps {
  allocatedVoucher?: AllocateVoucher;
}

const RedeemVoucherWrapper = styled.div`
  /* --adm-card-padding-inline: 20px;
  --adm-card-body-padding-block: 20px; */
`;

export const RedeemVoucher: React.FC<RedeemVoucherProps> = memo((props) => {
  const { allocatedVoucher } = props;
  const navigate = useNavigate();
  useViewPage({
    pageType: "redeem_point_success",
  });

  // Variables
  const { id: voucherId, image_url } =
    allocatedVoucher?.webContents?.contents || {};

  // Handlers
  const onClickUseVoucher = useCallback(() => {
    navigate(
      generatePath(ROUTES["VOUCHER-DETAIL"].path, {
        voucherId,
      })
    );
  }, [navigate, voucherId]);

  return (
    <ConditionalRenderer>
      <RedeemVoucherWrapper className="flex flex-col gap-5">
        <Card bodyClassName="flex flex-col items-center gap-5">
          <img
            src={image_url || redeemVoucherSuccess}
            className={clsx("w-full aspect-[1.5] rounded-[10px]", {
              "object-contain object-center": !image_url,
            })}
            alt="Redeem Voucher Success"
          />
          <div className="font-redRose font-bold text-xl text-main-primary">
            Đổi voucher thành công
          </div>
        </Card>
        <div className="flex flex-col gap-2.5">
          <Button shape="rounded" color="primary" onClick={onClickUseVoucher}>
            Dùng voucher ngay
          </Button>
          <Button
            shape="rounded"
            onClick={() => {
              navigate(ROUTES["GIFT"].path);
            }}
          >
            Đổi điểm tiếp
          </Button>
        </div>
      </RedeemVoucherWrapper>
    </ConditionalRenderer>
  );
});

RedeemVoucher.displayName = "RedeemVoucher";
