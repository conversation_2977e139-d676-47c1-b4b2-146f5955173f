// Libraries
import {
  <PERSON><PERSON>,
  Card,
  Ellipsis,
  Image,
  Modal,
  Space,
  Toast,
} from "@antscorp/ama-ui";
import dayjs from "dayjs";
import { motion } from "motion/react";
import React, { useCallback, useEffect, useMemo } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import styled from "styled-components";
import { useImmer } from "use-immer";
import { useToggle } from "usehooks-ts";

// Constants
import { API_ERROR, DATE_TIME_FORMAT } from "constant";

// Components
import {
  ConditionalRenderer,
  DripsTag,
  MemberRegistration,
  TermAndConditionSheet,
} from "components";
import { RedeemVoucher } from "./components";

// Queries
import { useGetSchemeDetail, useRedeemScheme } from "queries";
import { useSyncCustomer, useUserInfo, useViewPage } from "hooks";
import { AllocateVoucher } from "schemas";
import { callCdpEvent } from "utils";
import { InfoIcon } from "lucide-react";

interface GiftDetailProps {}

const GiftDetailWrapper = styled(motion.div)`
  padding: var(--layout-content-padding);
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;

  .gift-card {
    max-height: 100%;
    overflow: auto;

    &__image {
      width: 100%;
      aspect-ratio: 1.573;
      border-radius: 10px;
      flex-shrink: 0;
      height: auto !important;
    }
  }
`;

type TState = {
  /** Allocate voucher */
  allocateVoucher?: AllocateVoucher;
};

const FAIL_MESSAGE = "Đổi Voucher thất bại, <br /> vui lòng thử lại bạn nhá!";

export const GiftDetail: React.FC<GiftDetailProps> = () => {
  const params = useParams<{ giftId: string }>();
  const [searchPrams] = useSearchParams();
  const { isRegistered, userInfo, loyaltyCustomer } = useUserInfo();
  useSyncCustomer();
  useViewPage({
    pageType: "detail_scheme",
  });

  // State
  const [showTAndC, toggleShowTAndC] = useToggle(false);
  const [state, setState] = useImmer<TState>({
    allocateVoucher: undefined,
  });

  // Variables
  const { allocateVoucher } = state;
  const { giftId } = params || {};
  const search = searchPrams.get("search") || "";

  const handleRedeemErrors = (statusCode: number) => {
    const message = API_ERROR[statusCode]?.message || FAIL_MESSAGE;

    Modal.alert({
      header: <InfoIcon size={46} className="text-error" />,
      content: (
        <div
          className="text-center mt-5"
          dangerouslySetInnerHTML={{ __html: message }}
        />
      ),
      confirmText: "Đóng",
    });
  };

  // Queries
  const { data: schemeDetailData, isLoading: isSchemeDetailLoading } =
    useGetSchemeDetail({
      args: {
        id: giftId || "",
      },
      options: {
        enabled: !!giftId,
      },
    });
  const { mutateAsync: redeemScheme, isPending: isRedeemPending } =
    useRedeemScheme({
      options: {
        onSettled(data) {
          if (data?.data && data.data.webContents) {
            const { promotion_code } = data.data.webContents?.contents || {};

            setState((draft) => {
              draft.allocateVoucher = data.data;
            });

            callCdpEvent({
              ea: "redeem_point",
              ec: "point",
              uId: userInfo?.id,
              dims: {
                scheme_marketing: {
                  id: giftId,
                  scheme_code: schemeDetailData?.data?.scheme_code || "",
                },
                promotion_code: {
                  id: promotion_code,
                },
              },
              data: {
                point_used: schemeDetailData?.data?.point_redeem || 0,
                customer_phone: userInfo?.phoneNumber,
              },
            });
          } else {
            callCdpEvent({
              uId: userInfo?.id,
              ea: "redeem_point_fail",
              ec: "point",
              dims: {
                scheme_marketing: {
                  id: giftId,
                  scheme_code: schemeDetailData?.data?.scheme_code || "",
                },
              },
              data: {
                point_used: schemeDetailData?.data?.point_redeem || 0,
                customer_phone: userInfo?.phoneNumber,
                reason_fail: data?.data || data?.message || "Unknown error",
                reason_fail_code: data?.errorCode || data?.code || 0,
              },
            });

            handleRedeemErrors(data?.errorCode || 0);
          }
        },
      },
    });

  // Effects
  useEffect(() => {
    if (userInfo?.id && giftId) {
      setTimeout(() => {
        callCdpEvent({
          uId: userInfo.id,
          ea: "view",
          ec: "scheme_marketing",
          dims: {
            scheme_marketing: {
              id: giftId,
              //TODO: Add scheme code
              scheme_code: schemeDetailData?.data?.scheme_code || "",
            },
          },
          data: {
            customer_phone: userInfo?.phoneNumber,
            src_search_term: search,
          },
        });
      }, 500);
    }
  }, [
    userInfo.id,
    giftId,
    userInfo?.phoneNumber,
    search,
    schemeDetailData?.data?.scheme_code,
  ]);

  // Memo
  const schemeDetail = useMemo(() => {
    return schemeDetailData?.data;
  }, [schemeDetailData]);

  // const isCanRedeem = useMemo(() => {
  //   if (!loyaltyCustomer?.availablePoints) return false;

  //   return (
  //     Number(loyaltyCustomer.availablePoints) >=
  //     Number(schemeDetail?.point_redeem)
  //   );
  // }, [loyaltyCustomer?.availablePoints, schemeDetail?.point_redeem]);

  const handleRedeemVoucher = useCallback(async () => {
    Modal.show({
      // header: <SuccessIcon size={46} className="text-success" />,
      content: (
        <div className="text-center mt-5">
          Để hoàn tất, bạn vui lòng bấm <strong>{`"Xác nhận"`}</strong> để đổi
          điểm nhé!
        </div>
      ),
      closeOnMaskClick: true,
      closeOnAction: true,
      actions: [
        {
          key: "confirm",
          text: "Xác nhận",
          primary: true,
          onClick: async () => {
            if (userInfo?.phoneNumber && giftId) {
              const { data } = await redeemScheme({
                phoneNumber: userInfo.phoneNumber,
                schemeId: giftId,
              });

              if (data?.webContents) {
                const { globalTracking } = data.webContents.contents || {};

                // Call Global Tracking Event
                if (globalTracking) {
                  fetch(globalTracking.impression);
                  fetch(globalTracking.view);
                }
              }
            }
          },
        },
        {
          key: "cancel",
          primary: false,
          className: "adm-button-default !border",
          text: "Để sau",
        },
      ],
    });
  }, [userInfo.phoneNumber, giftId, redeemScheme]);

  // Renders
  const renderGiftDetail = useCallback(() => {
    const { image, name, point_redeem, partner, expire_date, description } =
      schemeDetail || {};

    if (allocateVoucher) {
      return <RedeemVoucher allocatedVoucher={allocateVoucher} />;
    }

    return (
      <MemberRegistration>
        <Card
          className="gift-card"
          bodyClassName="flex flex-col gap-2.5 h-full"
        >
          <Image className="gift-card__image" src={image} alt="" />
          <div>
            <span className="text-text-secondary font-medium">{partner}</span>
            <Ellipsis
              className="text-base font-medium"
              rows={2}
              content={name || ""}
            />
          </div>
          <Space align="center" justify="between">
            <Space align="center" style={{ "--gap": "2px" }}>
              <span className="text-[10px] xs:!text-sm text-tertiary">
                Hạn sử dụng:
              </span>
              <span className="text-[11px] xs:!text-[13px]">
                {dayjs(expire_date || "").format(
                  DATE_TIME_FORMAT.VOUCHER_DATE_FORMAT
                )}
              </span>
            </Space>

            <DripsTag drips={Number(point_redeem) || 0} />
          </Space>
          <p
            className="h-full overflow-auto"
            dangerouslySetInnerHTML={{
              __html: `${description || ""}`,
            }}
          />
        </Card>
        <div className="flex flex-col gap-2.5 w-full">
          <Button
            color="primary"
            block
            shape="rounded"
            // loading={isRedeemPending}
            onClick={handleRedeemVoucher}
          >
            Đổi voucher ngay
          </Button>
          <Button block shape="rounded" onClick={toggleShowTAndC}>
            Điều kiện sử dụng
          </Button>
        </div>
      </MemberRegistration>
    );
  }, [
    schemeDetail,
    allocateVoucher,
    // isRedeemPending,
    handleRedeemVoucher,
    toggleShowTAndC,
  ]);

  return (
    <ConditionalRenderer
      isLoading={isSchemeDetailLoading}
      isEmpty={!schemeDetail}
    >
      <GiftDetailWrapper initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
        {renderGiftDetail()}

        <TermAndConditionSheet
          title="Điều kiện sử dụng"
          visible={showTAndC}
          unmountOnClose={true}
          onClose={toggleShowTAndC}
        >
          <div
            dangerouslySetInnerHTML={{
              __html: schemeDetail?.terms_condition || "",
            }}
          />
        </TermAndConditionSheet>
      </GiftDetailWrapper>
    </ConditionalRenderer>
  );
};
