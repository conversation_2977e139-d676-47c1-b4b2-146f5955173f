import React from 'react';

interface RestaurantInfo {
  name: string;
  address: string;
  phone: string;
  customerId: string;
  logo: string;
}

interface RestaurantInfoCardProps {
  restaurant: RestaurantInfo;
}

export const RestaurantInfoCard: React.FC<RestaurantInfoCardProps> = ({ restaurant }) => {
  return (
    <div className="m-2 mb-0 bg-white">
      <div className="flex items-start gap-2 p-3">
        <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-text-primary text-sm font-bold text-white">
          {restaurant.logo.charAt(0)}
        </div>

        <div className="flex flex-1 flex-col gap-1">
          <div className="text-[15px] font-medium leading-[22px] text-text-primary">
            {restaurant.name}
          </div>

          <div className="text-xs font-normal leading-[18px] text-text-secondary">
            {restaurant.address}
          </div>

          <div className="flex items-center gap-1">
            <span className="text-xs font-normal leading-[18px] text-text-secondary">Hotline:</span>

            <span className="cursor-pointer font-medium leading-[18px] text-blue-300">
              {restaurant.phone}
            </span>
          </div>

          <div className="text-left text-xs font-normal leading-[18px] text-text-secondary">
            Mã KH: {restaurant.customerId}
          </div>
        </div>
      </div>
    </div>
  );
};
