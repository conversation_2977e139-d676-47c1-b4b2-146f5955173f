import React from 'react';

interface PaymentInfo {
  method: string;
}

interface PaymentInfoCardProps {
  payment: PaymentInfo;
}

export const PaymentInfoCard: React.FC<PaymentInfoCardProps> = ({ payment }) => {
  return (
    <div className="flex flex-col gap-4 bg-white p-4">
      <div className="text-[17px] font-medium leading-[26px] text-text-primary"><PERSON><PERSON> toán</div>

      <div className="flex w-full items-center justify-between">
        <div className="flex-1 text-[15px] font-normal leading-[22px] text-text-secondary">
          <PERSON><PERSON><PERSON> thức thanh toán
        </div>

        <div className="flex-shrink-0 text-right text-[15px] font-medium leading-[22px] text-text-primary">
          {payment.method}
        </div>
      </div>
    </div>
  );
};
