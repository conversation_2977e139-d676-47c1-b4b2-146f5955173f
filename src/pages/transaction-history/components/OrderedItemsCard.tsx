import React, { useState, useMemo } from 'react';
import { ChevronDown } from 'lucide-react';
import clsx from 'clsx';

interface OrderedItemsCardProps {
  items: string[];
}

export const OrderedItemsCard: React.FC<OrderedItemsCardProps> = ({ items }) => {
  const [showAllItems, setShowAllItems] = useState(false);

  const displayedItems = useMemo(() => {
    if (showAllItems) {
      return items;
    }
    return items.slice(0, 3);
  }, [items, showAllItems]);

  const hasMoreItems = items.length > 3;

  return (
    <div className="flex flex-col gap-4 bg-white p-4">
      <div className="text-black-300 text-[17px] font-medium leading-[26px]">Món đã gọi</div>

      <div className="flex flex-col gap-3">
        {displayedItems.map((item, index) => (
          <div key={index} className="flex w-full items-stretch">
            <div className="flex-1 font-normal leading-[22px] text-secondary">{item}</div>
          </div>
        ))}
      </div>

      {hasMoreItems && (
        <div
          className="flex cursor-pointer items-center justify-center gap-[6px] text-blue-300"
          onClick={() => setShowAllItems(!showAllItems)}
        >
          <div className="text-center font-medium leading-[22px]">
            {showAllItems ? 'Thu gọn' : 'Xem thêm'}
          </div>

          <div className="flex h-4 w-4 items-center justify-center">
            <ChevronDown
              size={16}
              className={clsx({
                'rotate-180': showAllItems,
              })}
            />
          </div>
        </div>
      )}
    </div>
  );
};
