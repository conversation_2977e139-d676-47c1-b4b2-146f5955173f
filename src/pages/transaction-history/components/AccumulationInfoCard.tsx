import React from 'react';

interface AccumulationInfo {
  gCoin: number;
  rankingPoints: number;
}

interface AccumulationInfoCardProps {
  accumulation: AccumulationInfo;
}

const formatCurrency = (amount: number): string => {
  return `${amount.toLocaleString('vi-VN')} vnđ`;
};

const formatGCoin = (amount: number): string => {
  return `+${amount.toLocaleString('vi-VN')}`;
};

export const AccumulationInfoCard: React.FC<AccumulationInfoCardProps> = ({ accumulation }) => {
  return (
    <div className="flex flex-col gap-4 bg-white p-4">
      <div className="text-[17px] font-medium leading-[26px] text-text-primary">Tích lũy</div>

      <div className="flex flex-col gap-3">
        <div className="flex w-full items-center justify-between">
          <div className="flex-1 text-[15px] font-normal leading-[22px] text-text-secondary">
            Tích lũy G-coin
          </div>
          <div className="flex-shrink-0 text-right text-[17px] font-medium leading-[26px] text-success">
            {formatGCoin(accumulation.gCoin)}
          </div>
        </div>

        <div className="flex w-full items-center justify-between">
          <div className="flex-1 text-[15px] font-normal leading-[22px] text-text-secondary">
            Tích lũy thăng hạng
          </div>
          <div className="flex-shrink-0 text-right text-[17px] font-medium leading-[26px] text-text-primary">
            {formatCurrency(accumulation.rankingPoints)}
          </div>
        </div>
      </div>
    </div>
  );
};
