import React from 'react';

interface TransactionDetails {
  transactionId: string;
  date: string;
}

interface FinancialDetails {
  totalAmount: number;
  serviceFee: number;
  discount: number;
  subtotal: number;
  vat: number;
  totalAfterVat: number;
  voucher: number;
  finalAmount: number;
}

interface TransactionDetailsCardProps {
  transaction: TransactionDetails;
  financial: FinancialDetails;
}

const formatCurrency = (amount: number): string => {
  return `${amount.toLocaleString('vi-VN')} vnđ`;
};

const DetailRow: React.FC<{
  label: string;
  value: string;
  isBold?: boolean;
  isOrange?: boolean;
  isHighlight?: boolean;
}> = ({ label, value, isBold = false, isOrange = false, isHighlight = false }) => {
  const labelClasses =
    isBold || isHighlight
      ? 'font-medium leading-[22px] text-text-primary flex-1'
      : 'font-normal leading-[22px] text-text-secondary flex-1';

  const valueClasses = (() => {
    if (isHighlight) {
      return 'font-medium text-[17px] leading-[26px] text-text-primary text-right flex-shrink-0';
    }
    if (isOrange) {
      return 'font-medium text-[17px] leading-[26px] text-[#f5832f] text-right flex-shrink-0';
    }
    if (isBold) {
      return 'font-medium text-[15px] leading-[22px] text-text-primary text-right flex-shrink-0';
    }
    return 'font-medium text-[15px] leading-[22px] text-text-primary text-right flex-shrink-0';
  })();

  return (
    <div className="flex w-full items-center justify-between">
      <div className={labelClasses}>{label}</div>
      <div className={valueClasses}>{value}</div>
    </div>
  );
};

export const TransactionDetailsCard: React.FC<TransactionDetailsCardProps> = ({
  transaction,
  financial,
}) => {
  return (
    <div className="flex flex-col gap-4 bg-white p-4">
      <div className="w-full text-[17px] font-medium leading-[26px] text-text-primary">
        Chi tiết
      </div>

      <div className="flex flex-col gap-3">
        <DetailRow label="Mã đơn hàng" value={transaction.transactionId} />

        <DetailRow label="Thời gian" value={transaction.date} />
      </div>

      <div className="h-px w-full bg-[#e9ebed]" />

      <div className="flex flex-col gap-3">
        <DetailRow
          label="Tổng tiền hàng"
          value={formatCurrency(financial.totalAmount)}
          isHighlight
        />

        <DetailRow label="Phí dịch vụ" value={formatCurrency(financial.serviceFee)} />

        <DetailRow label="Giảm giá" value={formatCurrency(financial.discount)} />

        <DetailRow label="Thành tiền" value={formatCurrency(financial.subtotal)} />

        <DetailRow label="VAT (10%)" value={formatCurrency(financial.vat)} />

        <DetailRow label="Tổng tiền sau VAT" value={formatCurrency(financial.totalAfterVat)} />

        <DetailRow label="Voucher" value={formatCurrency(financial.voucher)} />

        <DetailRow
          label="Tổng tiền thanh toán"
          value={formatCurrency(financial.finalAmount)}
          isHighlight
          isOrange
        />
      </div>
    </div>
  );
};
