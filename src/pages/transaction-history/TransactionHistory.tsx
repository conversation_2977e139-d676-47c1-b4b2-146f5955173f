import React from 'react';
import { useParams } from 'react-router-dom';
import {
  RestaurantInfoCard,
  TransactionDetailsCard,
  OrderedItemsCard,
  PaymentInfoCard,
  AccumulationInfoCard,
} from './components';

// Mock data based on Figma design
const mockTransactionDetail = {
  transactionId: '4297181',
  date: '28/10/2021 - 19:20:21',
  restaurant: {
    name: '<PERSON><PERSON>',
    address: 'Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng Mai, Hà Nội',
    phone: '0909090909',
    customerId: '29682242',
    logo: '<PERSON>gi', // This would be the logo identifier
  },
  financial: {
    totalAmount: 800000,
    serviceFee: 0,
    discount: 0,
    subtotal: 800000,
    vat: 80000,
    totalAfterVat: 880000,
    voucher: 0,
    finalAmount: 880000,
  },
  items: [
    '1x Sườn non bò <PERSON> hả<PERSON> hạng',
    '1x Đùi dê ướ<PERSON> số<PERSON>',
    '1x Bạch tuộc baby',
    '1x Tôm cân đẩu vân',
    '1x Set menu An Khang',
  ],
  payment: {
    method: 'Cá Nhân',
  },
  accumulation: {
    gCoin: 68805,
    rankingPoints: 880000,
  },
};

export const TransactionHistory: React.FC<{}> = () => {
  const { transactionId } = useParams<{ transactionId: string }>();

  // TODO: Use transactionId to fetch actual transaction data from API
  console.log('Transaction ID:', transactionId);

  return (
    <div className="flex flex-col gap-[10px] overflow-y-auto bg-[#e9ebed]">
      {/* Restaurant Info Card */}
      <RestaurantInfoCard restaurant={mockTransactionDetail.restaurant} />

      {/* Transaction Details Card */}
      <TransactionDetailsCard
        transaction={{
          transactionId: mockTransactionDetail.transactionId,
          date: mockTransactionDetail.date,
        }}
        financial={mockTransactionDetail.financial}
      />

      {/* Ordered Items Card */}
      <OrderedItemsCard items={mockTransactionDetail.items} />

      {/* Payment Info Card */}
      <PaymentInfoCard payment={mockTransactionDetail.payment} />

      {/* Accumulation Info Card */}
      <AccumulationInfoCard accumulation={mockTransactionDetail.accumulation} />
    </div>
  );
};
