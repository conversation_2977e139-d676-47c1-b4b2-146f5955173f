import React from 'react';
import { List } from '@antscorp/ama-ui';
import { useUserName } from 'hooks/useUserName';

export const AccountInfo: React.FC = () => {
  const { firstName, lastName } = useUserName({ nationality: 'VN' });

  const accountInfoItems = [
    { label: 'Họ', value: lastName },
    { label: 'Tên', value: firstName },
    { label: 'Số điện thoại', value: '**********' },
    { label: 'Mã khách hàng', value: '********' },
    { label: 'Giới tính', value: 'Nam' },
    { label: 'Ng<PERSON><PERSON> sinh', value: '13/04/1993' },
    { label: 'Email', value: '<EMAIL>' },
    { label: 'Địa chỉ', value: 'Chung cư Osaka, Hoàng Liệt, Hoàng Mai, Hà Nội' },
    { label: 'CMND/CCCD', value: '************' },
  ];

  return (
    <List className="p-4">
      {accountInfoItems.map((item, index) => (
        <List.Item
          key={index}
          title={
            <div className="flex items-center justify-between">
              <span className="min-w-24 text-[15px] font-normal">{item.label}</span>

              <span className="text-[15px] font-medium text-black">{item.value}</span>
            </div>
          }
        />
      ))}
    </List>
  );
};
