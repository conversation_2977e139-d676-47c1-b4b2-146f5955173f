import { Image } from '@antscorp/ama-ui';

export const BrandMenus = () => {
  const menuItems = [
    {
      id: '1',
      name: 'Combo Lẩu <PERSON>ề<PERSON>ống',
      image:
        'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=164&h=164&fit=crop&auto=format',
    },
    {
      id: '2',
      name: 'Combo Lẩu <PERSON> Truyền Thống',
      image:
        'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=164&h=164&fit=crop&auto=format',
    },
    {
      id: '3',
      name: 'Combo Lẩu <PERSON>á<PERSON>ru<PERSON>ề<PERSON> Thống',
      image:
        'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=164&h=164&fit=crop&auto=format',
    },
    {
      id: '4',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      image:
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=164&h=164&fit=crop&auto=format',
    },
  ];

  return (
    <div className="grid grid-cols-2 gap-x-[15px] gap-y-5 p-4">
      {menuItems.map((item) => (
        <div key={item.id} className="flex flex-col">
          <Image lazy src={item.image} fit="cover" className="aspect-square rounded-md" />

          <div className="pt-2">
            <h3 className="text-sm font-medium leading-[22px] text-black">{item.name}</h3>
          </div>
        </div>
      ))}
    </div>
  );
};
