// Libraries
import { useQueryClient } from "@tanstack/react-query";
import React, { useEffect } from "react";

// Components
import { MemberRegistration } from "components";
import { AccumulateDescription, QrCodeInfo } from "./components";

// Hooks
import { useViewPage } from "hooks";

// Constants
import { QUERY_KEY } from "constant";

interface AccumulatePointsProps {}

export const AccumulatePoints: React.FC<AccumulatePointsProps> = () => {
  const queryClient = useQueryClient();

  useViewPage({
    pageType: "qr_earn_point",
  });

  // useEffect(() => {
  //   return () => {
  //     queryClient.invalidateQueries({
  //       queryKey: [QUERY_KEY.GET_LOYALTY_CUSTOMER_DETAIL],
  //       exact: false,
  //     });
  //     queryClient.invalidateQueries({
  //       queryKey: [QUERY_KEY.GET_TRANSACTION_LIST],
  //       exact: false,
  //     });
  //   };
  // }, [queryClient]);

  return (
    <div className="flex flex-col h-full p-layout overflow-auto">
      <MemberRegistration registerText="Đăng ký ngay">
        <AccumulateDescription />
        <QrCodeInfo />
      </MemberRegistration>
    </div>
  );
};
