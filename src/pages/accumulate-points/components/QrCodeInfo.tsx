// Libraries
import { motion } from "motion/react";
import { QRCodeSVG } from "qrcode.react";
import React, { memo } from "react";
import styled from "styled-components";

// Images
import highlandsLogo from "assets/images/logos/highlands-logo-rounded.webp";
import divider from "assets/images/others/divider.png";

// Components
import { Tag } from "components";

// Hooks
import { useUserInfo } from "hooks";

// Constants

// Utils
import { countdownDateExpiration, dayjs } from "utils";

interface QrCodeInfoProps {}

const QrCodeInfoWrapper = styled(motion.div)`
  position: relative;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  border-radius: 20px;

  .qr-code {
    &__qr {
      image {
        border-radius: 5px;
      }
    }

    &__info-top {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px;
      gap: 16px;
      text-align: center;
    }

    &__info-bottom {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      text-align: center;
      padding: 17px 37.5px 48px;
      gap: 10px;
    }

    &__divider {
      width: 100%;
      aspect-ratio: 17.222;
      margin: -10px 0;
      background: url("${divider}") no-repeat center center / cover;
    }
  }
`;

export const QrCodeInfo: React.FC<QrCodeInfoProps> = memo((props) => {
  const { loyaltyCustomer } = useUserInfo();
  const { availablePoints, dateExpire, membershipCard } = loyaltyCustomer || {};

  const dateExpiration = countdownDateExpiration(
    dateExpire ? `${dateExpire} 23:59:59` : "",
    "DD/MM/YYYY 23:59:59"
  );
  const isPreviousDateExpired = dayjs(
    `${dateExpire} 23:59:59`,
    "DD/MM/YYYY"
  ).isBefore(dayjs(), "day");

  return (
    <QrCodeInfoWrapper
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="qr-code__info-top">
        <QRCodeSVG
          className="qr-code__qr"
          value={membershipCard || ""}
          size={260}
          imageRendering={"pixelated"}
          imageSettings={{
            src: highlandsLogo,
            width: 50,
            height: 50,
            excavate: true,
          }}
        />
        <span className="font-semibold">{`ID: ${membershipCard || ""}`}</span>
      </div>
      <div className="qr-code__divider"></div>
      <div className="qr-code__info-bottom">
        <span className="text-description">
          {`Bạn có ${availablePoints || 0} điểm Drips ${
            availablePoints && dateExpire
              ? `${
                  isPreviousDateExpired ? "đã" : "sẽ"
                } hết hạn vào ngày ${dateExpire}`
              : ""
          }`}
        </span>
        {!!dateExpiration && !!availablePoints && !isPreviousDateExpired && (
          <Tag color="warning">{`Hết hạn sau ${dateExpiration}`}</Tag>
        )}
      </div>
    </QrCodeInfoWrapper>
  );
});

QrCodeInfo.displayName = "QrCodeInfo";
