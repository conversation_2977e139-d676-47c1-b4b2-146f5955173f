// Libraries
import React, { memo } from "react";

// Hooks
import { useUserInfo } from "hooks";

// Utils
import { formatVietnameseCurrency } from "utils";

// Constants
import { TIER_KEY } from "constant";

interface AccumulateDescriptionProps {}

export const AccumulateDescription: React.FC<AccumulateDescriptionProps> = memo(
  (props) => {
    const { ...restProps } = props;
    const { loyaltyCustomer, nextMemberTier, memberTier } = useUserInfo();

    // Variables
    const {
      remainingAmountUpgrade,
      remainingAmountKeepgrade,
      nextDowngradeMembershipDate,
    } = loyaltyCustomer || {};

    return (
      <div className="text-center leading-6 py-4">
        {!!remainingAmountUpgrade && !!nextMemberTier && (
          <div>
            Bạn cần chi tiêu thêm <br />
            {`${formatVietnameseCurrency(
              remainingAmountUpgrade,
              " VNĐ"
            )}  để thăng hạng `}
            <span className="capitalize">{nextMemberTier.name}</span>
          </div>
        )}

        {!!remainingAmountKeepgrade && !!nextDowngradeMembershipDate ? (
          <div>
            <span>
              {`Chi tiêu thêm ${formatVietnameseCurrency(
                remainingAmountKeepgrade || 0,
                " VNĐ"
              )} để giữ hạng hiện tại`}{" "}
            </span>
            <br />
            <span>
              Thời gian duy trì hạng hiện tại đến{" "}
              <strong>hết {nextDowngradeMembershipDate}</strong>
            </span>
          </div>
        ) : (
          memberTier?.key !== TIER_KEY.MEMBER && (
            <div>Bạn đã chi tiêu đủ để giữ hạng</div>
          )
        )}
      </div>
    );
  }
);

AccumulateDescription.displayName = "AccumulateDescription";
