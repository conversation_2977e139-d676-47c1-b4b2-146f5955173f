import { useState, useMemo } from 'react';
import { ImageGallery } from 'components/ui/ImageGallery';
import { Tabs } from 'components/ui/Tabs';

const mockImages = [
  {
    id: 1,
    src: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=375&h=200&fit=crop&crop=center',
    alt: 'Golden Gate Restaurant Interior',
    title: 'Elegant dining space with warm lighting',
    description: 'Modern restaurant interior with comfortable seating',
  },
  {
    id: 2,
    src: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=185&h=185&fit=crop&crop=center',
    alt: 'Grilled seafood platter',
    title: 'Fresh grilled seafood',
    description: 'Delicious grilled prawns and fish',
  },
  {
    id: 3,
    src: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=185&h=185&fit=crop&crop=center',
    alt: 'Vietnamese hot pot',
    title: 'Traditional hot pot',
    description: 'Authentic Vietnamese hot pot experience',
  },
  {
    id: 4,
    src: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=375&h=200&fit=crop&crop=center',
    alt: 'Restaurant exterior view',
    title: 'Golden Gate Restaurant facade',
    description: 'Beautiful restaurant exterior with outdoor seating',
  },
  {
    id: 6,
    src: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=185&h=185&fit=crop&crop=center',
    alt: 'Asian fusion dessert',
    title: 'Signature dessert',
    description: 'Creative Asian-inspired dessert presentation',
  },
  {
    id: 7,
    src: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=375&h=200&fit=crop&crop=center',
    alt: 'Private dining room',
    title: 'VIP dining experience',
    description: 'Exclusive private dining room for special occasions',
  },
  {
    id: 8,
    src: 'https://images.unsplash.com/photo-1562967914-608f82629710?w=185&h=185&fit=crop&crop=center',
    alt: 'Coffee and pastries',
    title: 'Coffee corner',
    description: 'Artisan coffee with fresh pastries',
  },
];

export const RestaurantGallery = () => {
  const [activeTab, setActiveTab] = useState('all');

  const restaurantImages = mockImages;

  const renderGalleryContent = useMemo(
    () => <ImageGallery key={`gallery-${activeTab}`} images={restaurantImages} lazyLoad={false} />,
    [activeTab, restaurantImages],
  );

  return (
    <Tabs
      activeKey={activeTab}
      onChange={setActiveTab}
      items={[
        {
          key: 'all',
          title: 'Tất cả',
          content: activeTab === 'all' ? renderGalleryContent : null,
        },
        {
          key: 'foods',
          title: 'Món ăn',
          content: activeTab === 'foods' ? renderGalleryContent : null,
        },
        {
          key: 'space',
          title: 'Không gian',
          content: activeTab === 'space' ? renderGalleryContent : null,
        },
      ]}
    />
  );
};
