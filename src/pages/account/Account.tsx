// Libraries
import React from 'react';

// Components
import { InfoCards, Menu, Profile } from './components';

// Hooks
import { useUserInfo, useViewPage } from 'hooks';
import { PullToRefresh } from '@antscorp/ama-ui';

export const Account: React.FC = () => {
  const { userInfo, loyaltyCustomer, refetchLoyaltyCustomerDetail } = useUserInfo();

  useViewPage({
    pageType: 'my_account',
  });

  return (
    <PullToRefresh threshold={40} onRefresh={refetchLoyaltyCustomerDetail}>
      <div data-test="account-wrapper" className="flex flex-col gap-5 p-layout !pt-5">
        <Profile
          className="self-center"
          info={{
            username: userInfo.name || '',
            userid: userInfo.id || '',
            avatar: userInfo.avatar,
            verified: loyaltyCustomer?.verified,
          }}
        />

        <InfoCards />

        <Menu />
      </div>
    </PullToRefresh>
  );
};
