import clsx from 'clsx';
import { ChevronRightIcon } from 'lucide-react';
import { UserAvatar } from 'components/ui/UserAvatar/UserAvatar';

interface ProfileProps {
  info: {
    username: string;
    userid: string;
    avatar?: string;
    verified?: boolean;
  };
  className?: string;
}

export const Profile = (props: ProfileProps) => {
  const { info, className } = props;
  const { avatar, username, userid, verified } = info;

  return (
    <div className={clsx('flex w-[343px] flex-col items-center gap-4', className)}>
      <div className="flex size-16 items-center justify-center rounded-full bg-primary-light p-3">
        <UserAvatar src={avatar} className="size-11 rounded-full" />
      </div>

      <div className="flex w-full flex-col items-center gap-0.5">
        <h2 className="text-center font-roboto text-xl font-semibold leading-[1.4]">{username}</h2>

        <div className="flex items-center gap-2">
          <span className="font-roboto text-[15px] font-normal leading-[1.467] text-secondary">
            Mã KH: {userid}
          </span>

          <div
            className={clsx(
              'flex h-7 items-center justify-center gap-1 rounded-full px-2.5 py-0.5',
              {
                'bg-success-light': verified,
                'text-success': verified,
                'bg-error-light': !verified,
                'text-error': !verified,
              },
            )}
          >
            <span className="text-xs font-semibold leading-normal">
              {verified ? 'Đã xác thực' : 'Chưa xác thực'}
            </span>

            <ChevronRightIcon size={12} color="currentColor" />
          </div>
        </div>
      </div>
    </div>
  );
};
