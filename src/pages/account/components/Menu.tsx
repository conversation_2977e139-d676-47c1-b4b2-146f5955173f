import { ReactNode, useCallback, useMemo } from 'react';
import { List } from '@antscorp/ama-ui';
import { useNavigate } from 'react-router-dom';
import { ROUTE_KEYS, ROUTES } from 'constant/routes';
import UserProfileIcon from 'assets/svg/user-profile.svg?react';
import HistoryIcon from 'assets/svg/points-history.svg?react';
import ReservationHistoryIcon from 'assets/svg/reservation-history.svg?react';
import TermsAndConditionsIcon from 'assets/svg/terms-conditions.svg?react';
import AboutIcon from 'assets/svg/info-circle.svg?react';
import SubscriptionIcon from 'assets/svg/coin-refresh.svg?react';

const MENU_ITEMS = [
  {
    key: ROUTES.ACCOUNT_INFO.key,
    label: 'Thông tin cá nhân',
    icon: <UserProfileIcon width={24} height={24} />,
    goTo: ROUTES[ROUTE_KEYS.ACCOUNT_INFO].path,
  },
  {
    key: ROUTES[ROUTE_KEYS.TRANSACTION_HISTORIES].key,
    label: 'Lịch sử giao dịch',
    icon: <HistoryIcon width={24} height={24} />,
    goTo: ROUTES[ROUTE_KEYS.TRANSACTION_HISTORIES].path,
  },
  {
    key: ROUTES[ROUTE_KEYS.RESERVATION_HISTORY].key,
    label: 'Lịch sử đặt bàn',
    icon: <ReservationHistoryIcon width={24} height={24} />,
    goTo: ROUTES[ROUTE_KEYS.RESERVATION_HISTORY].path,
  },
  {
    key: ROUTES[ROUTE_KEYS.TERMS_AND_CONDITIONS].key,
    label: 'Điều khoản sử dụng',
    icon: <TermsAndConditionsIcon width={24} height={24} />,
    goTo: ROUTES[ROUTE_KEYS.TERMS_AND_CONDITIONS].path,
  },
  {
    key: ROUTES[ROUTE_KEYS.ABOUT].key,
    label: 'Về Golden SpoonS',
    icon: <AboutIcon width={24} height={24} />,
    goTo: ROUTES[ROUTE_KEYS.ABOUT].path,
  },
] as const;

export const Menu = () => {
  const navigate = useNavigate();

  const isSubscribed = false;

  const items = useMemo(() => {
    const results: {
      key: string;
      label: string;
      icon: ReactNode;
      goTo: string;
    }[] = [...MENU_ITEMS];

    if (isSubscribed) {
      results.splice(0, 0, {
        key: ROUTES.SUBSCRIPTION.key,
        label: 'Gói Không giới hạn của tôi',
        icon: <SubscriptionIcon width={24} height={24} />,
        goTo: ROUTES[ROUTE_KEYS.SUBSCRIPTION].path,
      });
    }

    return results;
  }, [isSubscribed]);

  const onClickMenuItem = useCallback(
    (path: string) => {
      console.log('path', path);
      navigate(path);
    },
    [navigate],
  );

  return (
    <List>
      {items.map((item) => (
        <List.Item
          key={item.key}
          title={<span className="font-medium text-black">{item.label}</span>}
          prefix={item.icon}
          onClick={() => onClickMenuItem(item.goTo)}
        />
      ))}
    </List>
  );
};
