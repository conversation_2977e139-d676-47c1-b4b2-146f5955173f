import React from 'react';

type InfoCardsProps = {
  gcoin?: number;
  discountAmount?: number;
  icon?: React.ReactNode;
  title?: string;
};

export const InfoCard = (props: InfoCardsProps) => {
  const { icon, title } = props;

  return (
    <div className="bg-primary-light flex flex-col items-center gap-2">
      {icon}

      <span className="text-center font-roboto font-normal text-secondary">{title}</span>
    </div>
  );
};
