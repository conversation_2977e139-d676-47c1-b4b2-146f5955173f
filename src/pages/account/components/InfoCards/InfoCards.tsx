import clsx from 'clsx';
import { Card } from './Card';
import { Badge } from '@antscorp/ama-ui';
import SubscriptionIcon from 'assets/svg/coin-refresh.svg?react';
import GcoinIcon from 'assets/svg/gcoin.svg?react';
import GiftIcon from 'assets/svg/giff.svg?react';

export const InfoCards = () => {
  const isSubscribed = true;
  const vourcherAmounnt = 100;
  const gcoinAmount = 12_690;

  const onSelectCard = (card: 'subscription' | 'gcoin' | 'gift') => {
    console.log(card);
  };

  return (
    <div
      className={clsx('grid items-center gap-4', {
        'grid-cols-3': isSubscribed,
        'grid-cols-2': !isSubscribed,
      })}
    >
      {isSubscribed && (
        <Card
          icon={<SubscriptionIcon width={36} height={36} />}
          title="Gói không giới hạn"
          onClick={() => onSelectCard('subscription')}
        />
      )}

      <Card
        icon={<GcoinIcon width={36} height={36} />}
        title={`${gcoinAmount} G${'\u2011'}coin`}
        onClick={() => onSelectCard('gcoin')}
      />

      <Card
        icon={
          <div className="relative">
            <GiftIcon height={36} width={36} />

            <Badge
              content={vourcherAmounnt}
              bordered
              className="absolute bottom-[calc(100%-12px)] left-[calc(100%-12px)]"
            />
          </div>
        }
        title="Ví ưu đãi"
        onClick={() => onSelectCard('gift')}
      />
    </div>
  );
};
