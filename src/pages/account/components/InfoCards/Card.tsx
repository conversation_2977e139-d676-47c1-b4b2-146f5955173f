import React, { forwardRef } from 'react';

type InfoCardsProps = {
  gcoin?: number;
  discountAmount?: number;
  icon?: React.ReactNode;
  title?: string;
  onClick?: () => void;
};

export const Card = forwardRef<HTMLDivElement, InfoCardsProps>((props, ref) => {
  const { icon, title, onClick } = props;

  return (
    <div
      className="bg-primary-light flex flex-col items-center gap-2 self-stretch rounded-lg border-[1.5px] border-primary p-3"
      ref={ref}
      onClick={onClick}
    >
      {icon}

      <div className="flex flex-1 items-center">
        <span className="break-words text-center font-roboto font-semibold">{title}</span>
      </div>
    </div>
  );
});

Card.displayName = 'Card';
