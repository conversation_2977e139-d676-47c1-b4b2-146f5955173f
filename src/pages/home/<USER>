// Libraries
import { motion } from "motion/react";
import React, { useCallback } from "react";
import styled from "styled-components";

// Components
import {
  CountdownResult,
  useCountdown,
  useSyncCustomer,
  useUserInfo,
  useViewPage,
} from "hooks";
import { News, SchemeListing, MemberShip } from "./components";
import { useGetHotNewsList } from "queries";
import { HomeHeader, LightningIcon, SummerGamePopup } from "components";
import { PullToRefresh } from "@antscorp/ama-ui";

interface HomeProps {}

const HomeWrapper = styled(motion.div)`
  position: relative;
  z-index: 10;
  padding: 12px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

export const Home: React.FC<HomeProps> = () => {
  useViewPage({
    pageType: "home",
  });

  useSyncCustomer();

  const { data: hotNewsListData, isLoading } = useGetHotNewsList({});
  const { refetchLoyaltyCustomerDetail } = useUserInfo();

  // Memos
  const newsList = hotNewsListData?.data || [];

  // Renders

  return (
    <>
      <PullToRefresh threshold={40} onRefresh={refetchLoyaltyCustomerDetail}>
        <HomeHeader />
        <HomeWrapper>
          <MemberShip />
          {/* <SchemeListing title={renderSchemeHotTitle} /> */}
          <SchemeListing schemeType="foryou" title={"Thương hiệu áp dụng"} />
          {/* <News newsList={newsList} loading={isLoading} /> */}
        </HomeWrapper>
      </PullToRefresh>
    </>
  );
};
