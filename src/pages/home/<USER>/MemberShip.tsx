// Libraries
import React, { memo, useCallback, useMemo } from "react";
import styled from "styled-components";
import { motion } from "motion/react";
import { generatePath, useNavigate } from "react-router-dom";

// Images
import voucher1 from "assets/vouchers/voucher-1.png";

// Components
import { BeanIcon, CheckIcon, MembershipCard } from "components";

// Schemas
import { OfferVoucher } from "schemas";

// Constants
import { ROUTES, TIER } from "constant";
import { useGetSchemeList } from "queries";
import { Avatar } from "zmp-ui";
import { Ellipsis, ProgressBar } from "@antscorp/ama-ui";
import { useAppConfig, useResponsive, useUserInfo } from "hooks";
import { respond } from "utils";

// Styles
const MemberShipWrapper = styled(motion.div)`
  width: 100%;
  text-align: justify;

  .title {
    font-weight: bold;
    font-size: 16px;
    color: var(--color-text-primary);
    padding-top: 13px;
    padding-bottom: 16px;
  }

  .link {
    display: inline-block;
    padding-top: 8px;
    color: var(--color-text-link);
  }

  .promotion-roadmap {
    margin-top: 25px;
    padding: 17px 12px;
    border: 1px solid var(--color-border);

    display: flex;
    flex-direction: column;
    gap: 12px;
    border-radius: 10.4px;

    &__title {
      font-weight: 500;
      font-size: 15px;
      display: flex;
      justify-content: space-between;

      .detail {
        color: var(--color-text-link);
        font-weight: 400;
      }
    }

    &__progress {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    &__progress-label {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    &__required-point {
      background-color: var(--color-grey-primary);
      width: 100%;
      padding: 7px 17px;
      text-align: center;
      font-size: 12px;
      border-radius: 6.24px;
    }
  }
`;

const UserInfo = styled.div`
  color: var(--color-text-white) !important;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 100%;

  .name {
    font-weight: 700;
    font-size: 16px;
  }

  .tier {
    font-size: 13px;
  }

  .balance {
    font-size: 21px;
    font-weight: bold;
    &.unit {
      font-size: 13px;
      align-content: end;
      padding-bottom: 3px;
    }
  }

  .update-at {
    font-size: 13px;
  }

  .zaui-avatar-image {
    border: 1.04px solid var(--color-text-white);
    box-sizing: border-box;
    flex-shrink: 0;
  }
`;

const TierPoint = styled.div`
  width: 21px;
  height: 21px;
  border-radius: 100%;

  ${respond.xs`
    width: 27px;
    height: 27px;
  `}
`;

// Mock Data
// const voucherList: Partial<OfferVoucher>[] = Array.from({ length: 10 }).map(
//   (_, index) => ({
//     id: `${index + 1}`,
//     brand: "KPUB",
//     name: "Phiếu quà tặng K-Pub 100k",
//     points: 100,
//     imageUrl: voucher1,
//   })
// );

interface MemberShipProps {}

export const MemberShip = memo((props: MemberShipProps) => {
  const { ...restProps } = props;
  const navigate = useNavigate();

  // Hooks
  const { appSettings } = useAppConfig();
  const { userInfo, memberTier, nextMemberTier, loyaltyCustomer } =
    useUserInfo();
  const { isLargeMobile } = useResponsive();

  // Variables
  const { tierList = Object.values(TIER) } =
    appSettings?.globals?.loyalty || {};
  const { spendingAmount = 200000 } = loyaltyCustomer || {};

  // Memos
  const promotionProgress = useMemo(() => {
    if (!memberTier || !tierList?.length) return 0;

    const totalTiers = tierList.length;
    const currentTierIndex = tierList.findIndex(
      (tier) => tier.key === memberTier.key
    );

    // If user is already at the highest tier or there's no next tier info
    if (currentTierIndex === totalTiers - 1 || !nextMemberTier) {
      return 100;
    }

    // Calculate percentage gap between each tier (e.g., 3 tiers = 0%, 50%, 100%)
    const percentPerTier = 100 / (totalTiers - 1);

    // Calculate current progress:
    // 1. Base progress = current tier index * percentPerTier
    // 2. Additional progress within current tier based on spending
    const baseProgress = percentPerTier * currentTierIndex;
    const extraProgress =
      ((spendingAmount - memberTier.minSpend) * percentPerTier) /
      (nextMemberTier.minSpend - memberTier.minSpend);

    return Math.min(baseProgress + extraProgress, 100);
  }, [memberTier, nextMemberTier, spendingAmount, tierList]);

  const requiredPoints = useMemo(() => {
    if (!nextMemberTier || spendingAmount >= nextMemberTier.minSpend) return 0;
    return nextMemberTier.minSpend - spendingAmount;
  }, [nextMemberTier, spendingAmount]);

  // Queries
  const { data: schemeListData, isLoading: isSchemeListLoading } =
    useGetSchemeList({
      args: {
        params: {
          type: "hot",
        },
      },
    });

  // Memo
  const schemeList = useMemo(() => {
    return schemeListData?.data?.schemes || [];
  }, [schemeListData?.data]);

  // Handlers

  return (
    <MemberShipWrapper {...restProps}>
      <motion.div className="title">THÔNG TIN TÍCH ĐIỂM</motion.div>
      <MembershipCard
      // backgroundColor={
      //   memberTier?.backgroundColor || "var(--color-main-primary)"
      // }
      >
        <UserInfo>
          <div className="flex items-center gap-2">
            <Avatar src={userInfo?.avatar} size={50} />
            <div className="flex flex-col">
              <Ellipsis
                className="name"
                content={userInfo?.name?.toUpperCase() || ""}
              />
              <span className="tier">{memberTier?.name || ""}</span>
            </div>
          </div>
          <div className="flex flex-col text-right">
            <div className="flex gap-1 self-end">
              <span className="balance">
                {spendingAmount.toLocaleString("vi-VN")}
              </span>
              <span className="balance unit">VND</span>
            </div>
            <div className="flex gap-1 self-end">
              <span className="update-at">
                Cập nhật lúc: 17:29 - 13/04/2023
              </span>
            </div>
          </div>
        </UserInfo>
      </MembershipCard>
      <motion.div className="mt-4">
        Golden SpoonS thường cập nhật điểm 01 giờ sau khi thanh toán.
      </motion.div>
      <motion.a href={""} className="link">
        Xem điều lệ chương trình
      </motion.a>
      <div className="promotion-roadmap">
        <div className="promotion-roadmap__title">
          Tích điểm thăng hạng
          <a className="promotion-roadmap__title detail">Xem chi tiết</a>
        </div>
        <div className="promotion-roadmap__progress">
          <div className="absolute w-full">
            <ProgressBar
              style={{
                "--track-width": "8px",
                "--fill-color": "var(--color-green-primary)",
                "--track-color": "var(--color-grey-primary)",
              }}
              percent={promotionProgress}
            />
          </div>
          <div className="relative flex items-center justify-between w-full">
            {tierList.map((tier) => {
              const isCurrentTier = tier.key === memberTier?.key;

              return (
                <div key={tier.key} className="relative">
                  {isCurrentTier && (
                    <div
                      className="absolute inset-0 rounded-full opacity-75 animate-ping"
                      style={{
                        background: "var(--color-green-primary)",
                        animationDuration: "4s",
                      }}
                    />
                  )}
                  <TierPoint
                    className="relative flex items-center justify-center z-10"
                    style={{
                      color: "var(--color-text-white)",
                      background:
                        isCurrentTier || promotionProgress >= 100
                          ? "var(--color-green-primary)"
                          : "var(--color-grey-secondary)",
                    }}
                  >
                    <CheckIcon
                      size={isLargeMobile ? 13 : 10}
                      animated={isCurrentTier}
                    />
                  </TierPoint>
                </div>
              );
            })}
          </div>
        </div>
        <div className="promotion-roadmap__progress-label">
          {tierList.map((tier) => {
            const { title, description, textAlign } = tier.promotionConfig;
            return (
              <div
                key={tier.key}
                style={{
                  textAlign,
                }}
                className="flex-1"
              >
                <div className="font-[500] text-[12px] xs:text-[14px]">
                  {title}
                </div>
              </div>
            );
          })}
        </div>
        <span className="promotion-roadmap__required-point	">
          {requiredPoints ? (
            <span>
              Tích thêm{" "}
              <span className="font-bold">{`${requiredPoints.toLocaleString(
                "vi-VN"
              )} VND`}</span>{" "}
              để lên hạng tiếp theo.
            </span>
          ) : (
            <>Chúc mừng bạn đã đạt hạng cao nhất!</>
          )}
        </span>
      </div>
    </MemberShipWrapper>
  );
});

MemberShip.displayName = "MemberShip";
