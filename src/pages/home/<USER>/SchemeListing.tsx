// Libraries
import { useGetSchemeList } from "queries";
import React, { memo, useCallback, useMemo } from "react";
import { generatePath } from "react-router-dom";
import styled from "styled-components";

// Hooks
import { CountdownResult, useCountdown, useNavigateWithSearch } from "hooks";

// Components
import { BrandItem } from "components";

// Constants
import { ROUTES } from "constant";

// Schemas
import { Countdown, OfferVoucher } from "schemas";

// Types
import { SchemeType } from "types";

// Assets
import Ashima from "assets/images/logos/brands/Ashima.png";
import CrystalJade from "assets/images/logos/brands/CrystalJade.png";
import Daruma from "assets/images/logos/brands/Daruma.png";
import GogiHouse from "assets/images/logos/brands/GogiHouse.png";
import Hutong from "assets/images/logos/brands/Hutong.png";
import Icook from "assets/images/logos/brands/Icook.png";
import KPub from "assets/images/logos/brands/KPub.png";

interface SchemeListingProps {
  title?: string;
  schemeType?: SchemeType;
}

export const SchemeListingWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;

  .scheme-listing {
    &__title {
      display: flex;
      width: 100%;
      justify-content: space-between;
      font-weight: 500;
      font-size: 16px;
      padding-bottom: 14px;

      .see-all {
        font-size: 15px;
        font-weight: 400;
        color: var(--color-text-link) !important;
      }
    }

    &__content {
      display: flex;
      gap: 10px;
      width: 100%;
      overflow: auto;
    }
  }
`;

const mockBrandList = [
  {
    id: "1",
    name: "Ashima",
    image: Ashima,
    description: "Ashima Hotpot",
  },
  {
    id: "2",
    name: "Crystal Jade",
    image: CrystalJade,
    description: "Lần đầu tiên xuất hiện tại Việt Nam, Crystal Jade",
  },
  {
    id: "3",
    name: "Daruma",
    image: Daruma,
    description: "Sushi, Sashimi & Hotpot",
  },
  {
    id: "4",
    name: "Gogi House",
    image: GogiHouse,
    description: "Quán thịt nướng Hàn Quốc",
  },
  {
    id: "5",
    name: "Hutong",
    image: Hutong,
    description: "Lẩu Hồng Kông. Văn hoá ẩm thực Hong Kong",
  },
  {
    id: "6",
    name: "Icook",
    image: Icook,
    description: "Restaurant at Home",
  },
  {
    id: "7",
    name: "K-Pub",
    image: KPub,
    description: "K-Pub không chỉ đặc biệt ở tên gọi mà còn",
  },
];

const skeletons = Array.from({ length: 5 }).map((_, index) => ({
  id: `${index + 1}`,
}));

export const SchemeListing: React.FC<SchemeListingProps> = memo((props) => {
  // Props
  const { title, schemeType = "hot" } = props || {};

  // Hooks
  const navigate = useNavigateWithSearch();
  const { data: schemeListData, isLoading: isSchemeListLoading } =
    useGetSchemeList({
      args: {
        params: {
          type: schemeType,
        },
      },
    });

  // Memo
  const schemeList = useMemo(() => {
    return (
      schemeListData?.data?.schemes?.sort(
        (a, b) => Number(b.point_redeem) - Number(a.point_redeem)
      ) || []
    );
  }, [schemeListData?.data]);

  // Handlers
  const onClickSeeAll = () => {
    navigate({
      pathname: ROUTES.BRAND_LIST.path,
    });
  };

  const onClickScheme = (brand: Partial<OfferVoucher>) => {
    console.log("🚀 ~ onClickScheme ~ brand:", brand);
    navigate(
      generatePath(ROUTES["BRAND_LIST"].path, {
        brandId: `${brand.id || ""}`,
      })
    );
  };

  // Renders

  if (!isSchemeListLoading && !schemeList?.length) {
    return null;
  }

  return (
    <SchemeListingWrapper
      data-testid="scheme-listing"
      className="scheme-listing"
    >
      <div className="flex items-center justify-between w-full">
        <div className="scheme-listing__title xs:!text-lg">
          {title?.toUpperCase()}
          <button
            className="see-all"
            onClick={onClickSeeAll}
          >{`Xem thêm`}</button>
        </div>
      </div>
      <div className="scheme-listing__content hide-scrollbar">
        {isSchemeListLoading
          ? skeletons.map((scheme) => (
              <BrandItem
                key={scheme.id}
                className="!w-[30%] h-[131px]"
                scheme={scheme}
                skeleton
              />
            ))
          : mockBrandList.map((brand) => (
              <BrandItem
                key={brand.id}
                scheme={brand}
                className="!w-[30%] !h-[131px]"
                onClick={() => {
                  onClickScheme(brand);
                }}
              />
            ))}
      </div>
    </SchemeListingWrapper>
  );
});

SchemeListing.displayName = "SchemeListing";
