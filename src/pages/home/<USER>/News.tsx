// Libraries
import { isEmpty } from "lodash-es";
import { HTMLMotionProps, motion } from "motion/react";
import React, { useCallback } from "react";
import styled from "styled-components";
import { openWebview } from "zmp-sdk";

// Components
import { Image, Swiper } from "@antscorp/ama-ui";
import { ConditionalRenderer } from "components";

// Schemas
import { HotNews } from "schemas";

// Utils
import { callCdpEvent } from "utils";

// Constants
import { EVENT_CONFIG } from "constant";
import { useUserInfo } from "hooks";

const { Item } = Swiper;

// Define component props
interface NewsProps extends HTMLMotionProps<"div"> {
  title?: string;
  newsList?: HotNews[];
  loading?: boolean;
}

// Styled component for News
const NewsWrapper = styled(motion.div)`
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: 250px;

  .news {
    &__title {
      font-family: "Red Rose", sans-serif;
      font-weight: 700;
      font-size: 16px;
      line-height: 19.98px;
      color: var(--color-text-brown);
    }

    &__news-item {
      width: 100%;
    }

    &__news-item-img {
      width: 100%;
      aspect-ratio: 1.5;
      border-radius: var(--adm-radius-m);
      height: auto !important;
    }
  }
` as unknown as React.ComponentType<HTMLMotionProps<"div">>;

// News component
export const News: React.FC<NewsProps> = ({
  title = "Tin tức",
  newsList = [],
  loading,
  ...restProps
}) => {
  const { userInfo } = useUserInfo();

  // Handlers
  const onClickNewsItem = useCallback(
    (item: HotNews, index?: number) => {
      // Call CDP event tracking
      callCdpEvent({
        uId: userInfo?.id,
        ea: "click",
        ec: "banner",
        data: {
          position: "home_what_new",
          page_type: "home",
          page_cate: EVENT_CONFIG.APP_LOYALTY,
          index,
          destination_url: item.destination_url,
          image_url: item.image_url,
        },
      });

      // Handle news item click
      openWebview({
        url: item.destination_url,
        config: {
          style: "bottomSheet",
          leftButton: "back",
        },
      });
    },
    [userInfo?.id]
  );

  // Define memoized function to render the news list
  const renderNewsList = useCallback(() => {
    // Return swiper component with news items
    return (
      <ConditionalRenderer isEmpty={isEmpty(newsList)} isLoading={loading}>
        <Swiper indicator={false} autoplay={true} autoplayInterval={5000} loop>
          {Array.isArray(newsList)
            ? newsList?.map((news, index) => {
                // Destructure news item properties
                const { image_url } = news;

                return (
                  <Item
                    key={index}
                    className="news__news-item"
                    onClick={() => onClickNewsItem(news, index)}
                  >
                    <Image
                      className="news__news-item-img"
                      src={image_url}
                      fit="cover"
                    />
                  </Item>
                );
              })
            : []}
        </Swiper>
      </ConditionalRenderer>
    );
  }, [loading, newsList, onClickNewsItem]);

  // Render component
  return (
    <NewsWrapper {...restProps}>
      <div className="news__title xs:!text-lg">{title}</div>
      {renderNewsList()}
    </NewsWrapper>
  );
};
