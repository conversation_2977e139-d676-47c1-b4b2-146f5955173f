import React from 'react';
import { ChevronRight } from 'lucide-react';
import { Rating } from 'components/ui';

export interface Restaurant {
  id: string;
  name: string;
  rating: number;
  totalReviews: number;
}

interface ReviewsSectionProps {
  restaurant: Restaurant;
}

export const ReviewsSection: React.FC<ReviewsSectionProps> = ({ restaurant }) => {
  // Mock reviews data
  const reviews = [
    {
      id: '1',
      userName: 'Lê Trần <PERSON>',
      phoneNumber: '0988 *** ***',
      date: '14/06/2021',
      rating: 4,
      reviewText:
        'Thái độ nhân viên nhiệt tình. Món ăn thì đa dạng. Tuy nhiên nước lẩu hơi cay với mình.',
      avatar: 'https://via.placeholder.com/48x48?text=LA',
    },
    {
      id: '2',
      userName: 'Nguyễn <PERSON>',
      phoneNumber: '0988 *** ***',
      date: '13/06/2021',
      rating: 5,
      reviewText: '<PERSON>h<PERSON><PERSON> gian đẹp, rộng rãi, tho<PERSON><PERSON> mái. <PERSON>ồ ăn Ngon',
      avatar: 'https://via.placeholder.com/48x48?text=NH',
    },
    {
      id: '3',
      userName: 'Mỹ Linh',
      phoneNumber: '0988 *** ***',
      date: '13/06/2021',
      rating: 5,
      reviewText: 'Nhà hàng đẹp, nhiều góc chụp lên ảnh rất đẹp. Mình và bạn bè hay đi ăn.',
      avatar: 'https://via.placeholder.com/48x48?text=ML',
    },
  ];

  const handleViewAllReviews = () => {
    // Navigate to all reviews page
    console.log('View all reviews');
  };

  return (
    <div className="flex flex-col gap-4 bg-white p-4">
      <Rating
        title="Đánh giá"
        overallRating={restaurant.rating}
        totalReviews={restaurant.totalReviews}
        reviews={reviews}
        onViewAllReviews={handleViewAllReviews}
      />
    </div>
  );
};
