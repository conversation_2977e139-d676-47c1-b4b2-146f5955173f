import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Users, Calendar } from 'lucide-react';
import { ReservationTimeSelector } from 'components/ui';

export interface Restaurant {
  id: string;
  name: string;
}

interface ReservationSectionProps {
  restaurant: Restaurant;
}

export const ReservationSection: React.FC<ReservationSectionProps> = ({ restaurant }) => {
  const [selectedTime, setSelectedTime] = useState<string>('12:00');
  const [selectedGuests, setSelectedGuests] = useState<number>(4);
  const [selectedDate, setSelectedDate] = useState<string>('Chủ nhật, 27/12, 12h00');

  // Mock time slots
  const timeSlots = [
    { id: '12:00', time: '12:00', available: true },
    { id: '12:15', time: '12:15', available: true },
    { id: '12:30', time: '12:30', available: true },
    { id: '12:45', time: '12:45', available: true },
    { id: '13:00', time: '13:00', available: true },
    { id: '16:00', time: '16:00', available: true },
    { id: '16:30', time: '16:30', available: true },
  ];

  const handleTimeSelect = (timeSlot: any) => {
    setSelectedTime(timeSlot.time);
  };

  return (
    <div className="flex flex-col gap-5 bg-white p-4">
      {/* Header */}
      <div className="flex flex-col gap-1">
        <h2 className="text-lg font-medium leading-[26px] text-black">Đặt bàn</h2>
        <p className="text-sm leading-[22px] text-secondary">
          Món ngon cùng thưởng thức, đặt bàn ngay lập tức
        </p>
      </div>

      {/* Reservation Form */}
      <div className="flex flex-col gap-4">
        {/* Date and Guests Selection */}
        <div className="border-border flex h-10 items-center rounded-sm border">
          {/* Guests */}
          <div className="border-border flex items-center gap-2 border-r px-3">
            <Users className="h-4 w-4 text-black" />
            <span className="text-sm font-medium leading-[22px] text-black">
              {selectedGuests} người
            </span>
          </div>

          {/* Date */}
          <div className="flex flex-1 items-center gap-2 px-3">
            <Calendar className="h-4 w-4 text-black" />
            <span className="text-sm font-medium leading-[22px] text-black">{selectedDate}</span>
          </div>

          {/* Arrow Icons */}
          <div className="flex flex-col">
            <button className="p-1">
              <ChevronUp className="h-4 w-4 text-black" />
            </button>
            <button className="p-1">
              <ChevronDown className="h-4 w-4 text-black" />
            </button>
          </div>
        </div>

        {/* Time Selection */}
        <div className="flex flex-col gap-2">
          <ReservationTimeSelector
            timeSlots={timeSlots}
            selectedTimeId={selectedTime}
            onTimeSelect={handleTimeSelect}
            showScrollButtons={true}
            scrollAmount={200}
          />
        </div>
      </div>
    </div>
  );
};
