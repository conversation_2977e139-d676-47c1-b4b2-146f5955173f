import React from 'react';
import { OfferCard } from 'components/ui';

export interface Restaurant {
  id: string;
  name: string;
}

interface RestaurantOffersSectionProps {
  restaurant: Restaurant;
}

export const RestaurantOffersSection: React.FC<RestaurantOffersSectionProps> = ({ restaurant }) => {
  // Mock offers data
  const offers = [
    {
      id: '1',
      title: 'Sumo Hà Nội tặng 100.000 suất buffet 0đ',
      heroImage: 'https://via.placeholder.com/300x140?text=Offer+1',
      expiryDate: '15/11/2021',
      gCoinReward: 30000,
      restaurantName: 'Gogi',
      restaurantLogo: 'https://via.placeholder.com/16x16?text=G',
    },
    {
      id: '2',
      title: 'Su<PERSON> Hà Nội tặng 100.000 suất buffet 0đ',
      heroImage: 'https://via.placeholder.com/300x140?text=Offer+2',
      expiryDate: '15/11/2021',
      gCoinReward: 30000,
      restaurantName: 'Gogi',
      restaurantLogo: 'https://via.placeholder.com/16x16?text=G',
    },
  ];

  return (
    <div className="flex flex-col gap-3 bg-white">
      <div className="flex flex-col gap-3 px-4 py-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium leading-[26px] text-black">Các ưu đãi từ nhà hàng</h2>

          <button className="flex items-center gap-1.5 text-blue">
            <span className="text-sm font-medium leading-[22px]">Tất cả</span>
          </button>
        </div>

        {/* Offers List */}
        <div className="flex gap-4 overflow-x-auto pb-2 scrollbar-hide">
          {offers.map((offer) => (
            <OfferCard
              key={offer.id}
              className="min-w-[300px]"
              restaurantName={offer.restaurantName}
              restaurantLogo={offer.restaurantLogo}
              title={offer.title}
              expiryDate={offer.expiryDate}
              gCoinReward={offer.gCoinReward}
              heroImage={offer.heroImage}
              onExchange={() => {
                // Handle exchange logic
                console.log(`Exchange offer ${offer.id}`);
              }}
              onClick={() => {
                // Handle offer click
                console.log(`View offer details ${offer.id}`);
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
