import React from 'react';

export interface Restaurant {
  id: string;
  name: string;
}

interface MenuHighlightsSectionProps {
  restaurant: Restaurant;
}

export const MenuHighlightsSection: React.FC<MenuHighlightsSectionProps> = ({ restaurant }) => {
  // Mock menu items
  const menuItems = [
    {
      id: '1',
      name: 'Combo Lẩu <PERSON> Truyền Thống',
      image: 'https://via.placeholder.com/164x164?text=Menu+1',
    },
    {
      id: '2',
      name: 'Combo Lẩu <PERSON> Truyền Thống',
      image: 'https://via.placeholder.com/164x164?text=Menu+2',
    },
    {
      id: '3',
      name: 'Combo Lẩu T<PERSON>á<PERSON>ru<PERSON>ền Thống',
      image: 'https://via.placeholder.com/164x164?text=Menu+3',
    },
    {
      id: '4',
      name: '<PERSON><PERSON><PERSON> Mai',
      image: 'https://via.placeholder.com/164x164?text=Menu+4',
    },
  ];

  return (
    <div className="flex flex-col gap-4 bg-white p-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium leading-[26px] text-black">Menu nổi bật</h2>

        <button className="flex items-center gap-1.5 text-blue">
          <span className="text-sm font-medium leading-[22px]">Tất cả</span>
        </button>
      </div>

      {/* Menu Grid */}
      <div className="flex flex-col gap-4">
        {/* First Row */}
        <div className="flex gap-4">
          {menuItems.slice(0, 2).map((item) => (
            <div key={item.id} className="flex flex-col">
              <div className="bg-gray-200 relative h-[164px] w-[164px] overflow-hidden rounded-md">
                <img src={item.image} alt={item.name} className="h-full w-full object-cover" />
              </div>
              <div className="pt-2">
                <h3 className="text-sm font-medium leading-[22px] text-black">{item.name}</h3>
              </div>
            </div>
          ))}
        </div>

        {/* Second Row */}
        <div className="flex gap-4">
          {menuItems.slice(2, 4).map((item) => (
            <div key={item.id} className="flex flex-col">
              <div className="bg-gray-200 relative h-[164px] w-[164px] overflow-hidden rounded-md">
                <img src={item.image} alt={item.name} className="h-full w-full object-cover" />
              </div>
              <div className="pt-2">
                <h3 className="text-sm font-medium leading-[22px] text-black">{item.name}</h3>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
