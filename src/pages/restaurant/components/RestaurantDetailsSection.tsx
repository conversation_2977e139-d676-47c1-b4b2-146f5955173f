import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { Map } from 'components/ui/Map';
import { useCopyToClipboard } from 'hooks/useCopyToClipboard';
import { PhoneIcon } from 'components/icons/PhoneIcon';
import { MapBinIcon } from 'components/icons/MapBinIcon';
import { ClocheIcon } from 'components/icons/ClocheIcon';
import { RestaurantMapBinIcon } from 'components/icons/RestaurantMapBinIcon';
import { PhoneNumber } from 'components/ui/PhoneNumber';

export interface Restaurant {
  id: string;
  name: string;
  description: string;
  address: string;
  phone: string;
  type: string;
  location: {
    lat: number;
    lng: number;
  };
  distance: string;
}

interface RestaurantDetailsSectionProps {
  restaurant: Restaurant;
}

export const RestaurantDetailsSection: React.FC<RestaurantDetailsSectionProps> = ({
  restaurant,
}) => {
  const [showFullDescription, setShowFullDescription] = useState(false);

  const { copyToClipboard: copyAdressToClipboard } = useCopyToClipboard({
    content: restaurant.address,
    toastMessage: 'Đã sao chép địa chỉ',
  });

  const toggleDescription = () => {
    setShowFullDescription(!showFullDescription);
  };

  return (
    <div className="flex flex-col gap-4 bg-white p-4">
      {/* Title */}
      <h2 className="text-lg font-medium leading-[26px] text-black">Thông tin chi tiết</h2>

      {/* Description */}
      <div className="flex flex-col gap-2">
        <p className="text-sm leading-[22px] text-secondary">
          {showFullDescription
            ? restaurant.description
            : `${restaurant.description.substring(0, 100)}...`}
        </p>

        <button
          onClick={toggleDescription}
          className="flex items-center gap-1.5 self-start text-blue"
        >
          <span className="text-sm font-medium leading-[22px]">
            {showFullDescription ? 'Thu gọn' : 'Xem thêm'}
          </span>

          <ChevronDown
            className={`h-3 w-3 transition-transform ${showFullDescription ? 'rotate-180' : ''}`}
          />
        </button>
      </div>

      {/* Map */}
      <div className="bg-gray-200 relative w-full overflow-hidden rounded-lg">
        <Map center={restaurant.location} zoom={15} height="200px" />

        {/* Map Overlay */}
        <div className="absolute bottom-1/2 left-1/2 flex h-9 w-9 -translate-x-1/2 transform items-center justify-center rounded-full shadow-md">
          <RestaurantMapBinIcon className="h-8 w-8 text-primary" />
        </div>
      </div>

      {/* Contact Information */}
      <div className="flex flex-col gap-4">
        {/* Address */}
        <div className="flex gap-2" role="button" onClick={copyAdressToClipboard}>
          <MapBinIcon className="mt-0.5 h-5 w-5 text-black" />

          <div className="flex flex-col gap-2">
            <span className="text-sm font-medium leading-[22px] text-black">Địa chỉ</span>
            <span className="text-sm leading-[22px] text-black">{restaurant.address}</span>
            <span className="text-sm leading-[22px] text-blue">{restaurant.distance}</span>
          </div>
        </div>

        {/* Phone */}
        <div className="flex gap-2">
          <PhoneIcon className="mt-0.5 h-5 w-5 text-black" />

          <div className="flex flex-col gap-2">
            <span className="text-sm font-medium leading-[22px] text-black">Hotline</span>
            <PhoneNumber phoneNumber={restaurant.phone} className="text-sm leading-[22px]" />
          </div>
        </div>

        {/* Restaurant Type */}
        <div className="flex gap-2">
          <ClocheIcon className="mt-0.5 h-5 w-5 text-black" />

          <div className="flex flex-col gap-2">
            <span className="text-sm font-medium leading-[22px] text-black">Loại hình</span>
            <span className="text-sm leading-[22px] text-black">{restaurant.type}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
