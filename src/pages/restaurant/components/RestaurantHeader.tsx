import React from 'react';
import { Clock, Star } from 'lucide-react';

export interface Restaurant {
  id: string;
  name: string;
  address: string;
  rating: number;
  totalReviews: number;
  operatingHours: string;
}

interface RestaurantHeaderProps {
  restaurant: Restaurant;
}

export const RestaurantHeader: React.FC<RestaurantHeaderProps> = ({ restaurant }) => {
  return (
    <div className="flex flex-col gap-4 bg-white p-4">
      {/* Restaurant Info */}
      <div className="flex flex-col gap-1">
        <h1 className="text-xl font-semibold leading-[28px] text-black">{restaurant.name}</h1>
        <p className="text-sm leading-[18px] text-secondary">{restaurant.address}</p>
      </div>

      {/* Rating and Hours */}
      <div className="flex items-center gap-6">
        {/* Operating Hours */}
        <div className="flex items-center gap-1">
          <Clock className="h-4 w-4 text-error" />
          <span className="text-sm font-medium leading-[22px] text-black">
            {restaurant.operatingHours}
          </span>
        </div>

        {/* Rating */}
        <div className="flex items-center gap-1">
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-primary text-primary" />
            <span className="text-sm font-bold leading-[22px] text-primary">
              {restaurant.rating.toFixed(1)}
            </span>
          </div>
          <span className="text-sm font-medium leading-[22px] text-black">
            ({restaurant.totalReviews} đánh giá)
          </span>
        </div>
      </div>
    </div>
  );
};
