import React from 'react';
import { useParams } from 'react-router-dom';
import {
  MenuHighlightsSection,
  RestaurantOffersSection,
  ReviewsSection,
  ReservationSection,
  RestaurantDetailsSection,
  RestaurantHeader,
} from './components';

export const RestaurantDetail: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();

  // Mock data - replace with actual API call
  const restaurantData = {
    id: restaurantId || '1',
    name: 'Gogi House Nguyễn Hồng Đào',
    address: '131 Nguyễn Hồng Đào, Phường 14, Tân Bình, TP Hồ Chí Minh',
    rating: 5.0,
    totalReviews: 59,
    operatingHours: 'Đóng - Mở: 06h00',
    phone: '02873009191',
    type: 'Lẩu, nướng, khác',
    description:
      'Gogi – <PERSON><PERSON><PERSON> thịt nướng Hàn Quốc Ngon Số 1 sẽ đưa bạn ghé đến những quán thịt nướng tại Seoul đã tạo nên danh tiếng cho nền ẩm thực xứ kim...',
    location: {
      lat: 10.8231,
      lng: 106.6297,
    },
    distance: '0.5 km ~ 9 phút',
  };

  return (
    <div className="flex flex-col gap-2 bg-gray">
      <RestaurantHeader restaurant={restaurantData} />

      <ReservationSection restaurant={restaurantData} />

      <RestaurantDetailsSection restaurant={restaurantData} />

      <MenuHighlightsSection restaurant={restaurantData} />

      <RestaurantOffersSection restaurant={restaurantData} />

      <ReviewsSection restaurant={restaurantData} />
    </div>
  );
};
