// Libraries
import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import clsx from 'clsx';

// Icons
import { GCoinIcon } from 'components/icons';
import { ChevronDown } from 'lucide-react';

// Types
interface MenuItem {
  id: string;
  name: string;
  description: string;
  originalPrice?: number;
  currentPrice: number;
  image: string;
  category: string;
}

interface MenuData {
  id: string;
  name: string;
  description: string;
  image: string;
  originalPrice?: number;
  currentPrice: number;
  menuItems: MenuItem[];
}

// Mock data
const MOCK_MENU_DATA: MenuData = {
  id: '1',
  name: 'Combo Ưu Đãi Hot Pot',
  description: `
    <div>
      <p><strong>🍲 Combo Lẩu Đặc Biệt - Trải Nghiệm Ẩm Thực Đỉnh Cao</strong></p>
      
      <p>Thưởng thức combo lẩu đặc biệt với các món ăn kèm hấp dẫn, được chế biến từ những nguyên liệu tươi ngon và chất lượng nhất. Combo này được thiết kế hoàn hảo cho 2-4 người, mang đến trải nghiệm ẩm thực đầy đủ và thỏa mãn.</p>

      <p><strong>📋 Combo bao gồm:</strong></p>
      <ul style="margin-left: 20px;">
        <li><strong>Nước lẩu đặc biệt:</strong> Lẩu Thái chua cay truyền thống với hương vị đậm đà, cân bằng hoàn hảo giữa vị chua của me và cay của ớt</li>
        <li><strong>Thịt tươi cao cấp:</strong> Thịt ba rọi thái lát mỏng, thịt bò Úc nhập khẩu, tôm sú tươi sống</li>
        <li><strong>Rau củ organic:</strong> Rau muống, rau cần tây, nấm đùi gà, nấm kim châm, bắp cải, cà chua</li>
        <li><strong>Bánh và bún:</strong> Bánh tráng nướng giòn rụm, bún tươi, bánh phở</li>
        <li><strong>Nước chấm đặc biệt:</strong> Nước chấm mắm nêm truyền thống, tương ớt cay nồng</li>
      </ul>

      <p><strong>⭐ Điểm nổi bật:</strong></p>
      <ul style="margin-left: 20px;">
        <li>Nguyên liệu 100% tươi sống, không chất bảo quản</li>
        <li>Nước lẩu được ninh từ xương ống trong 12 tiếng</li>
        <li>Phục vụ nóng hổi, đảm bảo chất lượng tốt nhất</li>
        <li>Thích hợp cho cả gia đình và bạn bè</li>
        <li>Có thể điều chỉnh độ cay theo yêu cầu</li>
      </ul>

      <p><strong>🕒 Thời gian phục vụ:</strong> 15-20 phút chuẩn bị</p>
      <p><strong>👥 Phù hợp:</strong> 2-4 người ăn</p>
      <p><strong>🔥 Độ cay:</strong> Trung bình (có thể điều chỉnh)</p>

      <p><em>Lưu ý: Combo có thể thay đổi một số món ăn kèm tùy theo tình hình nguyên liệu trong ngày. Vui lòng thông báo với nhân viên nếu có dị ứng thực phẩm.</em></p>
    </div>
  `,
  image:
    'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=375&h=175&fit=crop&crop=center',
  originalPrice: 450,
  currentPrice: 350,
  menuItems: [
    {
      id: '1',
      name: 'Lẩu Thái Chua Cay',
      description: 'Lẩu thái truyền thống với vị chua cay đặc trưng',
      currentPrice: 120,
      image:
        'https://images.unsplash.com/photo-1604908176997-125f25cc6f3d?w=300&h=140&fit=crop&crop=center',
      category: 'Lẩu',
    },
    {
      id: '2',
      name: 'Combo Thịt Ba Rọi',
      description: 'Thịt ba rọi tươi ngon, thái lát mỏng',
      originalPrice: 80,
      currentPrice: 65,
      image:
        'https://images.unsplash.com/photo-1544025162-d76694265947?w=300&h=140&fit=crop&crop=center',
      category: 'Thịt',
    },
    {
      id: '3',
      name: 'Rau Củ Tươi',
      description: 'Rau củ quả tươi mát, đa dạng',
      currentPrice: 45,
      image:
        'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=300&h=140&fit=crop&crop=center',
      category: 'Rau củ',
    },
    {
      id: '4',
      name: 'Bánh Tráng Nướng',
      description: 'Bánh tráng nướng giòn tan, ăn kèm',
      currentPrice: 25,
      image:
        'https://images.unsplash.com/photo-1626804475297-41608ea09aeb?w=300&h=140&fit=crop&crop=center',
      category: 'Món phụ',
    },
  ],
};

export const BrandMenu: React.FC = () => {
  const { brandId, menuId } = useParams<{ brandId: string; menuId: string }>();
  const [isExpanded, setIsExpanded] = useState(false);
  const [heroOpacity, setHeroOpacity] = useState(1);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Mock: In reality, this would fetch data based on brandId and menuId
  const menuData = MOCK_MENU_DATA;

  // Handle scroll effect for hero image fade
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const scrollY = scrollContainer.scrollTop;
      const heroHeight = 175; // Hero image height in pixels

      // Calculate opacity: fade out as user scrolls down
      // Opacity goes from 1 to 0 as scroll goes from 0 to heroHeight
      const opacity = Math.max(0, 1 - scrollY / heroHeight);
      setHeroOpacity(opacity);
    };

    scrollContainer.addEventListener('scroll', handleScroll);

    // Cleanup
    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const renderPrice = (currentPrice: number, originalPrice?: number) => {
    return (
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1">
          <GCoinIcon size={16} className="text-primary" />
          <span className="text-normal font-medium text-black">{currentPrice.toLocaleString()}</span>
        </div>
        {originalPrice && (
          <span className="text-sm text-secondary line-through">
            {originalPrice.toLocaleString()}
          </span>
        )}
      </div>
    );
  };

  return (
    <div ref={scrollContainerRef} className="flex flex-col overflow-y-auto bg-gray">
      {/* Hero Image */}
      <div
        className="bg-gray-300 h-[175px] w-full shrink-0 overflow-hidden transition-opacity duration-75 ease-out"
        style={{ opacity: heroOpacity }}
      >
        <img src={menuData.image} alt={menuData.name} className="h-full w-full object-cover" />
      </div>

      {/* Content */}
      <div className="flex-1 bg-white px-4">
        {/* Title and Pricing */}
        <div className="py-4">
          <h1 className="mb-3 text-xl font-semibold text-black">{menuData.name}</h1>
          {renderPrice(menuData.currentPrice, menuData.originalPrice)}
        </div>

        {/* Divider */}
        <div className="-mx-4 mb-4 h-2 bg-gray" />

        {/* Menu Description */}
        <div className="mb-4 flex flex-col">
          <div
            className={clsx('text-normal leading-relaxed text-black', !isExpanded && 'line-clamp-3')}
          >
            {/* This will be HTML content from API in the future */}
            <div dangerouslySetInnerHTML={{ __html: menuData.description }} />
          </div>

          <button
            onClick={toggleExpanded}
            className="mx-auto mt-2 text-normal font-medium text-blue-600 outline-none hover:text-blue-800"
          >
            {isExpanded ? 'Thu gọn' : 'Xem thêm'}

            <ChevronDown
              size={16}
              className={clsx('ml-[6px]', {
                'rotate-180': isExpanded,
              })}
            />
          </button>
        </div>

        {/* Divider */}
        <div className="-mx-4 mb-4 h-2 bg-gray" />

        {/* Related Menus */}
        <div className="mb-6">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-medium text-black">Thực đơn liên quan</h2>
            <button className="text-normal font-medium text-blue-600 outline-none hover:text-blue-800">
              Xem thêm
            </button>
          </div>

          {/* Horizontal scroll container */}
          <div className="flex gap-4 overflow-x-auto pb-2 scrollbar-hide">
            {menuData.menuItems.map((item) => (
              <div key={item.id} className="w-[300px] flex-shrink-0 rounded-lg bg-white">
                {/* Menu Item Image */}
                <div className="bg-gray-300 mb-3 h-[140px] w-full overflow-hidden rounded-lg">
                  <img src={item.image} alt={item.name} className="h-full w-full object-cover" />
                </div>

                {/* Menu Item Content */}
                <div className="space-y-2">
                  <h3 className="text-normal font-medium text-black">{item.name}</h3>
                  <p className="line-clamp-2 text-sm text-secondary">{item.description}</p>
                  {renderPrice(item.currentPrice, item.originalPrice)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
