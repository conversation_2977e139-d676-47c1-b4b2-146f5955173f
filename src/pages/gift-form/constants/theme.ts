import { ThemeConfig } from "@antscorp/ama-ui-v1";

export const GIFT_FORM_THEME: ThemeConfig = {
  token: {},
  components: {
    Input: {
      // paddingBlockLG: 14,
      paddingInlineLG: 24,
      borderRadiusLG: 100,
      controlHeightLG: 50,
      colorText: "var(--color-main-primary)",
      colorTextPlaceholder: "var(--color-placeholder)",
    },
    DatePicker: {
      // paddingBlockLG: 18,
      // paddingInlineLG: 24,
      // borderRadiusLG: 100,
      // colorText: "var(--color-main-primary)",
      controlHeightLG: 50,
      borderRadius: 100,
      paddingInline: 24,
      colorTextPlaceholder: "var(--color-placeholder)",
    },
    Select: {
      controlHeightLG: 50,
      // borderRadiusLG: 100,
      colorTextPlaceholder: "var(--color-placeholder)",
      paddingSM: 24,
      fontSize: 16,
      optionPadding: "12px 24px",
      optionSelectedColor: "var(--color-main-primary)",
    },
    Form: {
      labelFontSize: 16,
      verticalLabelPadding: "0 0 4px",
      labelColor: "#fff",
      itemMarginBottom: 10,
    },
    Button: {
      defaultHoverColor: "#fff",
      defaultHoverBorderColor: "#fff",
      colorBorder: "rgba(255, 255, 255, 0.5)",
      controlHeightLG: 50,
      colorTextDisabled: "rgba(255, 255, 255, 0.5)",
      borderColorDisabled: "rgba(255, 255, 255, 0.5)",
      borderRadiusLG: 100,
    },
    Spin: {
      contentHeight: 1000,
    },
    Modal: {
      borderRadiusLG: 20,
    },
  },
};
