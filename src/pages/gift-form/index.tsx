// Libraries
import { AnimatePresence } from "motion/react";
import React, { Suspense, useEffect } from "react";
import { useRecoilState } from "recoil";
import styled from "styled-components";

// Components
import { AllocatedVoucher, Banner, GiftForm, Header } from "./components";

// State
import { ConfigProvider } from "@antscorp/ama-ui-v1";
import { giftFormState } from "./state";

// Constants
import { GIFT_FORM_THEME } from "./constants";

interface GiftFormProps {}

const GiftFormContainer = styled.div`
  padding-top: calc(var(--zaui-safe-area-inset-top, 0px) + 44px);
`;

const GiftFormPage: React.FC<GiftFormProps> = (props) => {
  const [formState, setFormState] = useRecoilState(giftFormState);
  const { allocateVoucher } = formState || {};

  useEffect(() => {
    return () => {
      setFormState((draft) => ({
        allocateVoucher: null,
        isShowVoucher: false,
      }));
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ConfigProvider theme={GIFT_FORM_THEME} componentSize="large">
      <GiftFormContainer className="flex flex-col h-full w-full bg-white overflow-hidden">
        <Suspense>
          <Header title="Highlands Coffee" showBackIcon={false} />

          {/* <AnimatePresence
            onExitComplete={() => {
              setTimeout(() => {
                setFormState((draft) => ({
                  ...draft,
                  isShowVoucher: true,
                }));
              }, 100);
            }}
          > */}
          {!allocateVoucher ? (
            <>
              <Banner />
              <GiftForm />
            </>
          ) : (
            <AllocatedVoucher />
          )}
          {/* </AnimatePresence> */}
          {/* <AllocatedVoucher /> */}

          {/* <AnimatePresence mode="wait">
            {isShowVoucher && <AllocatedVoucher />}
          </AnimatePresence> */}
        </Suspense>
      </GiftFormContainer>
    </ConfigProvider>
  );
};

export default GiftFormPage;
