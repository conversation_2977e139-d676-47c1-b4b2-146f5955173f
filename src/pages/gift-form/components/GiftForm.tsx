// Libraries
import * as Sentry from "@sentry/react";
import { MD5, SHA256 } from "crypto-js";
import dayjs from "dayjs";
import { motion } from "motion/react";
import React, { useCallback, useEffect, useMemo } from "react";
import { useRecoilState } from "recoil";
import styled from "styled-components";
import { useImmer } from "use-immer";
import { useLocalStorage } from "usehooks-ts";
import { followOA, getPhoneNumber, getUserInfo } from "zmp-sdk/apis";
import { useNavigate } from "zmp-ui";
import { isEmpty } from "lodash-es";
import { closeApp } from "zmp-sdk";

// Icons
import { CalendarIcon, ChevronDownIcon } from "lucide-react";

// Components
import {
  DatePicker as MobileDatePicker,
  Modal as ModalV2,
} from "@antscorp/ama-ui";
import { Button, Form, Input, Modal, Select, Spin } from "@antscorp/ama-ui-v1";
import { Spinner, SystemNotificationModal, Text } from "components";

// Constants
import { APP_CONFIG, EVENT_CONFIG, LOCAL_STORAGE_KEY, ROUTES } from "constant";

// Utils
import {
  callCdpEvent,
  formatEventGender,
  formatVietnamesePhoneNumber,
} from "utils";

// Hooks
import {
  useAppConfig,
  useDeepCompareCallback,
  useNavigateWithSearch,
  useUserInfo,
} from "hooks";

// Services
import { zaloServices } from "services";

// Queries
import { useCheckUser, useGiftSubmit } from "queries";

// State
import { giftFormState } from "../state";

interface GiftFormProps {}

type TFormValues = {
  phone: string;
  dateOfBirth: any;
  gender: string;
};

type TState = {
  isLoadingPhone?: boolean;
  isModalOpen?: boolean;
  isCanPlay?: boolean;
  isOutOfVoucher?: boolean;
  isLoadingModal?: boolean;
  sessionKey?: string;
  datePickerVisible?: boolean;
};

export const GENDER_OPTIONS = [
  {
    label: "Nam",
    value: "male",
  },
  {
    label: "Nữ",
    value: "female",
  },
  {
    label: "Khác",
    value: "other",
  },
];

const FormContainer = styled(motion.div)`
  background-color: var(--color-main-primary);
  width: 100%;
  height: 100%;
  color: white;

  .zaui-input-label {
    color: white;
    font-size: 16px;
  }

  .zaui-input-affix-wrapper {
    border-radius: 100px;
    height: 50px;
    border: none;
  }

  .ama-picker-input {
    input {
      color: var(--color-main-primary);
    }
  }

  .ama-select-selector {
    border-radius: 100px !important;
  }

  .ama-select-selection-item {
    color: var(--color-main-primary);
  }

  .zaui-picker-input {
    &::placeholder {
      color: var(--color-placeholder);
    }
  }

  .ama-form-item-control {
    border-radius: 100px;
    overflow: hidden;
  }

  @media screen and (max-height: 800px) {
    .form-title {
      font-size: 18px !important;
    }

    .form-description {
      font-size: 12px !important;
    }

    .ama-form-item {
      margin-bottom: 5px !important;

      .ama-form-item-label > label {
        font-size: 14px !important;
      }
    }
  }
`;

export const GiftForm: React.FC<GiftFormProps> = React.memo((props) => {
  const { appSettings } = useAppConfig();
  const { userInfo } = useUserInfo();
  const [form] = Form.useForm();
  const [config, setConfig] = useLocalStorage(LOCAL_STORAGE_KEY.CONFIG, {
    isAcceptRule: false,
    isPhoneNumberAllowed: false,
    lastIdentifyDate: "",
  });
  const [cacheFormValues, setCacheFormValues] = useLocalStorage<{
    phone?: string;
    dateOfBirth?: string;
    gender?: string;
  }>(LOCAL_STORAGE_KEY.GIFT_FORM, {
    phone: undefined,
    dateOfBirth: "1990-01-01",
    gender: undefined,
  });
  const values = Form.useWatch<TFormValues>([], form);
  const navigate = useNavigateWithSearch();

  // State
  const [state, setState] = useImmer<TState>({
    isLoadingPhone: false,
    isModalOpen: false,
    isCanPlay: true,
    isOutOfVoucher: false,
    isLoadingModal: false,
    datePickerVisible: false,
  });
  const [_, setGiftForm] = useRecoilState(giftFormState);

  // Queries
  const { mutateAsync: checkUser, isPending: isCheckUserPending } =
    useCheckUser({});
  const { mutateAsync: giftSubmit, isPending: isGiftSubmitPending } =
    useGiftSubmit({
      options: {
        onError: () => {
          setState((draft) => {
            draft.isOutOfVoucher = true;
            draft.isModalOpen = false;
          });
        },
        onSuccess: ({ code }) => {
          // If the code is not 200, it means that the voucher is out of stock
          if (code !== 200) {
            setTimeout(() => {
              setState((draft) => {
                draft.isOutOfVoucher = true;
                draft.isModalOpen = false;
              });
            }, 1000);
          }
        },
      },
    });

  // Effects
  useEffect(() => {
    if (userInfo?.id) {
      // Set session ID
      setState((draft) => {
        draft.sessionKey = MD5(
          `${userInfo?.id}${dayjs().unix()}session`
        ).toString();
      });

      // Call cdp event
      callCdpEvent({
        data: {
          page_type: "form",
          page_cate: EVENT_CONFIG.NEW_FRIEND,
        },
        uId: userInfo?.id,
      });
    }
  }, [setState, userInfo]);

  useEffect(() => {
    if (!isEmpty(cacheFormValues)) {
      form.setFieldsValue({
        phone: cacheFormValues.phone,
        dateOfBirth: cacheFormValues.dateOfBirth
          ? dayjs(cacheFormValues.dateOfBirth).toDate()
          : undefined,
        gender: cacheFormValues.gender,
      });
    }
  }, [cacheFormValues, form]);

  // Memos
  const isSubmitDisabled = useMemo(() => {
    return !values?.phone || !values?.dateOfBirth || !values?.gender;
  }, [values]);

  const isModalLoading = useMemo(() => {
    return isCheckUserPending || isGiftSubmitPending;
  }, [isCheckUserPending, isGiftSubmitPending]);

  // Variables
  const { lastIdentifyDate, isPhoneNumberAllowed } = config;
  const {
    isModalOpen,
    isCanPlay,
    isOutOfVoucher,
    isLoadingPhone,
    isLoadingModal,
    sessionKey,
    datePickerVisible,
  } = state;
  const { systemErrorMessages } = appSettings?.globals || {};

  // Handlers
  const handleRequestPhonePermission = useCallback(async () => {
    getPhoneNumber({
      async success(res) {
        setState((draft) => {
          draft.isLoadingPhone = true;
        });

        // TODO: Call cdp allow phone number
        const { userInfo } = await getUserInfo();
        const { data } = (await zaloServices.getZaloInfo()) || {};
        const formatPhoneNumber = formatVietnamesePhoneNumber(data?.number);

        if (!isPhoneNumberAllowed) {
          // Call cdp event
          callCdpEvent({
            uId: userInfo.id,
            data: {
              page_type: "allow_phone",
              page_cate: EVENT_CONFIG.NEW_FRIEND,
            },
          });

          setConfig({
            ...config,
            isPhoneNumberAllowed: true,
          });
        }

        form.setFieldsValue({
          phone: formatPhoneNumber,
        });

        setState((draft) => {
          draft.isLoadingPhone = false;
        });

        // Call cdp tracking allow phone
        callCdpEvent({
          ec: "field",
          ea: "input",
          data: {
            field_name: "phone",
            field_value: formatPhoneNumber,
            page_type: "form",
            page_cate: EVENT_CONFIG.NEW_FRIEND,
            session_key: sessionKey,
          },
          uId: userInfo.id,
        });
      },
      fail(err) {
        setState((draft) => {
          draft.isLoadingPhone = false;
        });
      },
    });
  }, [config, form, isPhoneNumberAllowed, sessionKey, setConfig, setState]);

  const onClickPhone = useDeepCompareCallback(async () => {
    // If phone is already allowed then return
    if (values?.phone) {
      return;
    }

    const { userInfo: requestedUserInfo } = await getUserInfo({
      autoRequestPermission: true,
      fail() {},
    });

    if (requestedUserInfo) {
      // TODO: Call CDP event
      if (!requestedUserInfo?.followedOA) {
        await followOA({
          id: APP_CONFIG.OA_ID,
          async success() {
            callCdpEvent({
              uId: requestedUserInfo.id,
              data: {
                page_type: "follow_oa",
                page_cate: EVENT_CONFIG.NEW_FRIEND,
              },
            });

            await handleRequestPhonePermission();
          },
          async fail() {},
        });
      }

      await handleRequestPhonePermission();
    }
  }, [handleRequestPhonePermission, values?.phone]);

  const onFinishSubmit = useDeepCompareCallback(
    async (values: TFormValues) => {
      setState((draft) => {
        draft.isModalOpen = true;
        draft.isLoadingModal = true;
      });

      setCacheFormValues((prev) => ({
        ...prev,
        phone: values.phone,
        dateOfBirth: dayjs(values.dateOfBirth).format("YYYY-MM-DD"),
        gender: values.gender,
      }));

      const { userInfo } = await getUserInfo();

      Sentry.captureMessage(
        `[Gift Form][Submit] User(${userInfo?.name} - ${userInfo?.id}) has submitted form`,
        {
          extra: {
            values,
          },
        }
      );

      // Call api check user can play game
      const { data } = (await checkUser()) || {};

      // If user can't play then show error message
      if (!data?.status) {
        Sentry.captureMessage(
          `[Gift Form][Submit] User(${userInfo?.name} - ${userInfo?.id}) can't play game`,
          {
            extra: {
              values,
            },
          }
        );

        // Call cdp event
        callCdpEvent({
          data: {
            page_type: "old_user",
            page_cate: EVENT_CONFIG.NEW_FRIEND,
          },
          uId: userInfo?.id,
        });

        setState((draft) => {
          draft.isCanPlay = false;
        });

        return;
      }

      const { data: giftSubmitData } = await giftSubmit({
        data: {
          idByOa: `${userInfo.idByOA || ""}`,
          name: `${userInfo.name || ""}`,
          phoneNumber: `${values.phone || ""}`,
        },
      });

      if (giftSubmitData && giftSubmitData.webContents) {
        const gender = formatEventGender(values?.gender);
        const eventData = {
          ec: "lead_form",
          ea: "submit",
          dims: {
            users: {
              user_id: userInfo?.id,
              identify_event: "submit_lead_miniapp",
              identify_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
              id_by_oa: userInfo?.idByOA,
              name: userInfo?.name,
              phone: values?.phone,
              gender,
              birthday: dayjs(values?.dateOfBirth).format("YYYY-MM-DD"),
            },
            customers: {
              name: userInfo?.name,
              email: "",
              phone: values?.phone,
              customer_id: SHA256(values?.phone).toString(),
              zalo_uid: userInfo?.id,
              id_by_oa: userInfo?.idByOA,
              gender,
              birthday: dayjs(values?.dateOfBirth).format("YYYY-MM-DD"),
            },
          },
          items: [
            {
              type: "lead",
              id: MD5(
                `${dayjs().unix()}${values?.phone}new_friend_miniapp`
              ).toString(),
              lead_event: "new_friend_miniapp",
              lead_locate: "miniapp",
              lead_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
              name: userInfo?.name,
              phone: values?.phone,
              gender,
              birthday: dayjs(values?.dateOfBirth).format("YYYY-MM-DD"),
            },
          ],
          data: {
            identify_id: SHA256(values?.phone).toString(),
            identify_event: "submit_lead_miniapp",
            cdp_property_id: 565018501,
          },
        };

        // Call sentry log
        Sentry.captureMessage(
          `[Gift Form][Allocated Voucher] User(${userInfo?.name} - ${userInfo?.id}) has allocated voucher(${giftSubmitData?.webContents?.contents?.name})`,
          {
            level: "info",
            extra: {
              formValues: values,
              cdpEventData: eventData,
            },
          }
        );

        // Call cdp event submit lead
        callCdpEvent(eventData);

        // Call cdp event identifier lead
        if (values.phone) {
          const customerId = SHA256(values.phone).toString();

          callCdpEvent({
            ec: "user",
            ea: "identify",
            uId: userInfo.id,
            dims: {
              users: {
                user_id: userInfo.id,
                identify_event: "allow_miniapp",
                identify_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
                id_by_oa: userInfo.idByOA,
                name: userInfo.name,
                phone: values.phone,
              },
              customers: {
                name: userInfo.name,
                zalo_name: userInfo.name,
                phone: values.phone,
                customer_id: customerId,
                zalo_uid: userInfo.id,
                id_by_oa: userInfo.idByOA,
              },
            },
            data: {
              identify_id: customerId,
              identify_event: "allow_miniapp",
            },
          });
        }

        // Call global tracking
        const { globalTracking, promotion_code } =
          giftSubmitData?.webContents?.contents || {};

        // Call Global Tracking Event
        fetch(globalTracking?.impression);
        fetch(globalTracking?.view);

        callCdpEvent({
          data: {
            page_type: "gift_code",
            page_cate: EVENT_CONFIG.NEW_FRIEND,
          },
          dims: {
            promotion_code: {
              id: promotion_code,
            },
          },
          uId: userInfo?.id,
        });

        setGiftForm((draft) => ({
          ...draft,
          allocateVoucher: giftSubmitData,
        }));
      }

      setState((draft) => {
        draft.isModalOpen = false;
      });
    },
    [checkUser, giftSubmit, setGiftForm, setState, setCacheFormValues]
  );

  // Renders
  const renderModalContent = useCallback(() => {
    if (isModalLoading) {
      return (
        <>
          <Spinner />
          <Text className="text-center !text-[21px] !leading-[28px] text-main-primary">
            Bạn chờ xí nha! <br />
            Highlands đang xử lý
          </Text>
        </>
      );
    }

    // If user can't play then return error message
    if (!isCanPlay) {
      return (
        <>
          <Text className="text-center !text-[21px] !leading-[28px] text-main-primary">
            Úi! Bạn là khách hàng siu thân thiết nên không thể tham gia chương
            trình Bạn Mới. Xem ưu đãi khác nhé.
          </Text>
          <Button
            type="primary"
            block
            onClick={() => navigate("/")}
            className="capitalize"
          >
            Xem quà tặng của tôi
          </Button>
        </>
      );
    }

    return null;
  }, [isCanPlay, isModalLoading, navigate]);

  return (
    <>
      <FormContainer
        initial={{ y: "100%" }}
        animate={{ y: "0%" }}
        exit={{ y: "100%" }}
        transition={{
          duration: 0.5,
          ease: "easeInOut",
          delay: 0.5,
        }}
        className="flex flex-col rounded-t-[30px] p-7 gap-2 h-full overflow-auto"
      >
        <div className="shrink-0 flex flex-col gap-2">
          <Text className="text-center form-title !text-[20px] !leading-[24px] !font-bold">
            Chia sẻ thông tin nhận quà siêu hời
          </Text>
          <Text className="form-description !text-sm">
            Hãy quan tâm và chia sẻ thông tin với Highlands, để chúng mình có
            thể gửi đến bạn nhiều thông tin khuyến mãi, ưu đãi đặc biệt và phục
            vụ bạn tốt hơn nhé
          </Text>
        </div>

        <Form<TFormValues>
          form={form}
          layout="vertical"
          className="flex flex-col h-full overflow-auto gap-2"
          initialValues={{
            phone: "",
            dateOfBirth: dayjs("1990-01-01").toDate(),
            gender: null,
          }}
          onFinish={onFinishSubmit}
        >
          <div className="overflow-auto">
            <Form.Item<TFormValues>
              label="Số điện thoại"
              name="phone"
              className="relative"
            >
              <Spin spinning={isLoadingPhone}>
                <Input
                  value={values?.phone}
                  placeholder="Nhập số điện thoại"
                  readOnly
                  onClick={onClickPhone}
                />
              </Spin>
            </Form.Item>
            <Form.Item<TFormValues> label="Sinh nhật" name="dateOfBirth">
              {/* <DatePicker
                mask
                maskClosable={false}
                dateFormat="dd/mm/yyyy"
                placeholder="DD/MM/YYYY"
                title="Sinh nhật"
                endDate={dayjs().toDate()}
                inputClass="pl-4 pr-3 !text-main-primary"
                suffix={<CalendarIcon size={20} color="black" />}
                action={{
                  close: true,
                  text: "Xác nhận",
                  onClick: () => {
                    // Call cdp tracking event date of birth
                    callCdpEvent({
                      ec: "field",
                      ea: "input",
                      data: {
                        field_name: "birthday",
                        field_value: dayjs(values.dateOfBirth).format(
                          "YYYY-MM-DD"
                        ),
                        page_type: "form",
                        page_cate: EVENT_CONFIG.NEW_FRIEND,
                        session_key: sessionKey,
                      },
                      uId: userInfo.id,
                    });
                  },
                }}
              /> */}
              <div>
                <Input
                  readOnly
                  value={dayjs(values?.dateOfBirth).format("DD/MM/YYYY")}
                  suffix={
                    <CalendarIcon
                      size={20}
                      color="black"
                      onClick={() =>
                        setState((draft) => {
                          draft.datePickerVisible = true;
                        })
                      }
                    />
                  }
                  onClick={() =>
                    setState((draft) => {
                      draft.datePickerVisible = true;
                    })
                  }
                />
              </div>
            </Form.Item>
            <Form.Item<TFormValues> label="Giới tính" name="gender">
              <Select
                placeholder="Nam/Nữ/Khác"
                suffixIcon={<ChevronDownIcon size={20} color="black" />}
                options={GENDER_OPTIONS}
                onSelect={(value) => {
                  // Call cdp tracking event field for gender
                  callCdpEvent({
                    ec: "field",
                    ea: "input",
                    data: {
                      field_name: "gender",
                      field_value: value,
                      page_type: "form",
                      page_cate: EVENT_CONFIG.NEW_FRIEND,
                      session_key: sessionKey,
                    },
                    uId: userInfo?.id,
                  });
                }}
              />
            </Form.Item>
          </div>
          <Form.Item noStyle>
            <Button
              color="default"
              variant="outlined"
              ghost
              htmlType="submit"
              className="mx-auto shrink-0 border-[3px] rounded-3xl w-[100px]"
              disabled={isSubmitDisabled}
            >
              Gửi
            </Button>
          </Form.Item>
        </Form>
      </FormContainer>

      <Modal
        centered
        open={isModalOpen}
        footer={null}
        closable={false}
        width={"80%"}
      >
        <div className="flex flex-col gap-4 items-center py-2">
          {renderModalContent()}
        </div>
      </Modal>

      <SystemNotificationModal
        showCloseButton={false}
        visible={isOutOfVoucher}
        title="Thông báo"
        content={
          systemErrorMessages?.outOfCode ||
          APP_CONFIG.SYSTEM_ERROR_MESSAGES.maintenance
        }
        retryButtonProps={{
          onClick: () => {
            window.location.reload();
          },
        }}
      />

      <MobileDatePicker
        visible={datePickerVisible}
        onClose={() =>
          setState((draft) => {
            draft.datePickerVisible = false;
          })
        }
        onConfirm={(value) => {
          // Call cdp tracking event date of birth
          callCdpEvent({
            ec: "field",
            ea: "input",
            data: {
              field_name: "birthday",
              field_value: dayjs(value).format("YYYY-MM-DD"),
              page_type: "form",
              page_cate: EVENT_CONFIG.NEW_FRIEND,
              session_key: sessionKey,
            },
            uId: userInfo?.id,
          });

          form.setFieldValue("dateOfBirth", value);
        }}
        value={values?.dateOfBirth}
        min={dayjs("1900/01/01").toDate()}
        max={dayjs().toDate()}
        onCancel={() =>
          setState((draft) => {
            draft.datePickerVisible = false;
          })
        }
      />

      <ModalV2
        title="Thông báo"
        getContainer={document.body}
        content={
          <div
            className="text-center"
            dangerouslySetInnerHTML={{
              __html:
                systemErrorMessages?.gameEnd ||
                APP_CONFIG.SYSTEM_ERROR_MESSAGES.gameEnd,
            }}
          />
        }
        onClose={() => closeApp()}
        visible={true}
        closeOnMaskClick={false}
        actions={[
          {
            key: "start",
            text: "Quà tặng của tôi",
            primary: true,
            onClick: () =>
              navigate(ROUTES.GIFT.path, {
                newParams: {
                  tab: "redeemed",
                  voucherType: "voucher",
                },
              }),
          },
        ]}
      />
    </>
  );
});

GiftForm.displayName = "GiftForm";
