// Libraries
import React, { memo } from "react";
import { motion } from "motion/react";
import styled from "styled-components";

// Images
import WelcomeNewUser from "assets/images/backgrounds/welcome-new-user.png";
// import FormWelcome from "assets/images/backgrounds/form-welcome.gif";
// import PrimaryLogo from "assets/images/logos/primary-logo.png";
// import GF1 from "assets/images/others/gf-1.webp";
// import GF2 from "assets/images/others/gf-2.webp";

export const BannerWrapper = styled.div`
  @media screen and (max-height: 800px) {
    .welcome-title {
      height: 140px !important;
    }

    .banner-product-2 {
      width: 80px !important;
      margin-right: 30%;
    }
    .banner-product-1 {
      width: 70px !important;
      margin-left: 40%;
    }
    .banner-logo {
      width: 50px !important;
    }
  }
`;

export const Banner = memo(() => {
  return (
    <BannerWrapper className="flex flex-col shrink-0 gap-8 mb-2">
      <motion.img
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{
          duration: 0.5,
          ease: "easeInOut",
        }}
        src={WelcomeNewUser}
        alt="form-welcome"
        className="mx-auto welcome-title !h-[200px] !w-auto"
      />

      {/* <motion.img
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        src={FormWelcome}
        alt="form-welcome"
        width={"45%"}
        height={"auto"}
        className="mx-auto welcome-title"
      />

      <div className="relative flex items-center justify-center">
        <motion.img
          src={GF2}
          alt="gf-2"
          width={100}
          className="banner-product-2 absolute origin-bottom mr-[40%]"
          initial={{ opacity: 0, transform: "rotate(-90deg) scale(0.5)" }}
          animate={{ opacity: 1, transform: "rotate(-19deg) scale(1)" }}
          exit={{ opacity: 0, transform: "rotate(-90deg) scale(0.5)" }}
          transition={{
            duration: 1,
            delay: 0.5,
            type: "spring",
            stiffness: 150,
          }}
        />
        <motion.img
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
          transition={{
            duration: 1,
            type: "spring",
            stiffness: 150,
          }}
          src={PrimaryLogo}
          width={80}
          height={"auto"}
          alt=""
          className="banner-logo"
        />
        <motion.img
          src={GF1}
          alt="gf-1"
          width={90}
          className="banner-product-1 absolute origin-bottom ml-[45%]"
          initial={{ opacity: 0, transform: "rotate(90deg) scale(0.5)" }}
          animate={{ opacity: 1, transform: "rotate(12deg) scale(1)" }}
          exit={{ opacity: 0, transform: "rotate(90deg) scale(0.5)" }}
          transition={{
            duration: 1,
            delay: 0.75,
            type: "spring",
            stiffness: 150,
          }}
        />
      </div> */}
    </BannerWrapper>
  );
});

Banner.displayName = "Banner";
