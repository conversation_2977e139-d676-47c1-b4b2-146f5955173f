// Libraries
import { motion } from "motion/react";
import React, { memo } from "react";
import BarCode from "react-barcode";
import { useRecoilValue } from "recoil";
import styled from "styled-components";

// Components
import { Text, useNavigate } from "zmp-ui";

// State
import { APP_CONFIG } from "constant";
import { giftFormState } from "../state";

// Utils
import { Button } from "@antscorp/ama-ui-v1";
import { countdownDateExpiration, formatVoucherDateTime } from "utils";

const AllocatedVoucherWrapper = styled(motion.div)`
  display: flex;
  flex-direction: column;

  height: 100%;
  width: 100%;
`;

const Content = styled(motion.div)`
  flex: 1;
  overflow: auto;
`;

const Footer = styled(motion.div)`
  display: flex;
  justify-content: center;
  position: relative;
  height: 140px;
  width: 100%;

  .circle-bg {
    background-color: var(--color-main-primary);
    position: absolute;
    top: -50px;
    width: 800px;
    height: 800px;
    border-radius: 100%;
  }

  .zaui-text {
    color: white;
  }
`;

export const AllocatedVoucher = memo(() => {
  const navigate = useNavigate();
  const { allocateVoucher } = useRecoilValue(giftFormState) || {};
  const {
    name,
    promotion_code,
    description,
    start_date,
    expiry_date,
    image_url,
  } = allocateVoucher?.webContents.contents || {};

  return (
    <AllocatedVoucherWrapper>
      <Content
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{
          duration: 1,
          ease: "easeInOut",
        }}
        className="flex flex-col items-center px-6 gap-3"
      >
        <img
          src={image_url || ""}
          alt=""
          className="w-full h-auto object-cover object-top rounded-[24px]"
        />
        <Text className="text-center !font-bold !text-base">
          Chào bạn mới quen, tặng riêng bạn ưu đãi <br />
          {name}
        </Text>
        <div className="flex items-center justify-center shrink-0 w-full overflow-hidden">
          <BarCode
            fontSize={16}
            fontOptions="bold"
            font="Roboto"
            value={`${promotion_code || ""}`.toUpperCase()}
            height={80}
            width={APP_CONFIG.VOUCHER_DETAIL_BAR_WIDTH}
          />
        </div>
        <div className="flex flex-col gap-1 items-center flex-wrap">
          <Text className="text-description !text-xs">{`Phát hành lúc ${formatVoucherDateTime(
            start_date || ""
          )}`}</Text>
          <div className="flex items-center justify-center">
            <Text
              bold
              className="!text-xs"
            >{`Hạn sử dụng ${formatVoucherDateTime(
              expiry_date || ""
            )} | `}</Text>
            &nbsp;
            <Text
              bold
              className="!text-main-primary !text-xs"
            >{` Hết hạn sau ${countdownDateExpiration(
              expiry_date || ""
            )}`}</Text>
          </div>
        </div>
        <div className="max-h-[500px] min-h-[200px] overflow-auto pb-[50px]">
          <div dangerouslySetInnerHTML={{ __html: description || "" }}></div>
        </div>
      </Content>
      <Footer
        initial={{ opacity: 0, y: "100%" }}
        animate={{ opacity: 1, y: "0" }}
        transition={{
          duration: 1,
          ease: "easeInOut",
        }}
      >
        <motion.div
          initial={{ opacity: 1, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: 0.5,
            ease: "easeInOut",
          }}
          className="circle-bg origin-bottom-left"
        />
        <div className="flex flex-col items-center relative z-50 -mt-4">
          <Text className="!text-xl">Tặng bạn quà nè!</Text>
          <Text className="!text-base">Đến Highlands dùng ngay bạn nha</Text>
          <Button
            color="default"
            variant="outlined"
            ghost
            className="mx-auto shrink-0 border-[3px] rounded-3xl px-6 mt-2"
            onClick={() => navigate("/")}
          >
            Quà tặng của tôi
          </Button>
        </div>
      </Footer>
    </AllocatedVoucherWrapper>
  );
});

AllocatedVoucher.displayName = "AllocatedVoucher";
