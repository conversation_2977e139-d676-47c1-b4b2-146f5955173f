// Libraries
import { motion } from "motion/react";
import React from "react";
import styled from "styled-components";

// Components
import { Header as ZMPHeader } from "zmp-ui";
import { HeaderProps as ZMPHeaderProps } from "zmp-ui/header";

interface HeaderProps extends ZMPHeaderProps {}

const StyledHeader = styled(ZMPHeader)`
  background: white;
  padding: 40px 24px 16px;

  .zaui-header-title {
    font-size: 16px;
    color: var(--color-main-primary);
    max-width: var(--max-width);
    margin: auto;
  }

  &::after {
    display: none;
  }
`;

export const Header: React.FC<HeaderProps> = React.memo((props) => {
  const { ...restProps } = props;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
      className="shrink-0"
    >
      <StyledHeader {...restProps} />
    </motion.div>
  );
});

Header.displayName = "Header";
