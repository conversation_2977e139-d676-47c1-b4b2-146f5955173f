import { OfferCard } from 'components';

export const RestaurantOffers = () => {
  const offers = [
    {
      id: 1,
      restaurantName: 'Gogi',
      title: '<PERSON><PERSON> tặng 100.000 suất buffet 0đ',
      expiryDate: '15/11/2021',
      gCoinReward: 30000,
      heroImage: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=140&fit=crop',
    },
    {
      id: 2,
      restaurantName: 'Gogi',
      title: 'Su<PERSON> Nộ<PERSON> tặng 100.000 suất buffet 0đ',
      expiryDate: '15/11/2021',
      gCoinReward: 30000,
      heroImage: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=140&fit=crop',
    },
    {
      id: 3,
      restaurantName: 'Gogi',
      title: '<PERSON><PERSON>à Nộ<PERSON> tặng 100.000 suất buffet 0đ',
      expiryDate: '15/11/2021',
      gCoinReward: 30000,
      heroImage: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=140&fit=crop',
    },
    {
      id: 4,
      restaurantName: 'Gogi',
      title: 'Sumo Hà Nội tặng 100.000 suất buffet 0đ',
      expiryDate: '15/11/2021',
      gCoinReward: 30000,
      heroImage: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=140&fit=crop',
    },
  ];

  return (
    <div className="flex flex-col gap-6 overflow-y-auto px-4 py-3">
      {offers.map((offer) => (
        <OfferCard
          key={offer.id}
          expiryDate={offer.expiryDate}
          gCoinReward={offer.gCoinReward}
          heroImage={offer.heroImage}
          restaurantName={offer.restaurantName}
          title={offer.title}
        />
      ))}
    </div>
  );
};
