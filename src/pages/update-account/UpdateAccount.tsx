// Libraries
import { isEmpty } from "lodash-es";
import { motion } from "motion/react";
import React, { useCallback, useEffect, useMemo } from "react";
import styled from "styled-components";
import { useImmer } from "use-immer";
import { useDebounceCallback, useToggle } from "usehooks-ts";

// Components
import {
  ActionSheet,
  Button,
  Card,
  Cascader,
  CascaderView,
  DatePicker,
  Form,
  Input,
  Modal,
  Picker,
  Radio,
  Space,
} from "@antscorp/ama-ui";
import {
  ConditionalRenderer,
  InputPicker,
  SearchBar,
  SuccessIcon,
} from "components";

// Icons
import { InfoIcon } from "lucide-react";

// Assets
import favoriteStoresJson from "assets/json/favoriteStores.json";
import favoriteDishesJson from "assets/json/favoriteDishes.json";

// Hooks
import {
  useAppConfig,
  useDeepCompareEffect,
  useSyncCustomer,
  useUserInfo,
  useViewPage,
} from "hooks";

// Queries
import { useGetVietNamProvinceList, useUpdateLoyaltyCustomer } from "queries";

// Utils
import { searchStringQuery, dayjs } from "utils";

interface UpdateAccountProps {}

type TState = {
  isChanged: boolean;
  searches: string[];
  favoriteStoreCurrentTabIdx: number;
  tempFavoriteStore: any[];
};

const StyledCascaderView = styled(CascaderView)`
  .adm-cascader-view-tabs {
    overflow: auto;
    display: flex;
    flex-direction: column;
    height: 350px;

    .adm-tabs-content {
      height: 100%;
      overflow: auto;
    }
  }
`;

export const UpdateAccount: React.FC<UpdateAccountProps> = () => {
  // Hooks
  const [form] = Form.useForm();
  const { appSettings } = useAppConfig();
  useSyncCustomer();
  useViewPage({
    pageType: "edit_account",
  });

  const {
    userInfo,
    loyaltyCustomer,
    isLoading: isLoadingUserInfo,
  } = useUserInfo();
  const provinceValue = Form.useWatch(["addressDetailInfo", "state"], form);
  const districtValue = Form.useWatch(["addressDetailInfo", "district"], form);
  const detail1Value = Form.useWatch(["addressDetailInfo", "detail1"], form);
  const favoriteStore = Form.useWatch(["favoriteStore"], form);
  const dateOfBirthValue = Form.useWatch(["dateOfBirth"], form);

  const [state, setState] = useImmer<TState>({
    isChanged: false,
    searches: ["", ""],
    favoriteStoreCurrentTabIdx: 0,
    tempFavoriteStore: [],
  });
  const [favoriteStorePickerVisible, toggleFavoriteStorePicker] =
    useToggle(false);

  const { isChanged, favoriteStoreCurrentTabIdx, searches, tempFavoriteStore } =
    state;
  const { data: vietNamProvinceListData, isLoading: isLoadingVietNamProvince } =
    useGetVietNamProvinceList();

  const vietnamProvinces = useMemo(() => {
    return vietNamProvinceListData?.data || [];
  }, [vietNamProvinceListData]);

  const formattedFormValues = useCallback(() => {
    if (!isEmpty(userInfo) && !isEmpty(loyaltyCustomer)) {
      const {
        customerName,
        favoriteDish,
        favoriteStore,
        addressDetailInfo,
        gender,
        dateOfBirth,
      } = loyaltyCustomer || {};

      return {
        phoneNumber: userInfo.phoneNumber,
        customerName: userInfo.name || customerName,
        gender,
        dateOfBirth: dayjs(dateOfBirth, "DD/MM/YYYY").isValid()
          ? dayjs(dateOfBirth, "DD/MM/YYYY").toDate()
          : undefined,
        addressDetailInfo: {
          state: addressDetailInfo?.state
            ? [addressDetailInfo?.state]
            : undefined,
          district: addressDetailInfo?.district
            ? [addressDetailInfo?.district]
            : undefined,
          detail1: addressDetailInfo?.detail1
            ? [addressDetailInfo?.detail1]
            : undefined,
          detail2: addressDetailInfo?.detail2,
        },
        favoriteStore: `${favoriteStore || ""}`.split(" - ") || [],
        favoriteDish: `${favoriteDish || ""}`.split(" - ") || [],
      };
    }

    return {};
  }, [loyaltyCustomer, userInfo]);

  // Queries
  const { mutateAsync: updateLoyaltyCustomer, isPending: isUpdatePending } =
    useUpdateLoyaltyCustomer({
      options: {
        onSuccess(data) {
          if (data.code === 200) {
            Modal.alert({
              header: <SuccessIcon size={46} className="text-success" />,
              content: (
                <div className="text-center mt-5">
                  Bạn đã cập nhật tài khoản thành công
                </div>
              ),
              confirmText: "Đóng",
            });
          } else {
            form.setFieldsValue(formattedFormValues());
            Modal.alert({
              header: <InfoIcon size={46} className="text-error" />,
              content: (
                <div className="text-center mt-5">
                  Cập nhật tài khoản thất bại
                </div>
              ),
              confirmText: "Đóng",
            });
          }
        },
      },
    });

  // Variables
  const { favoriteDishList, favoriteStoreList } =
    appSettings?.globals?.loyalty || {};

  // Effects
  useDeepCompareEffect(() => {
    const values = formattedFormValues();

    if (!isEmpty(values)) {
      form.setFieldsValue(values);
    }
  }, [form, formattedFormValues]);

  // Memos
  const provinces = useMemo(() => {
    return vietnamProvinces.map((item) => ({
      label: item.name,
      value: item.name,
      key: item.name,
    }));
  }, [vietnamProvinces]);

  const districts = useMemo(() => {
    return vietnamProvinces
      .find((item) => provinceValue?.includes(item.name))
      ?.districts.map((item) => ({
        label: item.name,
        value: item.name,
        key: item.name,
      }));
  }, [provinceValue, vietnamProvinces]);

  const wards = useMemo(() => {
    return vietnamProvinces
      .find((item) => provinceValue?.includes(item.name))
      ?.districts.find((district) => districtValue?.includes(district.name))
      ?.wards.map((item) => ({
        label: item.name,
        value: item.name,
        key: item.name,
      }));
  }, [vietnamProvinces, provinceValue, districtValue]);

  const favoriteStores = useMemo(() => {
    return (favoriteStoreList || favoriteStoresJson)
      .filter((item) => {
        if (favoriteStoreCurrentTabIdx === 0) {
          return searchStringQuery(item.value, searches[0]);
        }

        return true;
      })
      ?.map((item) => {
        let children = item.children;

        if (favoriteStoreCurrentTabIdx === 1) {
          children = children.filter((child) => {
            return searchStringQuery(child.value, searches[1]);
          });
        }

        return {
          ...item,
          children,
        };
      });
  }, [favoriteStoreCurrentTabIdx, favoriteStoreList, searches]);

  const favoriteDishes = useMemo(() => {
    return favoriteDishList || favoriteDishesJson;
  }, [favoriteDishList]);

  // Handlers
  const onFinishSubmit = useCallback(
    async (values) => {
      const {
        customerName,
        gender,
        addressDetailInfo,
        favoriteDish,
        favoriteStore,
        dateOfBirth,
      } = values || {};

      const updateData = {
        customerName,
        gender,
        addressDetailInfo: {
          ...Object.entries(addressDetailInfo).reduce((acc, item: any) => {
            return {
              ...acc,
              [item[0]]: Array.isArray(item[1]) ? item[1]?.[0] : item[1],
            };
          }, {}),
        } as any,
        favoriteDish: favoriteDish?.join(" - "),
        favoriteStore: favoriteStore?.join(" - "),
        ...(dateOfBirth && {
          dateOfBirth: dayjs(dateOfBirth).format("DD/MM/YYYY"),
        }),
      };

      await updateLoyaltyCustomer({
        id: userInfo?.phoneNumber,
        data: updateData,
      });

      setState((draft) => {
        draft.isChanged = false;
      });
    },
    [setState, updateLoyaltyCustomer, userInfo?.phoneNumber]
  );

  const handleUpdateChanged = useCallback(() => {
    setState((draft) => {
      draft.isChanged = true;
    });
  }, [setState]);

  const onCloseFavoriteStorePicker = useCallback(() => {
    setState((draft) => {
      draft.searches = ["", ""];
    });

    toggleFavoriteStorePicker();
  }, [setState, toggleFavoriteStorePicker]);

  const debouncedSearchFavoriteStore = useDebounceCallback((value) => {
    setState((draft) => {
      draft.searches[favoriteStoreCurrentTabIdx] = value;
    });
  }, 500);

  const onClickSubmit = useCallback(async () => {
    form.submit();
  }, [form]);

  return (
    <>
      <div className="p-layout h-full flex flex-col gap-5 overflow-auto">
        <Card className="overflow-auto !px-0">
          <ConditionalRenderer
            isLoading={isLoadingUserInfo || isLoadingVietNamProvince}
          >
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
              <Form
                form={form}
                initialValues={{}}
                onFinish={onFinishSubmit}
                onValuesChange={handleUpdateChanged}
              >
                <Form.Item
                  name="customerName"
                  label="Họ tên"
                  rules={[{ required: true }, { max: 254 }]}
                >
                  <Input
                    disabled
                    maxLength={254}
                    onBlur={(e) => {
                      const trimmedValue = e.target.value.trim();
                      form.setFieldsValue({ customerName: trimmedValue });
                    }}
                  />
                </Form.Item>
                <Form.Item name="gender" label="Giới tính">
                  <Radio.Group>
                    <Space className="py-1" style={{ "--gap": "20px" }}>
                      <Radio value="MALE">Nam</Radio>
                      <Radio value="FEMALE">Nữ</Radio>
                      <Radio value="PREFER_NOT_TO_SAY">Khác</Radio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
                <div>
                  <Form.Item name="dateOfBirth" label="Ngày sinh">
                    <DatePicker
                      value={dateOfBirthValue}
                      onConfirm={(value) => {
                        handleUpdateChanged();

                        form.setFieldValue("dateOfBirth", value);
                      }}
                      defaultValue={dayjs("1990/01/01").toDate()}
                      min={dayjs("1900/01/01").toDate()}
                      max={dayjs().toDate()}
                    >
                      {(value, actions) => (
                        <InputPicker
                          readOnly
                          disabled={!!loyaltyCustomer?.dateOfBirth}
                          value={
                            dateOfBirthValue
                              ? dayjs(dateOfBirthValue).format("DD/MM/YYYY")
                              : ""
                          }
                          placeholder="Chọn ngày sinh"
                          onClick={actions.open}
                        />
                      )}
                    </DatePicker>
                    {!loyaltyCustomer?.dateOfBirth && (
                      <div className="text-[10px] text-text-secondary mt-1">
                        *Lưu ý: Voucher sinh nhật là quyền lợi của bạn, hãy cập
                        nhật đúng vì nó không thể chỉnh sửa nha.
                      </div>
                    )}
                  </Form.Item>
                </div>
                <Form.Item name="phoneNumber" label="Số điện thoại">
                  <Input type="number" readOnly disabled />
                </Form.Item>
                <Form.Item
                  name={["addressDetailInfo", "state"]}
                  label="Thành phố / Tỉnh"
                  rules={[{ required: true }]}
                >
                  <Picker
                    columns={[provinces]}
                    onConfirm={(values) => {
                      handleUpdateChanged();
                      form.setFieldValue(
                        ["addressDetailInfo", "state"],
                        values
                      );
                      form.setFieldValue(
                        ["addressDetailInfo", "district"],
                        undefined
                      );
                      form.setFieldValue(
                        ["addressDetailInfo", "detail1"],
                        undefined
                      );
                      form.validateFields(["addressDetailInfo", "state"], {
                        recursive: true,
                      });
                    }}
                  >
                    {(values, actions) => {
                      return (
                        <InputPicker
                          readOnly
                          value={`${values[0]?.label || ""}`}
                          onClick={actions.open}
                        />
                      );
                    }}
                  </Picker>
                </Form.Item>
                <div className="grid grid-cols-2 gap-4">
                  <Form.Item
                    name={["addressDetailInfo", "district"]}
                    label="Quận / Huyện"
                    rules={[{ required: true }]}
                  >
                    <Picker
                      columns={[districts || []]}
                      onConfirm={(values) => {
                        handleUpdateChanged();
                        form.setFieldValue(
                          ["addressDetailInfo", "district"],
                          values
                        );
                        form.setFieldValue(
                          ["addressDetailInfo", "detail1"],
                          undefined
                        );
                        form.validateFields(["addressDetailInfo", "district"], {
                          recursive: true,
                        });
                      }}
                    >
                      {(values, actions) => (
                        <InputPicker
                          readOnly
                          disabled={!districts}
                          value={districtValue?.[0] || ""}
                          onClick={() => {
                            actions.open();
                          }}
                        />
                      )}
                    </Picker>
                  </Form.Item>
                  <Form.Item
                    name={["addressDetailInfo", "detail1"]}
                    label="Phường"
                    rules={[{ required: true }]}
                  >
                    <Picker
                      columns={[wards || []]}
                      onConfirm={(values) => {
                        handleUpdateChanged();
                        form.setFieldValue(
                          ["addressDetailInfo", "detail1"],
                          values
                        );
                        form.validateFields(["addressDetailInfo", "detail1"], {
                          recursive: true,
                        });
                      }}
                    >
                      {(values, actions) => (
                        <InputPicker
                          value={detail1Value?.[0] || ""}
                          readOnly
                          disabled={!wards}
                          onClick={actions.open}
                        />
                      )}
                    </Picker>
                  </Form.Item>
                </div>
                <Form.Item
                  name={["addressDetailInfo", "detail2"]}
                  label="Địa chỉ"
                  rules={[{ required: true }, { max: 254 }]}
                >
                  <Input
                    maxLength={254}
                    onBlur={(e) => {
                      const trimmedValue = e.target.value.trim();
                      form.setFieldValue(
                        ["addressDetailInfo", "detail2"],
                        trimmedValue
                      );
                    }}
                  />
                </Form.Item>
                <Form.Item name="favoriteDish" label="Món yêu thích">
                  <Cascader
                    options={favoriteDishes}
                    onConfirm={(items) => {
                      handleUpdateChanged();
                      // If favoriteDish is not selected
                      if (items[1]) {
                        form.setFieldsValue({
                          favoriteDish: items,
                        });
                      }
                    }}
                    placeholder="Vui lòng chọn"
                  >
                    {(items, actions) => (
                      <InputPicker
                        readOnly
                        value={`${
                          items.map((item) => item?.label).join(" - ") || ""
                        }`}
                        onClick={actions.open}
                      />
                    )}
                  </Cascader>
                </Form.Item>
                <Form.Item name="favoriteStore" label="Cửa hàng yêu thích">
                  <div>
                    <InputPicker
                      value={`${favoriteStore?.[1] || ""}`}
                      readOnly
                      onClick={toggleFavoriteStorePicker}
                    />
                  </div>
                </Form.Item>
              </Form>
            </motion.div>
          </ConditionalRenderer>
        </Card>

        <Button
          color="primary"
          shape="rounded"
          onClick={onClickSubmit}
          disabled={!isChanged}
          loading={isUpdatePending}
        >
          Cập nhật
        </Button>
      </div>

      <ActionSheet
        visible={favoriteStorePickerVisible}
        actions={[]}
        extra={
          <div className="flex flex-col gap-2.5 w-full text-text-primary">
            <div className="flex items-center justify-between w-full">
              <div onClick={onCloseFavoriteStorePicker}>Huỷ</div>
              <div
                onClick={() => {
                  if (tempFavoriteStore?.[1]) {
                    form.setFieldValue("favoriteStore", tempFavoriteStore);
                    handleUpdateChanged();
                  }

                  toggleFavoriteStorePicker();
                  setState((draft) => {
                    draft.searches = ["", ""];
                  });
                }}
              >
                Xác nhận
              </div>
            </div>
            <SearchBar
              key={`${favoriteStoreCurrentTabIdx}`}
              defaultValue={searches[favoriteStoreCurrentTabIdx] || ""}
              placeholder="Tìm kiếm..."
              onChange={debouncedSearchFavoriteStore}
            />
            <StyledCascaderView
              key={`${favoriteStorePickerVisible}`}
              defaultValue={favoriteStore}
              options={favoriteStores}
              onTabsChange={(index) => {
                setState((draft) => {
                  draft.favoriteStoreCurrentTabIdx = index;
                });
              }}
              tabIndex={1}
              onChange={(items) => {
                // Check if when in first tab and change store, then reset store search
                if (favoriteStoreCurrentTabIdx === 0) {
                  setState((draft) => {
                    draft.searches[1] = "";
                  });
                }

                setState((draft) => {
                  draft.tempFavoriteStore = items;
                });
              }}
            />
          </div>
        }
        onClose={onCloseFavoriteStorePicker}
      ></ActionSheet>
    </>
  );
};
