// Libraries
import React from 'react';
import {
  TransactionHistoryCard,
  TransactionHistoryItem,
} from './components/TransactionHistoryCard';

// Components

interface TransactionHistoryProps {}

const mockTransactionHistory: TransactionHistoryItem[] = [
  {
    transactionId: '1',
    storeName: 'Ashima <PERSON>iang <PERSON>',
    date: '2025-12-22T19:20:21Z',
    points: 68805,
    transactionAmount: 1200000,
    accumulatedAmount: 1100000,
  },
  {
    transactionId: '2',
    storeName: 'Gogi Steak House 2B Ngô Thì Nhậm',
    date: '2025-12-22T19:20:21Z',
    points: 68805,
    transactionAmount: 1200000,
    accumulatedAmount: 1100000,
  },
  {
    transactionId: '3',
    storeName: 'Manwah Thái Hà',
    date: '2025-12-22T19:20:21Z',
    points: 68805,
    transactionAmount: 1200000,
    accumulatedAmount: 1100000,
  },
  {
    transactionId: '4',
    storeName: 'Kichi Kichi Royal City',
    date: '2025-12-22T19:20:21Z',
    points: 68805,
    transactionAmount: 1200000,
    accumulatedAmount: 1100000,
  },
  {
    transactionId: '5',
    storeName: 'Shogun Royal City',
    date: '2025-12-22T19:20:21Z',
    points: 68805,
    transactionAmount: 1200000,
    accumulatedAmount: 1100000,
  },
];

export const TransactionHistories: React.FC<TransactionHistoryProps> = () => {
  return (
    <div className="flex flex-col gap-2 bg-[#e9ebed]">
      {mockTransactionHistory.map((transaction) => (
        <TransactionHistoryCard key={transaction.transactionId} transaction={transaction} />
      ))}
    </div>
  );
};
