import React, { useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
import { Divider } from '@antscorp/ama-ui';

// Mock data based on Figma design
const mockTransactionDetail = {
  transactionId: '4297181',
  date: '28/10/2021 - 19:20:21',
  restaurant: {
    name: '<PERSON><PERSON> Đà<PERSON>',
    address: 'Dãy A - TT3, Tây Nam Linh Đàm, Hoàng Liệt, Hoàng M...',
    phone: '[SĐT nhà hàng]',
    customerId: '29682242',
    logo: '<PERSON>gi', // This would be the logo identifier
  },
  financial: {
    totalAmount: 800000,
    serviceFee: 0,
    discount: 0,
    subtotal: 800000,
    vat: 80000,
    totalAfterVat: 880000,
    voucher: 0,
    finalAmount: 880000,
  },
  items: [
    '1x Sườn non bò Mỹ hảo hạng',
    '1x Đùi dê ướp sốt Hutong',
    '1x Bạch tuộc baby',
    '1x Tôm cân đẩu vân',
    '1x Set menu An Khang',
  ],
  payment: {
    method: 'Cá Nhân',
  },
  accumulation: {
    gCoin: 68805,
    rankingPoints: 880000,
  },
};

const Container = styled.div`
  background-color: #e9ebed;
  min-height: 100vh;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const Card = styled.div`
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
`;

const RestaurantInfo = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
`;

const RestaurantLogo = styled.div`
  width: 32px;
  height: 32px;
  background-color: #001a33;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
`;

const RestaurantContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const RestaurantName = styled.div`
  font-family: Roboto;
  font-weight: 500;
  font-size: 15px;
  line-height: 1.47;
  color: #001a33;
`;

const RestaurantAddress = styled.div`
  font-family: Roboto;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
  color: #667685;
`;

const HotlineRow = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const HotlineLabel = styled.span`
  font-family: Roboto;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
  color: #667685;
`;

const HotlineNumber = styled.span`
  font-family: Roboto;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.5;
  color: #0068ff;
`;

const CustomerIdText = styled.div`
  font-family: Roboto;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
  color: #667685;
  width: 258px;
  text-align: left;
`;

const SectionContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px 16px;
`;

const SectionTitle = styled.div`
  font-family: Roboto;
  font-weight: 500;
  font-size: 17px;
  line-height: 1.53;
  color: #001a33;
  width: 343px;
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 343px;
`;

const DetailLabel = styled.div`
  font-family: Roboto;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.47;
  color: #667685;
  flex: 1;
`;

const DetailValue = styled.div`
  font-family: Roboto;
  font-weight: 500;
  font-size: 15px;
  line-height: 1.47;
  color: #001a33;
  text-align: right;
  flex-shrink: 0;
`;

const DetailValueOrange = styled(DetailValue)`
  color: #f5832f;
`;

const DetailValueGreen = styled(DetailValue)`
  color: #00c578;
`;

const BoldRow = styled(DetailRow)`
  ${DetailLabel} {
    font-weight: 500;
    color: #001a33;
  }

  ${DetailValue} {
    font-weight: 500;
    font-size: 17px;
    line-height: 1.53;
    color: #001a33;
  }
`;

const BoldRowOrange = styled(BoldRow)`
  ${DetailValue} {
    color: #f5832f;
  }
`;

const ItemList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const ItemRow = styled.div`
  width: 343px;
  display: flex;
  align-items: stretch;
`;

const ItemText = styled.div`
  font-family: Roboto;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.47;
  color: #667685;
  flex: 1;
`;

const ViewMoreButton = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  padding: 8px 0;
  cursor: pointer;
`;

const ViewMoreText = styled.div`
  font-family: Roboto;
  font-weight: 500;
  font-size: 15px;
  line-height: 1.47;
  color: #0068ff;
  text-align: center;
`;

const ArrowIcon = styled.div`
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const StyledDivider = styled(Divider)`
  margin: 0;
  width: 343px;
  height: 1px;
  background-color: #e9ebed;
`;

const formatCurrency = (amount: number): string => {
  return `${amount.toLocaleString('vi-VN')} vnđ`;
};

const formatGCoin = (amount: number): string => {
  return `+${amount.toLocaleString('vi-VN')}`;
};

export const TransactionHistoryDetail: React.FC = () => {
  const { transactionId } = useParams<{ transactionId: string }>();
  const [showAllItems, setShowAllItems] = useState(false);

  const displayedItems = useMemo(() => {
    if (showAllItems) {
      return mockTransactionDetail.items;
    }
    return mockTransactionDetail.items.slice(0, 3);
  }, [showAllItems]);

  const hasMoreItems = mockTransactionDetail.items.length > 3;

  return (
    <Container>
      {/* Restaurant Info Card */}
      <Card>
        <RestaurantInfo>
          <RestaurantLogo>{mockTransactionDetail.restaurant.logo.charAt(0)}</RestaurantLogo>
          <RestaurantContent>
            <RestaurantName>{mockTransactionDetail.restaurant.name}</RestaurantName>
            <RestaurantAddress>{mockTransactionDetail.restaurant.address}</RestaurantAddress>
            <HotlineRow>
              <HotlineLabel>Hotline:</HotlineLabel>
              <HotlineNumber>{mockTransactionDetail.restaurant.phone}</HotlineNumber>
            </HotlineRow>
            <CustomerIdText>Mã KH: {mockTransactionDetail.restaurant.customerId}</CustomerIdText>
          </RestaurantContent>
        </RestaurantInfo>
      </Card>

      {/* Transaction Details Card */}
      <Card>
        <SectionContainer>
          <SectionTitle>Chi tiết</SectionTitle>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <DetailRow>
              <DetailLabel>Mã đơn hàng</DetailLabel>
              <DetailValue>{mockTransactionDetail.transactionId}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Thời gian</DetailLabel>
              <DetailValue>{mockTransactionDetail.date}</DetailValue>
            </DetailRow>
          </div>

          <StyledDivider />

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <BoldRow>
              <DetailLabel>Tổng tiền hàng</DetailLabel>
              <DetailValue>
                {formatCurrency(mockTransactionDetail.financial.totalAmount)}
              </DetailValue>
            </BoldRow>
            <DetailRow>
              <DetailLabel>Phí dịch vụ</DetailLabel>
              <DetailValue>
                {formatCurrency(mockTransactionDetail.financial.serviceFee)}
              </DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Giảm giá</DetailLabel>
              <DetailValue>{formatCurrency(mockTransactionDetail.financial.discount)}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Thành tiền</DetailLabel>
              <DetailValue>{formatCurrency(mockTransactionDetail.financial.subtotal)}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>VAT (10%)</DetailLabel>
              <DetailValue>{formatCurrency(mockTransactionDetail.financial.vat)}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Tổng tiền sau VAT</DetailLabel>
              <DetailValue>
                {formatCurrency(mockTransactionDetail.financial.totalAfterVat)}
              </DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Voucher</DetailLabel>
              <DetailValue>{formatCurrency(mockTransactionDetail.financial.voucher)}</DetailValue>
            </DetailRow>
            <BoldRowOrange>
              <DetailLabel>Tổng tiền thanh toán</DetailLabel>
              <DetailValue>
                {formatCurrency(mockTransactionDetail.financial.finalAmount)}
              </DetailValue>
            </BoldRowOrange>
          </div>
        </SectionContainer>

        {/* Ordered Items Section */}
        <SectionContainer>
          <SectionTitle>Món đã gọi</SectionTitle>
          <ItemList>
            {displayedItems.map((item, index) => (
              <ItemRow key={index}>
                <ItemText>{item}</ItemText>
              </ItemRow>
            ))}
          </ItemList>
          {hasMoreItems && (
            <ViewMoreButton onClick={() => setShowAllItems(!showAllItems)}>
              <ViewMoreText>{showAllItems ? 'Thu gọn' : 'Xem thêm'}</ViewMoreText>
              <ArrowIcon>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M2.72 5.97L8 10.56L13.28 5.97"
                    stroke="#0068FF"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    transform={showAllItems ? 'rotate(180 8 8)' : ''}
                  />
                </svg>
              </ArrowIcon>
            </ViewMoreButton>
          )}
        </SectionContainer>

        {/* Payment Section */}
        <SectionContainer>
          <SectionTitle>Thanh toán</SectionTitle>
          <DetailRow>
            <DetailLabel>Hình thức thanh toán</DetailLabel>
            <DetailValue>{mockTransactionDetail.payment.method}</DetailValue>
          </DetailRow>
        </SectionContainer>

        {/* Accumulation Section */}
        <SectionContainer>
          <SectionTitle>Tích lũy</SectionTitle>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <DetailRow>
              <DetailLabel>Tích lũy G-coin</DetailLabel>
              <DetailValueGreen>
                {formatGCoin(mockTransactionDetail.accumulation.gCoin)}
              </DetailValueGreen>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Tích lũy thăng hạng</DetailLabel>
              <DetailValue>
                {formatCurrency(mockTransactionDetail.accumulation.rankingPoints)}
              </DetailValue>
            </DetailRow>
          </div>
        </SectionContainer>
      </Card>
    </Container>
  );
};

export default TransactionHistoryDetail;
