// Libraries
import React, { memo, useMemo } from 'react';
import dayjs from 'dayjs';

// Components
import { Divider } from '@antscorp/ama-ui';
import { useNavigate } from 'react-router-dom';
import GoldCoinIcon from 'assets/svg/gcoin.svg?react';

export interface TransactionHistoryItem {
  transactionId: string;
  storeName: string;
  date: string;
  points: number;
  transactionAmount: number;
  accumulatedAmount: number;
}

interface TransactionHistoryCardProps {
  transaction: TransactionHistoryItem;
}

export const TransactionHistoryCard: React.FC<TransactionHistoryCardProps> = memo((props) => {
  const { transaction } = props;

  const navigate = useNavigate();

  const { points = 0, storeName, transactionAmount, accumulatedAmount, date } = transaction || {};

  const formattedDate = useMemo(() => {
    if (!date) return '--';
    return dayjs(date).format('DD/MM/YYYY - HH:mm:ss');
  }, [date]);

  const formattedTransactionAmount = useMemo(() => {
    return `${(transactionAmount || 0).toLocaleString('vi-VN')}đ`;
  }, [transactionAmount]);

  const formattedAccumulatedAmount = useMemo(() => {
    return `${(accumulatedAmount || 0).toLocaleString('vi-VN')}đ`;
  }, [accumulatedAmount]);

  const formattedPoints = useMemo(() => {
    return `+ ${points.toLocaleString('vi-VN')}`;
  }, [points]);

  return (
    <div
      className="cursor-pointer bg-white"
      onClick={() => navigate(`/transaction-histories/${transaction.transactionId}`)}
    >
      <div className="flex items-center justify-between px-4 py-3">
        <div className="flex items-center gap-[6px] font-medium leading-[22px] text-success">
          <span>{formattedPoints}</span>

          <GoldCoinIcon width={16} height={16} />
        </div>

        <div className="text-xs font-normal leading-[18px] text-text-secondary">
          {formattedDate}
        </div>
      </div>

      <Divider style={{ margin: 0 }} />

      <div className="px-4 py-3">
        <div className="flex items-center gap-2.5">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#001a33] font-bold text-white">
            {storeName.charAt(0).toUpperCase()}
          </div>

          <div className="font-medium leading-[22px] text-[#001a33]">
            {storeName || 'Cửa hàng không xác định'}
          </div>
        </div>

        <div className="mt-2 flex justify-between gap-4 font-normal leading-[22px] text-text-secondary">
          <span>Tổng tiền: {formattedTransactionAmount}</span>
          <span>Tích luỹ: {formattedAccumulatedAmount}</span>
        </div>
      </div>
    </div>
  );
});

TransactionHistoryCard.displayName = 'TransactionHistoryCard';
