// Libraries
import React from "react";

// Hooks
import { useViewPage } from "hooks";

// Constants
import { EVENT_CONFIG } from "constant";

interface SurveyOrderProps {}

export const SurveyOrder: React.FC<SurveyOrderProps> = (props) => {
  useViewPage({
    pageType: "survey_order",
    pageCate: EVENT_CONFIG.APP_LOYALTY,
  });

  return (
    <div className="flex flex-col items-center justify-center w-full h-full">
      <h1 className="text-2xl font-bold mb-4 text-main-primary">Khảo sát</h1>
      <p className="text-center">
        Chúng tôi rất mong nhận được ý kiến đóng góp của bạn!
      </p>
    </div>
  );
};
