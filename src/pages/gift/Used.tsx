// Libraries
import React, { memo } from "react";
import { useDebounceCallback } from "usehooks-ts";

// Components
import { MemberRegistration, SearchBar } from "components";
import { VoucherList } from "./components/VoucherList";

// Hooks
import { useViewPage } from "hooks";

// Constants
import { EVENT_CONFIG } from "constant";

interface UsedProps {}

export const Used: React.FC<UsedProps> = memo(() => {
  // Hooks
  useViewPage({
    pageType: "my_gift_used",
    pageCate: EVENT_CONFIG.MY_GIFT_CATE,
  });

  const [search, setSearch] = React.useState("");

  const debounceChangeSearch = useDebounceCallback(
    (value: string) => setSearch(value),
    500
  );

  return (
    <div className="flex flex-col h-full items-center gap-[15px]">
      <SearchBar className="w-full" onChange={debounceChangeSearch} />
      <VoucherList voucherStatus="used" search={search} />
    </div>
  );
});

Used.displayName = "Used";
