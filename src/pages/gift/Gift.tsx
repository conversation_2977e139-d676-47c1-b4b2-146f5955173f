// Libraries
import { motion } from "motion/react";
import React from "react";
import styled from "styled-components";

// Components
import { Tabs } from "@antscorp/ama-ui";

// Sections
import { Offer } from "./Offer";
import { Redeemed } from "./Redeemed";
import { Used } from "./Used";
import { useTabs } from "hooks";

interface GiftProps {}

const GiftWrapper = styled(motion.div)`
  height: 100%;

  .adm-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }

  .adm-tabs-header {
    background-color: #fff;
  }

  .adm-tabs-content {
    --content-padding: var(--layout-content-padding);
    height: 100%;
    overflow: auto;
  }
`;

const TAB_KEY = {
  OFFER: "offer",
  REDEEMED: "redeemed",
  USED: "used",
} as const;

type TabKey = (typeof TAB_KEY)[keyof typeof TAB_KEY];

export const Gift: React.FC<GiftProps> = () => {
  const { activeTab, onChangeTab } = useTabs<TabKey>({
    defaultTab: "offer",
    tabMenu: Object.values(TAB_KEY),
  });
  const tabs = [
    {
      key: TAB_KEY.OFFER,
      title: "Ưu đãi",
    },
    {
      key: TAB_KEY.REDEEMED,
      title: "Đã đổi",
    },
    {
      key: TAB_KEY.USED,
      title: "Đã dùng",
    },
  ];

  const tabContent = {
    [TAB_KEY.OFFER]: <Offer />,
    [TAB_KEY.REDEEMED]: <Redeemed />,
    [TAB_KEY.USED]: <Used />,
  };

  return (
    <GiftWrapper>
      <Tabs activeKey={activeTab} onChange={onChangeTab}>
        {tabs.map((tab) => (
          <Tabs.Tab key={tab.key} title={tab.title} destroyOnClose>
            {tabContent[tab.key]}
          </Tabs.Tab>
        ))}
      </Tabs>
    </GiftWrapper>
  );
};
