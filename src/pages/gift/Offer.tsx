// Libraries
import React, { memo, useCallback, useMemo } from "react";
import { generatePath, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { motion } from "motion/react";
import { useImmer } from "use-immer";
import { useDebounceCallback, useLocalStorage } from "usehooks-ts";
import { useRecoilState } from "recoil";

// Components
import { PullToRefresh, Space } from "@antscorp/ama-ui";
import {
  ConditionalRenderer,
  BrandItem,
  SearchBar,
  SelectPicker,
  SelectPickerItem,
} from "components";
import { CATEGORIES_DEFAULT, CategoryList } from "./components";

// State
import { loyaltyState } from "state";

// Queries
import { useGetSchemeList } from "queries";

// Hooks
import { useAppConfig, useUserInfo, useViewPage } from "hooks";

// Constants
import { LOCAL_STORAGE_KEY, ROUTES } from "constant";

// Utils
import {
  callCdpEvent,
  recursiveSearchItems,
  safeParseInt,
  searchStringQuery,
} from "utils";

interface OfferProps {}

const OfferWrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 10px;

  .adm-pull-to-refresh-content {
    height: 100%;
  }

  .adm-pull-to-refresh {
    width: 100%;
  }
`;

const pointFilterOptions: (SelectPickerItem & { range: number[] })[] = [
  {
    value: "all",
    label: "Tất cả",
    range: [0, Infinity],
  },
  {
    value: "under_100",
    label: "Dưới 100 points",
    range: [0, 100],
  },
  {
    value: "100_to_500",
    label: "100 - 500 points",
    range: [100, 500],
  },
  {
    value: "500_to_1000",
    label: "500 - 1000 points",
    range: [500, 1000],
  },
  {
    value: "1000_to_5000",
    label: "1000 - 5000 points",
    range: [1000, 5000],
  },
  {
    value: "above_5000",
    label: "Trên 5000 points",
    range: [5000, Infinity],
  },
];

const skeletonList = Array.from({ length: 10 }).map((_, index) => index);

export const Offer: React.FC<OfferProps> = memo(() => {
  const navigate = useNavigate();
  const { appSettings } = useAppConfig();
  const { userInfo } = useUserInfo();

  useViewPage({
    pageType: "list_scheme",
  });

  // Hooks
  const debouncedChangeSearch = useDebounceCallback((value: string) => {
    setState((draft) => {
      draft.searchValue = value;
    });

    // Call cdp event search
    callCdpEvent({
      ea: "search",
      ec: "scheme_marketing",
      data: {
        src_search_term: value,
        customer_phone: userInfo?.phoneNumber,
      },
      uId: userInfo?.id,
    });
  }, 500);

  // Queries
  const {
    data: schemeListData,
    isLoading: isSchemeListLoading,
    refetch,
  } = useGetSchemeList({
    args: {
      params: {
        type: "all",
      },
    },
  });

  // Variables
  const { schemePointFilterList, schemeCategoryList } =
    appSettings?.globals?.loyalty || {};

  // Memos
  const pointsFilterList = useMemo(() => {
    return Array.isArray(schemePointFilterList) && schemePointFilterList.length
      ? schemePointFilterList
      : pointFilterOptions || [];
  }, [schemePointFilterList]);

  const categories = useMemo(() => {
    return Array.isArray(schemeCategoryList) && schemeCategoryList.length
      ? schemeCategoryList
      : CATEGORIES_DEFAULT;
  }, [schemeCategoryList]);

  const [schemeFilter, setSchemeFilter] = useLocalStorage(
    LOCAL_STORAGE_KEY.SCHEME_FILTER,
    {
      // point: pointsFilterList[0].value || "all",
      category: categories[0].value || "all",
    }
  );

  const [{ schemeFilter: loyaltySchemeFilter }, setLoyaltyState] =
    useRecoilState(loyaltyState);

  // State
  const [state, setState] = useImmer({
    searchValue: "",
  });
  const { searchValue } = state;
  const { point } = loyaltySchemeFilter || {};

  const schemeList = useMemo(() => {
    const filteredVoucherList =
      schemeListData?.data?.schemes?.filter((scheme) => {
        // Filter by point range
        const pointRange = pointFilterOptions.find(
          (option) => option.value === point
        )?.range;
        let isMatchedPointRange = true;
        let isMatchedValidCategory = true;

        if (pointRange) {
          isMatchedPointRange =
            safeParseInt(scheme.point_redeem) >= pointRange[0] &&
            safeParseInt(scheme.point_redeem) <= pointRange[1];
        }

        // Filter by category
        if (schemeFilter.category !== "all") {
          if (schemeFilter.category === "other") {
            isMatchedValidCategory = !categories?.some((category) => {
              return searchStringQuery(scheme.category, category.value);
            });
          } else {
            isMatchedValidCategory = searchStringQuery(
              scheme.category,
              schemeFilter.category
            );
          }
        }

        return isMatchedPointRange && isMatchedValidCategory;
      }) || [];

    // Sort by point
    const sortedVoucherList = filteredVoucherList?.sort((a, b) => {
      return safeParseInt(a.point_redeem) - safeParseInt(b.point_redeem);
    });

    const searchedVoucherList = recursiveSearchItems({
      list: sortedVoucherList || [],
      searchKeys: ["name", "partner"],
      searchValue,
    });

    return searchedVoucherList.results;
  }, [
    categories,
    schemeFilter.category,
    point,
    schemeListData?.data,
    searchValue,
  ]);

  const renderGifts = useCallback(() => {
    return (
      <div className="w-full h-full overflow-auto pb-layout hide-scrollbar">
        <div className="grid grid-cols-2 gap-x-2 gap-y-3 w-full">
          {schemeList.map((scheme, index) => (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{
                delay: index * 0.1,
              }}
              key={scheme.id}
              className="relative"
            >
              <BrandItem
                key={scheme.id}
                scheme={scheme}
                className="scheme-item !w-full !h-full"
                onClick={() => {
                  navigate(
                    `${generatePath(`${ROUTES["GIFT-DETAIL"].path}`, {
                      giftId: `${scheme.id || ""}`,
                    })}${searchValue ? `?search=${searchValue}` : ""}`
                  );
                }}
              />
            </motion.div>
          ))}
        </div>
      </div>
    );
  }, [schemeList, searchValue, navigate]);

  return (
    <OfferWrapper>
      <SearchBar className="w-full" onChange={debouncedChangeSearch} />

      <Space align="center" justify="between" className="w-full shrink-0">
        <SelectPicker
          options={pointsFilterList}
          defaultValue={pointsFilterList[0].value}
          value={`${point || ""}`}
          onChange={(value) => {
            setLoyaltyState((previous) => ({
              ...previous,
              schemeFilter: {
                ...previous.schemeFilter,
                point: value,
              },
            }));
          }}
        />
        {/* <Button fill="none" color="primary">
          Xoá
        </Button> */}
      </Space>
      <div
        className="flex h-full overflow-auto gap-2.5"
        style={{
          marginLeft: "calc(-1 * var(--layout-content-padding))",
          marginBottom: "calc(-1 * var(--layout-content-padding))",
        }}
      >
        <CategoryList
          schemeLoading={isSchemeListLoading}
          schemeLists={schemeListData?.data?.schemes || []}
          value={schemeFilter.category}
          onChange={(value) => {
            setSchemeFilter((prev) => ({
              ...prev,
              category: value,
            }));
          }}
        />

        <ConditionalRenderer
          isLoading={isSchemeListLoading}
          loadingRender={
            <div className="grid grid-cols-2 gap-2.5 w-full h-full overflow-auto">
              {skeletonList.map((index) => (
                <BrandItem
                  key={index}
                  skeleton
                  className="!w-full !h-auto aspect-[0.74]"
                />
              ))}
            </div>
          }
          isEmpty={!schemeList.length}
          onRefresh={async () => {
            await refetch();
          }}
        >
          <PullToRefresh threshold={40} onRefresh={refetch}>
            {renderGifts()}
          </PullToRefresh>
        </ConditionalRenderer>
      </div>
    </OfferWrapper>
  );
});

Offer.displayName = "Offer";
