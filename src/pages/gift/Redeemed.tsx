// Libraries
import React, { memo } from "react";
import styled from "styled-components";
import { useImmer } from "use-immer";
import { useDebounceCallback } from "usehooks-ts";

// Components
import { MemberRegistration, SearchBar, Segmented } from "components";
import { VoucherList } from "./components/VoucherList";

// Hooks
import { useTabs, useViewPage } from "hooks";

// Constants
import { EVENT_CONFIG, VOUCHER_TYPE_KEY, VOUCHER_TYPE_OPTIONS } from "constant";

// Types
import { VoucherStatusKey, VoucherType } from "types";

interface RedeemedProps {}

const RedeemedWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  height: 100%;
`;

type TState = {
  voucherType: VoucherType;
};

export const Redeemed: React.FC<RedeemedProps> = memo(() => {
  // Hooks
  useViewPage({
    pageType: "my_gift",
    pageCate: EVENT_CONFIG.MY_GIFT_CATE,
  });
  const { activeTab: voucherType, onChangeTab } = useTabs<VoucherType>({
    key: "voucherType",
    defaultTab: "voucher",
    tabMenu: Object.values(VOUCHER_TYPE_KEY),
  });

  const [search, setSearch] = React.useState("");

  const debounceChangeSearch = useDebounceCallback(
    (value: string) => setSearch(value),
    500
  );

  return (
    <RedeemedWrapper>
      <SearchBar className="w-full" onChange={debounceChangeSearch} />
      <Segmented
        className="w-fit"
        value={voucherType}
        onChange={(value) => onChangeTab(value as VoucherType)}
        options={VOUCHER_TYPE_OPTIONS}
      />
      <VoucherList
        voucherStatus="available"
        search={search}
        voucherType={voucherType}
      />
    </RedeemedWrapper>
  );
});

Redeemed.displayName = "Redeemed";
