// Libraries
import { InfiniteScroll, PullStatus, PullToRefresh } from "@antscorp/ama-ui";
import { useUserInfo } from "hooks";
import { useGetVoucherList } from "queries";
import React, { memo, useMemo } from "react";
import styled from "styled-components";
import dayjs from "dayjs";

// Components
import { VoucherItem } from "./VoucherItem";
import { ConditionalRenderer } from "components";

// Constants
import { VOUCHER_STATUS_KEY } from "constant";
import { Filter, VoucherStatusKey, VoucherType } from "types";

interface VoucherListProps {
  voucherStatus?: VoucherStatusKey;
  // Name search of voucher
  search?: string;

  voucherType?: VoucherType;
}

const ListWrapper = styled.div`
  height: 100%;
`;

const LIMIT = 10;

export const VoucherList: React.FC<VoucherListProps> = memo((props) => {
  const {
    voucherStatus = VOUCHER_STATUS_KEY.AVAILABLE,
    search,
    voucherType,
    ...restProps
  } = props;
  const { userInfo } = useUserInfo();

  const voucherListFilters = useMemo(() => {
    const filters: {
      OR: {
        AND: Filter[];
      }[];
    } = {
      OR: [
        {
          AND: [
            {
              column: "appUserId",
              data_type: "string",
              operator: "matches",
              value: [userInfo?.id],
            },
            {
              column: "status",
              data_type: "string",
              operator: "matches",
              value: [voucherStatus],
            },
            // Show vouchers created in the last 30 days
            {
              column: "ctime",
              data_type: "date",
              operator: "after_date",
              value: dayjs().subtract(29, "day").startOf("day").toDate(),
            },
          ],
        },
      ],
    };

    if (search) {
      filters.OR[0].AND.push({
        column: "name",
        data_type: "string",
        operator: "contains",
        value: `${search || ""}`,
      });
    }

    if (voucherType) {
      filters.OR[0].AND.push({
        column: "type",
        data_type: "string",
        operator: "matches",
        value: [voucherType],
      });
    }

    return filters;
  }, [search, userInfo?.id, voucherStatus, voucherType]);

  const {
    data: voucherListData,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    refetch,
  } = useGetVoucherList({
    params: {
      page: 1,
      limit: LIMIT,
      sort: ["status", "ctime"],
      sd: ["asc", "desc"],
      filters: voucherListFilters,
    },
  });

  // Memos
  const vouchers = useMemo(() => {
    return (
      voucherListData?.pages
        ?.flatMap((page) => page?.data?.entries)
        .filter(Boolean) || []
    );
  }, [voucherListData]);

  return (
    <>
      <div className="text-[#777777] text-[11px] text-center">
        Danh sách chỉ thể hiện lịch sử voucher trong 30 ngày. <br />
        Nếu cần xem thêm bạn hãy liên lạc Hotline: 19001755 để được hỗ trợ.
      </div>
      <div className="w-full h-full overflow-auto">
        <ConditionalRenderer isLoading={isLoading} isEmpty={!vouchers.length}>
          <PullToRefresh
            threshold={40}
            onRefresh={async () => {
              await refetch();
            }}
          >
            <ListWrapper className="flex flex-col w-full h-full gap-2.5 overflow-auto">
              {vouchers.map((voucher, index) => {
                const isLast = index === vouchers.length - 1;
                return (
                  <VoucherItem
                    key={voucher?.id}
                    index={index}
                    // ref={isLast && isShowLoadMore ? ref : null}
                    voucher={voucher}
                  />
                );
              })}
            </ListWrapper>
            <InfiniteScroll
              hasMore={hasNextPage}
              loadMore={async () => {
                if (!isFetchingNextPage) {
                  await fetchNextPage();
                }
              }}
            >{``}</InfiniteScroll>
          </PullToRefresh>
        </ConditionalRenderer>
      </div>
    </>
  );
});

VoucherList.displayName = "VoucherList";
