// Libraries
import clsx from "clsx";
import React, { memo, useCallback, useMemo } from "react";
import styled from "styled-components";

// Assets
import luckyMoneyLogo from "assets/images/logos/lucky-money-logo.png";
import voucherVerticalDivider from "assets/images/others/voucher-vertical-divider.png";

// Schemas
import { Voucher } from "schemas";

// Utils
import { checkAvailableVoucher, formatVoucherDateTime } from "utils";

// Components
import { VoucherTag } from "components";
import { Text, useNavigate } from "zmp-ui";

interface VoucherItemProps extends React.HTMLAttributes<HTMLDivElement> {
  voucher: Voucher;
  index?: number;
}
const VoucherItemWrapper = styled.div`
  --text-base-size: 12px;

  width: 100%;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 10px;
  cursor: pointer;

  .voucher-item {
    height: 100%;

    &__left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20.27%;
      height: 100%;
    }

    &__right {
      position: relative;
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 2px;
      padding: 12px;
      overflow: visible;
      border-left: 1px dashed #e5e7eb;

      &::after,
      &::before {
        content: "";
        position: absolute;
        width: 18px;
        height: 10px;
        background-color: var(--color-background);
        transform: translateX(calc(-100% - 3px));
      }

      &::after {
        bottom: 0;
        border-radius: 100px 100px 0px 0px;
      }

      &::before {
        top: 0;
        border-radius: 0px 0px 100px 100px;
      }
    }

    &__title,
    &__expiration {
      color: #777777;
      font-size: var(--text-base-size);
    }

    &__coupon-name {
      color: var(--color-main-primary);
      font-size: 14px;
      font-weight: 500;
    }

    &__use-btn {
      font-size: var(--text-base-size);
      color: var(--color-main-primary);
    }

    &__divider {
      border: 1px dashed #e5e7eb;
      height: 100%;
      /* aspect-ratio: 0.1;
      background: url("${voucherVerticalDivider}") no-repeat center top /
        contain; */
    }
  }
`;

export const VoucherItem = React.forwardRef((props: VoucherItemProps, ref) => {
  const { voucher } = props;
  const navigate = useNavigate();

  // Variables
  const { name, icon, gameType = "li_xi", expiryDate } = voucher;

  // Memo
  const isAvailableVoucher = useMemo(() => {
    return checkAvailableVoucher(voucher);
  }, [voucher]);

  const isShowNote = useMemo(() => {
    return ["li_xi"].includes(gameType) && isAvailableVoucher;
  }, [gameType, isAvailableVoucher]);

  // Handlers
  const onClickVoucherItem = useCallback(() => {
    navigate(`/vouchers/${voucher.id}`);
  }, [navigate, voucher.id]);

  return (
    <VoucherItemWrapper
      ref={ref as React.LegacyRef<HTMLDivElement>}
      className={clsx("voucher-item", {
        "opacity-70 pointer-events-none": !isAvailableVoucher,
      })}
      data-test="voucher-item"
      onClick={onClickVoucherItem}
    >
      <div className="voucher-item__left">
        <img
          src={icon || luckyMoneyLogo}
          width={40}
          onError={(e) => {
            e.currentTarget.src = luckyMoneyLogo;
            e.currentTarget.width = 50;
          }}
        />
      </div>
      <div className="voucher-item__right">
        <div className="flex items-center justify-between">
          <Text className="voucher-item__title">MÃ GIẢM GIÁ</Text>
          {/* <img src={discountBadge} width={12} /> */}
        </div>
        <div className="voucher-item__coupon-name !line-clamp-1">{name}</div>
        <Text className="voucher-item__expiration">{`HSD ${formatVoucherDateTime(
          expiryDate
        )}`}</Text>
        <div className="flex items-center justify-between gap-2.5">
          <div className="flex flex-col gap-1">
            <VoucherTag voucher={voucher} />
            {isShowNote && (
              <Text size="xxxSmall" className="text-[#777777]">
                Lưu ý: Sử dụng voucher sau 1 giờ nhận
              </Text>
            )}
          </div>
          {isAvailableVoucher && (
            <button
              className="voucher-item__use-btn shrink-0"
              onClick={onClickVoucherItem}
            >
              Dùng ngay
            </button>
          )}
        </div>
      </div>
    </VoucherItemWrapper>
  );
});

VoucherItem.displayName = "VoucherItem";
