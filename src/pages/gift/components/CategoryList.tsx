// Libraries
import * as Icon from "components/icons";
import styled, { css } from "styled-components";
import { motion } from "motion/react";
import React, { memo, useCallback, useEffect, useMemo } from "react";
import { startTransition } from "react";

// Hooks
import { useAppConfig, useControlledValue, useResponsive } from "hooks";

// Utils
import { respond, searchStringQuery } from "utils";

// Types
import { Scheme } from "schemas";

interface CategoryListProps {
  schemeLists: Scheme[];
  schemeLoading: boolean;
  value?: string;
  onChange?: (value: string) => void;
}

const CategoryListWrapper = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  height: 100%;
  width: 80px;
  background-color: #fffcf8;
  border-top-right-radius: 12px;
  overflow: auto;
  flex-shrink: 0;
  padding: 15px 0px;

  &::-webkit-scrollbar {
    display: none;
  }

  @media screen and (max-width: 380px) {
    width: 70px;
  }

  ${respond.xs`
    width: 90px;
  `}
`;

const CategoryItem = styled(motion.div)<{ $active?: boolean }>`
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  text-align: center;
  transition: color 0.3s, background-color 0.3s;
  gap: 4px;
  color: #9a9a9a;

  svg {
    color: #9a9a9a;
  }
  ${({ $active }) =>
    $active &&
    css`
      color: var(--adm-color-primary);

      svg {
        color: var(--adm-color-primary);
      }
    `}
`;

export const CATEGORIES_DEFAULT = [
  {
    value: "all",
    label: "Tất cả",
    icon: "AllIcon",
  },
  {
    value: "Giải trí",
    label: "Giải trí",
    icon: "SportsEsportsIcon",
  },
  {
    value: "Ăn uống",
    label: "Ăn uống",
    icon: "LunchDiningIcon",
  },
  {
    value: "Mua sắm",
    label: "Mua sắm",
    icon: "ShoppingBagIcon",
  },
  {
    value: "Mẹ và bé",
    label: "Mẹ và bé",
    icon: "BabyChangingStationIcon",
  },
  {
    value: "Di chuyển",
    label: "Di chuyển",
    icon: "AirportShuttleIcon",
  },
  {
    value: "TMDT",
    label: "TMĐT",
    icon: "ShoppingCartIcon",
  },
  {
    value: "Du lịch",
    label: "Du lịch",
    icon: "LocalAirportIcon",
  },
  {
    value: "Siêu thị",
    label: "Siêu thị",
    icon: "StorefrontIcon",
  },
  {
    value: "other",
    label: "Khác",
    icon: "PublicIcon",
  },
];

export const CategoryList: React.FC<CategoryListProps> = memo(
  ({
    value: controlledValue,
    schemeLists = [],
    schemeLoading = false,
    onChange,
  }) => {
    const { appSettings } = useAppConfig();

    // Variables
    const { schemeCategoryList } = appSettings?.globals?.loyalty || {};

    // Hooks
    const [currentValue, setValue] = useControlledValue({
      value: controlledValue,
      defaultValue: "all",
      onChange,
    });
    const { isLargeMobile } = useResponsive();

    // Handlers
    const onClickCategory = (value) => {
      setValue(value);
    };

    // Memos
    const categories = useMemo(() => {
      const draftCategories =
        Array.isArray(schemeCategoryList) && schemeCategoryList.length
          ? schemeCategoryList
          : CATEGORIES_DEFAULT;

      // This function filters the draftCategories array and returns only categories that meet certain criteria
      return draftCategories.filter((category) => {
        // Always include the "all" category
        if (category.value === "all") {
          return true;
        }

        // Only include the "other" category if there are no categories besides "all" and "other"
        // (returns true if there are NO categories that are neither "all" nor "other")
        if (category.value === "other") {
          return (
            schemeLists.filter(
              (scheme) =>
                !draftCategories.some(
                  (cat) =>
                    searchStringQuery(cat.value, scheme.category) &&
                    cat.value !== "all"
                )
            ).length !== 0
          );
        }

        // For any other category, include it only if there is at least one scheme
        // in the schemeLists array that belongs to this category
        return schemeLists.some((scheme) => {
          return searchStringQuery(scheme.category, category.value);
        });
      });
    }, [schemeCategoryList, schemeLists]);

    useEffect(() => {
      if (
        // length of categories is greater than 1 because we always have "all" category
        categories.length > 1 &&
        currentValue &&
        !categories.some((cat) => searchStringQuery(cat.value, currentValue))
      ) {
        setValue(categories[0].value);
      }
    }, [categories, currentValue, setValue]);

    return (
      <CategoryListWrapper>
        {!schemeLoading &&
          categories.map(({ value, icon, label }, index) => {
            const RenderIcon = Icon[icon] ? Icon[icon] : Icon.AllIcon;

            return (
              <CategoryItem
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                // transition={{ delay: index * 0.1 }}
                key={value}
                $active={currentValue === value}
                onClick={() => {
                  if (!("ontouchstart" in window)) {
                    onClickCategory(value);
                  }
                }}
                onTouchEnd={() => onClickCategory(value)}
              >
                {RenderIcon && <RenderIcon size={isLargeMobile ? 24 : 20} />}
                <span className="font-medium">{label}</span>
              </CategoryItem>
            );
          })}
      </CategoryListWrapper>
    );
  }
);

CategoryList.displayName = "CategoryList";
