// Libraries
import clsx from "clsx";
import dayjs from "dayjs";
import { motion } from "motion/react";
import React, { Suspense, useCallback, useEffect, useMemo } from "react";
import BarCode from "react-barcode";
import { useParams, useSearchParams } from "react-router-dom";
import styled from "styled-components";
import { useImmer } from "use-immer";
import { useQueryClient } from "@tanstack/react-query";

// Hooks
import { useAppConfig, useNotApplicable, useViewPage } from "hooks";

// Queries
import { useGetVoucherDetail, useUpdateVoucher } from "queries";

// Assets
import voucherDetailBg from "assets/images/backgrounds/voucher-detail-bg.png";
import dashed from "assets/svg/dashed.svg";

// Components
import { CopyIcon } from "lucide-react";
import { ConditionalRenderer, InfoIcon, VoucherTag } from "components";
import { Text, useNavigate } from "zmp-ui";
import { ClauseSheet } from "./ClauseSheet";

// Utils
import {
  copyToClipboard,
  formatVoucherDateTime,
  searchStringQuery,
} from "utils";

// Constants
import { APP_CONFIG, EVENT_CONFIG, PARTNERS, QUERY_KEY } from "constant";

// Schemas
import { Button, Modal, Toast } from "@antscorp/ama-ui";
import { Voucher } from "schemas";
import { isNull } from "lodash-es";

interface VoucherDetailPageProps {}

const VoucherDetailContent = styled(motion.div)`
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 30px;

  .voucher-detail {
    text-align: center;

    &__info {
      position: relative;
      margin-top: 12px;
      width: 100%;
      background-color: #ffffff;
      /* aspect-ratio: 0.936; */
      /* background: url("${voucherDetailBg}") no-repeat center center / contain; */
      display: flex;
      flex-direction: column;
      overflow: hidden;
      border-radius: 20px;
    }

    &__info-top {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px;
      gap: 10px;
      text-align: center;
    }

    &__info-bottom {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      text-align: center;
      /* height: 37.79%; */

      > .bottom-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 24px;
      }

      > .used-alert {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        background-color: #ffe8bd;
        color: #9d6e26;
        width: 100%;
        padding: 14px 0px;
        border-radius: 0px 0px 20px 20px;
        font-size: 11px;
      }
    }

    &__divider {
      position: relative;
      width: 100%;
      height: 1.5px;
      background: url(${dashed}) no-repeat center center / cover;
      display: flex;
      align-items: center;

      &::after,
      &::before {
        content: "";
        position: absolute;
        width: 14px;
        height: 24px;
        background-color: var(--color-background);
      }

      &::after {
        left: -2px;
        border-radius: 0px 100px 100px 0px;
      }

      &::before {
        right: -2px;
        border-radius: 100px 0px 0px 100px;
      }
    }
  }
`;

const BackButton = styled.button`
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 30px;
  left: 10px;
  border-radius: 100%;
  width: 50px !important;
  padding: 0px;
  aspect-ratio: 1;
  background-color: var(--color-main-primary);
  color: white;
  cursor: pointer;
`;

const VoucherDetail = () => {
  const queryClient = useQueryClient();
  const { appSettings } = useAppConfig();
  const navigate = useNavigate();
  const { voucherId } = useParams() || {};
  const { metrics } = appSettings?.globals || {};
  const [searchParams] = useSearchParams();

  useViewPage({
    pageType: "my_gift_detail",
    pageCate: EVENT_CONFIG.MY_GIFT_CATE,
  });

  const {
    data: voucherData,
    isLoading: isVoucherDetailLoading,
    isFetching: isVoucherDetailFetching,
  } = useGetVoucherDetail({
    params: {
      id: `${voucherId || ""}`,
    },
    options: {
      refetchOnMount: "always",
      enabled: !!voucherId,
    },
  });
  const { mutateAsync: updateVoucher, isPending: isUpdateVoucherPending } =
    useUpdateVoucher();
  const { data: voucherDetail } = voucherData || {};
  const isOpenTAC = searchParams.get("isOpenTAC");

  // State
  const [state, setState] = useImmer({
    clauseSheetVisible: false,
  });
  const { clauseSheetVisible } = state;

  // Variables
  const { metadata } = voucherDetail || {};
  const { partner } = metadata || {};
  const isVoucherUsed = voucherDetail?.status === "used";
  const { isShowNotApplicableMessage, notApplicableMessage } = useNotApplicable(
    {
      scheme_id: voucherDetail?.metadata?.scheme_id,
    }
  );

  // Memo
  const isShowNote = useMemo(() => {
    return ["li_xi"].includes(voucherDetail?.gameType || "li_xi");
  }, [voucherDetail?.gameType]);

  const isShowMarkUsedButton = useMemo(() => {
    if (isVoucherUsed || isNull(partner)) return false;

    if (
      ![PARTNERS.HIGHLANDS.key].some((p) => searchStringQuery(p, partner || ""))
    )
      return true;

    return false;
  }, [partner, isVoucherUsed]);

  useEffect(() => {
    return () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY.VOUCHER_LIST],
        exact: false,
      });
    };
  }, [queryClient]);

  useEffect(() => {
    if (isOpenTAC) {
      setState((draft) => {
        draft.clauseSheetVisible = true;
      });
    }
  }, [isOpenTAC, setState]);

  // Handlers
  const onClickBackButton = useCallback(() => {
    navigate("/");
  }, [navigate]);

  const onClickMarkUsedVoucher = useCallback(async () => {
    Modal.show({
      // header: <SuccessIcon size={46} className="text-success" />,
      title: "Xác nhận dùng Voucher",
      content: (
        <div className="text-center">
          Một khi nhấn <strong>“Đã dùng”</strong> voucher của bạn sẽ được chuyển
          vào mục <strong>“Đã dùng”.</strong> <br /> <strong>Lưu ý:</strong> Bạn
          không thể xem lại hoặc sử dụng voucher sau thao tác này.
        </div>
      ),
      closeOnMaskClick: true,
      closeOnAction: true,
      actions: [
        {
          key: "confirm",
          text: "Đã dùng",
          primary: true,
          onClick: async () => {
            const { data } =
              (await updateVoucher({
                id: voucherDetail?.id,
                status: "used",
                usedTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
              })) || {};

            if (data) {
              Toast.show({
                icon: "success",
                content: (
                  <div className="text-center">
                    Xác nhận đã dùng <br /> voucher thành công
                  </div>
                ),
              });
            }
          },
        },
        {
          key: "cancel",
          primary: false,
          className: "adm-button-default !border",
          text: "Chưa dùng",
        },
      ],
    });
  }, [updateVoucher, voucherDetail?.id]);

  const onClickCopyCode = useCallback(() => {
    if (voucherDetail?.code) {
      copyToClipboard(voucherDetail.code, {
        onSuccess: () => {
          Toast.show({
            icon: "success",
            content: <div className="text-center">Sao chép mã thành công</div>,
            duration: 1000,
          });
        },
      });
    }
  }, [voucherDetail?.code]);

  return (
    <VoucherDetailContent
      className={clsx("voucher-detail", {
        "h-full items-center justify-center":
          isVoucherDetailLoading || isVoucherDetailFetching,
      })}
    >
      <ConditionalRenderer
        isLoading={isVoucherDetailLoading || isVoucherDetailFetching}
      >
        <motion.div
          className="flex flex-col gap-[30px]"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="voucher-detail__info">
            <div className="voucher-detail__info-top">
              <Text size="xxSmall" className="text-description">
                MÃ GIẢM GIÁ
              </Text>
              <Text
                bold
                dangerouslySetInnerHTML={{
                  __html: `${voucherDetail?.name || ""}`,
                }}
              />
              <div className="w-full flex items-center justify-center">
                <BarCode
                  fontSize={16}
                  fontOptions="bold"
                  font="Roboto"
                  value={`${voucherDetail?.code || ""}`.toUpperCase()}
                  width={
                    metrics?.voucherDetailBarWidth ||
                    APP_CONFIG.VOUCHER_DETAIL_BAR_WIDTH
                  }
                />
              </div>
              <Button
                size="small"
                className="!text-sm"
                icon={<CopyIcon size={16} />}
                iconPosition="right"
                onClick={onClickCopyCode}
              >
                Sao chép mã
              </Button>
            </div>
            <div className="voucher-detail__divider"></div>
            <div className="voucher-detail__info-bottom">
              <div className="bottom-group">
                <Text className="text-description" size="xxSmall">
                  {`Mã giảm giá phát hành lúc ${formatVoucherDateTime(
                    voucherDetail?.ctime || ""
                  )} bởi Highlands
                    Coffee`}
                </Text>
                {!isVoucherUsed && (
                  <>
                    <Text bold size="small">
                      {`HSD ${formatVoucherDateTime(
                        voucherDetail?.expiryDate || ""
                      )}`}
                    </Text>
                    <VoucherTag voucher={voucherDetail as Voucher} />
                    {isShowNote && (
                      <Text className="text-[#777777]" size="xxSmall">
                        Lưu ý: Sử dụng voucher sau 1 giờ nhận
                        {isShowNotApplicableMessage && (
                          <>
                            <br />
                            {notApplicableMessage}
                          </>
                        )}
                      </Text>
                    )}
                  </>
                )}
              </div>
              {isVoucherUsed && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="used-alert"
                >
                  <InfoIcon width={20} height={20} />
                  <span>
                    {`Mã giảm giá này đã đổi lúc ${formatVoucherDateTime(
                      voucherDetail?.usedTime || voucherDetail?.utime || ""
                    )}`}
                  </span>
                </motion.div>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-2.5">
            {/*  //TODO: Uncomment when pharse 2 */}
            {isShowMarkUsedButton && (
              <Button
                color="primary"
                shape="rounded"
                // loading={isUpdateVoucherPending}
                onClick={onClickMarkUsedVoucher}
              >
                Xác nhận đã dùng voucher
              </Button>
            )}
            <Button
              color="default"
              shape="rounded"
              onClick={() => {
                setState((draft) => {
                  draft.clauseSheetVisible = true;
                });
              }}
            >
              Điều khoản sử dụng
            </Button>
          </div>
        </motion.div>
        {/* <BackButton onClick={onClickBackButton}>
          <ArrowLeftIcon />
        </BackButton> */}
      </ConditionalRenderer>
      <ClauseSheet
        visible={clauseSheetVisible}
        voucher={voucherDetail}
        loading={isVoucherDetailLoading}
        onClose={() =>
          setState((draft) => {
            draft.clauseSheetVisible = false;
          })
        }
      />
    </VoucherDetailContent>
  );
};

const VoucherDetailPage: React.FC<VoucherDetailPageProps> = () => {
  return (
    <Suspense>
      <VoucherDetail />
    </Suspense>
  );
};

export default VoucherDetailPage;
