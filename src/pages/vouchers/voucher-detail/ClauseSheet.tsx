// Libraries
import React from "react";
import styled from "styled-components";

// Components
import { Sheet, Text } from "zmp-ui";
import { ConditionalRenderer } from "components";

// Types
import { SheetProps } from "zmp-ui/sheet";

// Schemas
import { Voucher } from "schemas";

interface ClauseSheetProps extends SheetProps {
  voucher?: Voucher;
  loading?: boolean;
}

const ClauseSheetContent = styled.div`
  padding: 20px 20px 20px 30px;
  min-height: 400px;
  max-height: 75vh;
  display: flex;
  flex-direction: column;
  overflow: auto;

  ul {
    list-style-type: disc;
    line-height: 20px;
  }

  .clause-list {
    line-height: 20px;
    padding-bottom: 100px;
  }

  .clause-description {
    /* overflow: auto; */
  }
`;

export const ClauseSheet: React.FC<ClauseSheetProps> = (props) => {
  const { visible, voucher, loading, onClose } = props;
  const { description } = voucher || {};

  return (
    <Sheet
      visible={visible}
      autoHeight
      mask
      handler
      swipeToClose
      onClose={onClose}
    >
      <ClauseSheetContent>
        <ConditionalRenderer
          isLoading={loading}
          loadingProps={{
            className: "m-auto",
          }}
        >
          <Text className="!font-bold mb-2.5">Điều khoản sử dụng:</Text>
          <div
            className="clause-description"
            dangerouslySetInnerHTML={{
              __html: `${description || ""}`,
            }}
          />
        </ConditionalRenderer>
      </ClauseSheetContent>
    </Sheet>
  );
};
