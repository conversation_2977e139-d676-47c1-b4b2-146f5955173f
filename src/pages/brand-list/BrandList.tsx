// Libraries
import React from "react";
import styled from "styled-components";

// Hooks
import { useViewPage } from "hooks";

// Constants
import { EVENT_CONFIG } from "constant";

// Components
import { Grid } from "@antscorp/ama-ui";
import { BrandItem } from "components";

// Assets
import Ashima from "assets/images/logos/brands/Ashima.png";
import CrystalJade from "assets/images/logos/brands/CrystalJade.png";
import Daruma from "assets/images/logos/brands/Daruma.png";
import GogiHouse from "assets/images/logos/brands/GogiHouse.png";
import Hutong from "assets/images/logos/brands/Hutong.png";
import Icook from "assets/images/logos/brands/Icook.png";
import KPub from "assets/images/logos/brands/KPub.png";
import KTop from "assets/images/logos/brands/KTop.png";
import KichiKichi from "assets/images/logos/brands/KichiKichi.png";
import Manwah from "assets/images/logos/brands/Manwah.png";
import Shogun from "assets/images/logos/brands/Shogun.png";
import Vuvuzela from "assets/images/logos/brands/Vuvuzela.png";
import Yutang from "assets/images/logos/brands/Yutang.png";

interface BrandListProps {}

const mockBrandList = [
  {
    id: "1",
    name: "Ashima",
    image: Ashima,
    description: "Ashima Mushroom Hotpot",
  },
  {
    id: "2",
    name: "Crystal Jade",
    image: CrystalJade,
    description:
      "Lần đầu tiên xuất hiện tại Việt Nam, Crystal Jadeaaaaaaaaaaaaaaaaaaaaaaa",
  },
  {
    id: "3",
    name: "Daruma",
    image: Daruma,
    description: "Sushi, Sashimi & Hotpot",
  },
  {
    id: "4",
    name: "Gogi House",
    image: GogiHouse,
    description: "Quán thịt nướng Hàn Quốc",
  },
  {
    id: "5",
    name: "Hutong",
    image: Hutong,
    description: "Lẩu Hồng Kông. Văn hoá ẩm thực Hong Kong",
  },
  {
    id: "6",
    name: "Icook",
    image: Icook,
    description: "Restaurant at Home",
  },
  {
    id: "7",
    name: "K-Pub",
    image: KPub,
    description: "K-Pub không chỉ đặc biệt ở tên gọi mà còn",
  },
  {
    id: "8",
    name: "K-Top",
    image: KTop,
    description:
      "Với đa dạng hương vị nước lẩu, đồ nhúng thịt và rất nhiều aaaaaaaaa",
  },
  {
    id: "9",
    name: "KichiKichi",
    image: KichiKichi,
    description:
      "Lẩu Băng chuyền Kichi - Kichi là chuỗi nhà hàng lẩu 1 người sử dụng",
  },
  {
    id: "10",
    name: "Manwah",
    image: Manwah,
    description:
      "Lẩu Đài Loan. Sau hàng trăm năm tồn tại và trở thành hệ thống lẩu lớn tại Việt Nam",
  },
  {
    id: "11",
    name: "Shogun",
    image: Shogun,
    description: "Quán Nướng & Nhậu Đường Phố Nhật Bản",
  },
  {
    id: "12",
    name: "Vuvuzela",
    image: Vuvuzela,
    description: "Beer club theo phong cách bar, điểm tụ tập và hoạt động",
  },
  {
    id: "13",
    name: "Yutang",
    image: Yutang,
    description: "Trà sữa và món ăn đường phố Đài Loan",
  },
];

const SchemeListingWrapper = styled.div`
  padding: 12px 24px;
`;

export const BrandList: React.FC<BrandListProps> = (props) => {
  //   useViewPage({
  //     pageType: "survey_order",
  //     pageCate: EVENT_CONFIG.APP_LOYALTY,
  //   });

  return (
    <SchemeListingWrapper>
      <Grid columns={2} gap={15}>
        {mockBrandList.map((scheme) => (
          <Grid.Item key={scheme.id}>
            <BrandItem
              key={scheme.id}
              scheme={scheme}
              className="!h-[155px]"
              titleFontSize={"15px"}
              descriptionFontSize={"12px"}
              // onClick={() => {
              //   onClickScheme(scheme);
              // }}
            />
          </Grid.Item>
        ))}
      </Grid>
    </SchemeListingWrapper>
  );
};
