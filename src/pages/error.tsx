// Libraries
import { How<PERSON> } from "howler";

// Components
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";

// Components
import { SystemNotificationModal } from "components";

// Constants
import { APP_CONFIG } from "constant";

const ErrorPage = () => {
  const navigate = useNavigate();
  const searchPrams = new URLSearchParams(window.location.search);
  const errorMessage = searchPrams.get("errorMessage");

  useEffect(() => {
    Howler.stop();
  }, []);

  return (
    <SystemNotificationModal
      showCloseButton={false}
      visible={true}
      title="Thông báo"
      content={errorMessage || APP_CONFIG.SYSTEM_ERROR_MESSAGES.maintenance}
      retryButtonProps={{
        onClick: () => {
          navigate("/");
          window.location.reload();
        },
      }}
    />
  );
};

export default ErrorPage;
