:root {
  /* GGG Design Color */
  --NL300: #001a33;
  --NL500: #667685;
  --NL700: #99a3ad;
  --NL800: #bfc6cc;
  --NL900: #e9ebed;
  --BL300: #0068ff;
  --BL500: #66a4ff;
  --BL700: #cce1ff;
  --GL300: #cce1ff;
  --GL500: #00c578;
  --GL700: #ccf3e4;
  --RL300: #ef4e49;
  --RL500: #f59592;
  --RL700: #fcdcdb;
  --GGG: #e4b653;

  /* Primary Colors */
  --color-primary: #e4b653;
  --color-main-primary: #e4b653;
  --color-primary-light: #ff6b5b;
  --color-primary-dark: #8c0014;

  /* Secondary Colors */
  --color-secondary: #bd945c;
  --color-secondary-light: #ffc87d;
  --color-secondary-dark: #8c6a3a;

  /* Other Colors */
  --color-grey-primary: #f2f3f4;
  --color-grey-secondary: #bfc6cc;
  --color-brown-primary: #4e3629;
  --color-brown-secondary: #6a573d;
  --color-green-primary: #00c578;

  /* Background Colors */
  --color-background: #ffffff;
  --color-background-light: #ffffff;
  --color-background-dark: #e0e0e0;
  --color-background-card: #ffffff;
  --color-background-menu-item: #fdebea;
  --color-background-modal: rgba(0, 0, 0, 0.5);
  --color-background-disabled: #e9ebed;
  --color-placeholder: #f1ebe3;
  --color-background-image: #f2f2f2;
  --color-background-error: #ef4e491f;
  --color-background-success: #00c5781f;
  --color-background-primary: #e4b65329;

  /* Text Colors */
  --color-text-primary: #001a33;
  --color-text-brown: #502c1e;
  --color-text-white: #ffffff;
  --color-text-secondary: #667685;
  --color-text-light: #99a3ad;
  --color-text-tertiary: #777777;
  --color-text-disabled: #e9ebed;
  --color-text-inverse: #ffffff;
  --color-yellow-text: rgba(255, 234, 195, 1);
  --hl-color-text-description: #777777;
  --color-text-link: #0068ff;
  --color-text-error: ##ef4e49;
  --color-text-success: #5b954e;
  --color-text-warning: #ed6c02;

  /* Border Colors */
  --color-border: #d6d9db;
  --color-border-light: #faeae7;
  --color-border-focus: var(--color-main-primary);
  --color-border-error: #ef4e49;

  /* Status Colors */
  --color-success: var(--color-green-primary);
  --color-success-light: #0cb1381a;
  --color-error: #ef4e49;
  --color-error-light: #af00241a;
  --color-warning: #ed6c02;
  --color-warning-light: #ff9800;
  --color-info: #0288d1;
  --color-info-light: #29b6f6;
  --color-disabled: #e9ebed;

  /* Shadow Colors */
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-dark: rgba(0, 0, 0, 0.2);

  /* UI Framework Overrides */
  --zaui-light-bottom-navigation-active-color: var(--color-main-primary);
  --zaui-light-color-primary: var(--color-main-primary);
  --zaui-light-button-secondary-background: var(--color-background-disabled);
  --zaui-light-button-secondary-text: var(--color-main-primary);
  --zaui-light-button-primary-background-pressed: #f4c35b;
  --zaui-light-button-secondary-background-pressed: var(--color-background-disabled);
  --zaui-light-header-background-color: var(--color-main-primary);
  --zaui-light-button-tertiary-text: var(--color-main-primary);
  --zaui-light-picker-option-selected-color: var(--color-main-primary);
  --zaui-light-spinner-dot-color: var(--color-primary);
  --zaui-light-bottom-navigation-color: #fff;

  /* Padding */
  --header-padding-top: max(calc(var(--zaui-safe-area-inset-top, 24px) + 16px), 0px);
  --layout-content-padding: 10px;

  /* Term and condition */
  --tc-color-background: var(--color-background-light);
  --tc-color-text: var(--color-text-primary);

  --max-width: 460px;
}

:root:root {
  /* Antd mobile */
  --adm-color-primary: var(--color-main-primary) !important;
  --adm-color-light: var(--color-text-light);
  --adm-card-border-radius: 10px;
  --adm-button-border-color: #00000033;
  --adm-font-family: 'Roboto', sans-serif;
  --adm-tag-border-radius: 100px;
  --adm-color-text: var(--color-text-primary);
  --adm-font-size-main: 15px;
  --adm-popup-border-radius: 24px;
  --adm-center-popup-border-radius: 20px;
  --adm-center-popup-max-width: min(400px, 90vw);
  --adm-center-popup-min-width: min(400px, 90vw);
  --adm-progress-bar-track-color: #d9d9d9;
  --adm-color-danger: var(--color-error);
  --adm-color-success: var(--color-success);
  --adm-color-text-secondary: var(--color-text-secondary);
}

@media screen and (max-width: 370px) {
  :root {
    --layout-content-padding: 10px;
  }
}
