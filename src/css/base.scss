:root {

  /* GGG Design Color */
  --GL300_RGB: 0, 197, 120;
  --GL500_RGB: 102, 220, 174;
  --RL300_RGB: 239, 78, 73;

  --NL300: #001a33;
  --NL500: #667685;
  --NL700: #99a3ad;
  --NL800: #bfc6cc;
  --NL900: #e9ebed;
  --BL300: #0068ff;
  --BL500: #66a4ff;
  --BL700: #cce1ff;
  --GL300: rgb(var(--GL300_RGB));
  --GL500: rgb(var(--GL500_RGB));
  --GL700: #ccf3e4;
  --RL300: rgb(var(--RL300_RGB));
  --RL500: #f59592;
  --RL700: #fcdcdb;
  --OL300: #f5832f;
  --OL500: #f9b582;
  --OL700: #fde6d5;
  --GGG: #e4b653;

  /* Primary Colors */
  --color-primary: var(--GGG);
  --color-main-primary: var(--GGG);
  --color-primary-light: #ff6b5b;
  --color-primary-dark: #8c0014;

  /* Secondary Colors */
  --color-secondary: #bd945c;
  --color-secondary-light: #ffc87d;
  --color-secondary-dark: #8c6a3a;

  /* Other Colors */
  --color-grey-primary: #f2f3f4;
  --color-grey-secondary: var(--NL800);
  --color-brown-primary: #4e3629;
  --color-brown-secondary: #6a573d;
  --color-green-primary: var(--GL500);

  /* Background Colors */
  --color-background: #ffffff;
  --color-background-light: #ffffff;
  --color-background-dark: #e0e0e0;
  --color-background-card: #ffffff;
  --color-background-menu-item: #fdebea;
  --color-background-modal: rgba(0, 0, 0, 0.5);
  --color-background-disabled: var(--NL900);
  --color-placeholder: #f1ebe3;
  --color-background-image: #f2f2f2;
  --color-background-error: rgba(var(--RL300_RGB), 0.12);
  --color-background-success: rgba(var(--GL300_RGB), 0.12);
  --color-background-primary: #e4b65329;

  /* Text Colors */
  --color-text-primary: var(--NL300);
  --color-text-brown: #502c1e;
  --color-text-white: #ffffff;
  --color-text-secondary: var(--NL500);
  --color-text-light: var(--NL700);
  --color-text-tertiary: #777777;
  --color-text-disabled: var(--NL900);
  --color-text-inverse: #ffffff;
  --color-yellow-text: rgba(255, 234, 195, 1);
  --hl-color-text-description: #777777;
  --color-text-link: var(--BL300);
  --color-text-error: var(--RL300);
  --color-text-success: #5b954e;
  --color-text-warning: var(--OL300);

  /* Border Colors */
  --color-border: #d6d9db;
  --color-border-light: #faeae7;
  --color-border-focus: var(--color-main-primary);
  --color-border-error: var(--RL300);

  /* Status Colors */
  --color-success: var(--color-green-primary);
  --color-success-light: rgba(var(--GL300_RGB), 0.1);
  --color-error: var(--RL300);
  --color-error-light: rgba(var(--RL300_RGB), 0.1);
  --color-warning: var(--OL300);
  --color-warning-light: var(--OL500);
  --color-info: var(--BL300);
  --color-info-light: var(--BL500);
  --color-disabled: var(--NL900);

  /* Shadow Colors */
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-dark: rgba(0, 0, 0, 0.2);

  /* UI Framework Overrides */
  --zaui-light-bottom-navigation-active-color: var(--color-main-primary);
  --zaui-light-color-primary: var(--color-main-primary);
  --zaui-light-button-secondary-background: var(--color-background-disabled);
  --zaui-light-button-secondary-text: var(--color-main-primary);
  --zaui-light-button-primary-background-pressed: var(--GGG);
  --zaui-light-button-secondary-background-pressed: var(--color-background-disabled);
  --zaui-light-header-background-color: var(--color-main-primary);
  --zaui-light-button-tertiary-text: var(--color-main-primary);
  --zaui-light-picker-option-selected-color: var(--color-main-primary);
  --zaui-light-spinner-dot-color: var(--color-primary);
  --zaui-light-bottom-navigation-color: #fff;

  /* Padding */
  --header-padding-top: max(calc(var(--zaui-safe-area-inset-top, 24px) + 16px), 0px);
  --layout-content-padding: 10px;

  /* Term and condition */
  --tc-color-background: var(--color-background-light);
  --tc-color-text: var(--color-text-primary);

  --max-width: 460px;
}

:root:root {
  /* Antd mobile */
  --adm-color-primary: var(--color-main-primary) !important;
  --adm-color-light: var(--color-text-light);
  --adm-card-border-radius: 10px;
  --adm-button-border-color: #00000033;
  --adm-font-family: 'Roboto', sans-serif;
  --adm-tag-border-radius: 100px;
  --adm-color-text: var(--color-text-primary);
  --adm-font-size-main: 15px;
  --adm-popup-border-radius: 24px;
  --adm-center-popup-border-radius: 20px;
  --adm-center-popup-max-width: min(400px, 90vw);
  --adm-center-popup-min-width: min(400px, 90vw);
  --adm-progress-bar-track-color: #d9d9d9;
  --adm-color-danger: var(--color-error);
  --adm-color-success: var(--color-success);
  --adm-color-text-secondary: var(--color-text-secondary);
}

@media screen and (max-width: 370px) {
  :root {
    --layout-content-padding: 10px;
  }
}
