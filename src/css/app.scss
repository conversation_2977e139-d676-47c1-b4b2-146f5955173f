@import url('./base.scss');
@import url('./font.scss');

// .page {
//   padding: 16px 16px 96px 16px;
// }

.section-container {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 24px;
}

.zaui-list-item {
  cursor: pointer;
}

// Fonts
body {
  font-family: 'Roboto', sans-serif;
  overflow: hidden;
}

#app {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
}

/* Hide scrollbar */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Custom ZUI */
/* Button */
.zaui-btn {
  font-size: 16px;
  color: var(--color-text-primary);
  animation: none !important;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  // &.zaui-btn-secondary {
  //   border: 2px solid var(--color-main-primary);

  //   &:active {
  //     border-color: #8c1b22;
  //     color: #8c1b22;
  //   }
  // }
}

/* Spinner */
.zaui-spinner-ring {
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(255, 255, 255, 0.0001) -89.96deg,
    rgba(255, 255, 255, 0.0001) 180.44deg,
    var(--color-main-primary) 258.75deg,
    var(--color-main-primary) 270deg,
    rgba(255, 255, 255, 0.0001) 270.04deg,
    rgba(255, 255, 255, 0.0001) 540.44deg
  );
}

/* Ripple Effect */
.ripple-circle {
  width: 20px;
  height: 20px;
  border-radius: 100%;
  animation: ripple 1s ease-in-out 0s infinite alternate;
}

/* HighLands */

/* Keyframes */
@keyframes button-glow {
  0% {
    box-shadow: 0px 0px 10px 5px rgba(255, 242, 0, 0.3);
  }

  50% {
    box-shadow: 0 0 20px 10px rgba(255, 242, 0, 0.5);
  }

  100% {
    box-shadow: 0px 0px 10px 5px rgba(255, 242, 0, 0.3);
  }
}

@keyframes ripple {
  0% {
    box-shadow:
      0 0 0 0.25rem rgba(255, 255, 255, 0.2),
      0 0 0 0.5rem rgba(255, 255, 255, 0.2),
      0 0 0 1rem rgba(255, 255, 255, 0.2);
  }

  100% {
    box-shadow:
      0 0 0 0.5rem rgba(255, 255, 255, 0.2),
      0 0 0 0.75rem rgba(255, 255, 255, 0.2),
      0 0 0 1.5rem rgba(255, 255, 255, 0);
  }
}

/* Pull To Refresh */
.ptr {
  &.ptr--dragging {
    .ptr__pull-down--pull-more {
      display: flex !important;
      justify-content: center;
    }
  }

  &__pull-down {
    color: var(--color-main-primary);

    .lds-ellipsis div {
      background-color: var(--color-main-primary);
    }
  }
}

/* Custom Antd */
.adm-picker-header-button {
  color: var(--color-main-primary);
}

.adm-list-body {
  --border-top: none;
  --border-bottom: none;
  --font-size: var(--adm-font-size-main);
}

/* Custom */
.avatar-shadow {
  border: 1px solid rgba(128, 109, 88, 1);
}

/* Form */
.adm-form-item.adm-form-item-vertical {
  --border-inner: none;

  &.adm-form-item-has-error {
    .adm-form-item-child .adm-input {
      border-color: var(--color-error);
    }
  }

  // --padding-left: 12px;
  // --padding-right: 12px;
  // .adm-form-item-label {
  //   font-size: 10px !important;
  // }

  .adm-list-item-content-main {
    padding: 8px 0px;
  }

  // .adm-input {
  //   --font-size: 12px;
  //   padding: 3px 0px;
  //   border-bottom: 1px solid #e5e5e5;
  // }

  // .adm-radio {
  //   --icon-size: 20px;
  //   --font-size: 12px;
  //
  //   .adm-radio-icon {}
  // }

  // @media screen and (min-width: 400px) {
  //   .adm-form-item-label {
  //     font-size: 12px !important;
  //   }
  //
  //   .adm-input,
  //   .adm-radio-content {
  //     --font-size: 14px;
  //   }
  // }
}

.adm-cascader-view-header-title {
  max-width: 200px !important;
}

.adm-checkbox {
  --font-size: 12px !important;
}

.adm-action-sheet-popup > .adm-popup-body {
  border-top-left-radius: var(--adm-popup-border-radius);
  border-top-right-radius: var(--adm-popup-border-radius);
}
