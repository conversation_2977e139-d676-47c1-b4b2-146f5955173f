@import url("https://fonts.googleapis.com/css2?family=Encode+Sans:wght@100..900&family=M+PLUS+Rounded+1c:wght@400;700;800&family=Red+Rose:wght@300..700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap');

@font-face {
  font-family: "MoreSugar";
  src: url("../assets/fonts/MoreSugar/MoreSugar-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "MoreSugar";
  src: url("../assets/fonts/MoreSugar/MoreSugar-Extras.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: "MoreSugar";
  src: url("../assets/fonts/MoreSugar/MoreSugar-Thin.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "OceanSansStd";
  src: url("../assets/fonts/OceanSansStd/OceanSansStd-XBoldExt.ttf")
    format("trueType");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: "Mansalva";
  src: url("../assets/fonts/Mansalva//Mansalva-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}
