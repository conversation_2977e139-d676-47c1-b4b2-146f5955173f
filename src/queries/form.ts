// Libraries
import { useMutation, UseMutationOptions } from "@tanstack/react-query";
import { AllocateVoucher } from "schemas";

// Services
import { formServices, GiftSubmitArgs } from "services";

// Types
import { CommonResponse } from "types";

export interface UseGiftSubmitProps {
  options?: UseMutationOptions<
    CommonResponse<AllocateVoucher>,
    Error,
    GiftSubmitArgs
  >;
}

export const useGiftSubmit = (props: UseGiftSubmitProps) => {
  const { options } = props;

  return useMutation({
    mutationFn: formServices.giftSubmit,
    ...options,
  });
};
