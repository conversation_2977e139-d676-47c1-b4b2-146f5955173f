// Libraries
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from "@tanstack/react-query";

// Constants
import { QUERY_KEY } from "constant";

// Services
import {
  AllocateVoucherMixAndMatchArgs,
  AllocateVoucherMixAndMatchResponse,
  GetLeaderBoardArgs,
  GetLeaderboardResponse,
  GetRandomMissionResponse,
  mixAndMatchServices,
} from "services";

interface GetRandomMissionProps {
  options?: Omit<
    UseQueryOptions<GetRandomMissionResponse, Error>,
    "queryKey" | "queryFn"
  >;
}

interface AllocateVoucherMixAndMatchProps {
  options?: UseMutationOptions<
    AllocateVoucherMixAndMatchResponse,
    Error,
    AllocateVoucherMixAndMatchArgs,
    any
  >;
}

interface GetLeaderBoardProps {
  args: GetLeaderBoardArgs;
  options?: Omit<
    UseQueryOptions<GetLeaderboardResponse, Error>,
    "queryKey" | "queryFn"
  >;
}

export const useGetRandomMission = (props: GetRandomMissionProps) => {
  return useQuery({
    queryKey: [QUERY_KEY.GET_RANDOM_MISSION],
    queryFn: () => mixAndMatchServices.getRandomMission(),
    ...props.options,
  });
};

export const useAllocateVoucherMixAndMatch = (
  props: AllocateVoucherMixAndMatchProps
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (args: AllocateVoucherMixAndMatchArgs) =>
      mixAndMatchServices.allocateVoucher(args),
    onSettled() {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY.VOUCHER_LIST],
        exact: false,
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY.CHECK_USER_CAN_ALLOCATE],
        exact: false,
      });
    },
    ...props.options,
  });
};

export const useGetLeaderBoard = (props: GetLeaderBoardProps) => {
  return useQuery({
    queryKey: [QUERY_KEY.GET_LEADER_BOARD, props.args],
    queryFn: () => mixAndMatchServices.getLeaderBoard(props.args),
    ...props.options,
  });
};
