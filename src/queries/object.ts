// Libraries
import { useMutation, useQuery, UseQueryOptions } from "@tanstack/react-query";

// Constants
import { QUERY_KEY } from "constant";

// Services
import { CheckCanAllocateVoucherResponse, GetListDealResponse, objectServices } from "services";

export interface UseGetDealListProps {
    options?: Partial<UseQueryOptions<GetListDealResponse, Error>>;
  }

  interface UseCheckCanAllocateVoucherProps {
    options?: UseQueryOptions<
    CheckCanAllocateVoucherResponse,
      Error
    >;
  }

export const useGetDealList = ({ options }: UseGetDealListProps = {}) => {
    return useQuery({
      queryKey: [QUERY_KEY.DEAL_LIST],
      queryFn: () => objectServices.getListDeal(),
      ...options,
    });
  };


  export const useCheckCanAllocateVoucherAFF = (
    props: UseCheckCanAllocateVoucherProps
  ) => {
    return useQuery({
        queryKey: [QUERY_KEY.CHECK_CAN_ALLOCATE_VOUCHER],
        queryFn: () => objectServices.checkCanAllocateVoucher({}),
        ...props.options,
      });
  };