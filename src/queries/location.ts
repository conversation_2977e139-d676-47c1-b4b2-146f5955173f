// Libraries
import { useQuery, UseQueryOptions } from "@tanstack/react-query";

// Constants
import { QUERY_KEY } from "constant";

// Services
import { GetVietNamProvinceListArgs, locationServices } from "services";

// Types
import { CommonResponse } from "types";

interface UseGetVietNamProvinceListProps {
  args?: GetVietNamProvinceListArgs;
  options?: UseQueryOptions<CommonResponse<any>, Error>;
}

export const useGetVietNamProvinceList = ({
  args,
  options,
}: UseGetVietNamProvinceListProps = {}) => {
  return useQuery({
    queryKey: [QUERY_KEY.GET_VIET_NAM_PROVINCE_LIST],
    queryFn: () => locationServices.getVietNamProvinceList(),
    ...options,
  });
};
