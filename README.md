# Golden Gate Group Mini App

## Overview

The Golden Gate Group Mini App is a Zalo Mini App designed to enhance customer loyalty and engagement through gamification. This application allows Golden Gate Group customers to:

- Earn points through purchases and in-app activities
- Redeem points for vouchers and special offers
- Participate in interactive games to earn additional rewards
- Receive personalized promotions based on purchase history
- Track reward status and upcoming offers

## Key Features

### Customer Rewards System

- **Points System**: Customers earn points with every purchase at Golden Gate Group restaurant locations
- **Tiered Membership**: Different tiers (Silver, Gold, Platinum) with increasing benefits
- **Voucher Redemption**: Exchange points for discounts, free items, and partner offers
- **Hot Vouchers**: Featured limited-time offers with special redemption rates

### Interactive Games

- **Mini Games**: Simple, engaging games that reward players with points
- **Daily Check-in Rewards**: Bonus points for regular app usage
- **Challenges**: Time-limited missions to encourage specific behaviors (e.g., "Visit 3 Golden Gate restaurants this week")

### User Experience

- **Personalized Recommendations**: Tailored offers based on purchase history
- **Store Locator**: Find nearest Golden Gate Group restaurant locations
- **Digital Receipts**: View purchase history and points earned
- **Notifications**: Alerts for new offers, expiring points, and special events

## Tech Stack

- **Frontend**: React with TypeScript
- **Styling**: Styled Components
- **Animations**: Motion library
- **Platform**: Zalo Mini App SDK
- **UI Components**: ZaUI component library

## Development

### Using Zalo Mini App Extension

1. Install [Visual Studio Code](https://code.visualstudio.com/download) and [Zalo Mini App Extension](https://mini.zalo.me/docs/dev-tools).
2. In the **Home** tab, configure **App ID** and **Install Dependencies**.
3. Navigate to the **Run** tab, select the suitable launcher, and click **Start**.

### Using Zalo Mini App CLI

1. [Install Node.js](https://nodejs.org/en/download/).
2. [Install Zalo Mini App CLI](https://mini.zalo.me/docs/dev-tools/cli/intro/).
3. **Install dependencies**:
   ```bash
   npm install
   ```
4. **Start** the dev server:
   ```bash
   zmp start
   ```
5. **Open** `localhost:3000` in your browser.

## Project Structure

```
golden-gate-mini-app/
├── src/
│   ├── assets/           # Images, icons, and other static assets
│   ├── components/       # Reusable UI components
│   ├── pages/            # Application pages
│   │   ├── home/         # Home page with vouchers and rewards
│   │   ├── games/        # Interactive games section
│   │   ├── profile/      # User profile and rewards status
│   │   └── vouchers/     # Voucher redemption section
│   ├── schemas/          # Data models and type definitions
│   ├── services/         # API services and data fetching
│   ├── utils/            # Helper functions and utilities
│   └── App.tsx           # Root application component
├── public/               # Public assets
└── package.json          # Dependencies and scripts
```

## Deployment

1. **Create** a mini program. For instructions on how to create a mini program, please refer to the [Coffee Shop Tutorial](https://mini.zalo.me/tutorial/coffee-shop/step-1/).

2. **Deploy** your mini program to Zalo using the mini app ID created.
   - **Using Zalo Mini App Extension**: navigate to the **Deploy** panel > **Login** > **Deploy**.
   - **Using Zalo Mini App CLI**:
     ```bash
     zmp login
     zmp deploy
     ```

3. Open the mini app in Zalo by scanning the QR code.

## Resources

- [Zalo Mini App Official Website](https://mini.zalo.me/)
- [ZaUI Documentation](https://mini.zalo.me/documents/zaui/)
- [ZMP SDK Documentation](https://mini.zalo.me/documents/api/)
- [DevTools Documentation](https://mini.zalo.me/docs/dev-tools/)
- [Ready-made Mini App Templates](https://mini.zalo.me/zaui-templates)
- [Community Support](https://mini.zalo.me/community)

## About Golden Gate Group

Founded in 2005, Golden Gate (CÔNG TY CỔ PHẦN TẬP ĐOÀN GOLDEN GATE) is a pioneer in applying the restaurant chain model in Vietnam, with 5 main culinary styles including: Hot Pot, BBQ, Asian, European and Coffee shops. Golden Gate currently owns more than 40 brands with over 500 multi-style restaurants across 42 provinces and cities, serving 18 million customers annually and continues to strive for further development.

## Development

This project is developed by **Antsomi** - a marketing technology company that built the first AI-enabled customer data platform in Southeast Asia. Founded in 2020 and headquartered in Singapore, Antsomi specializes in helping businesses transform into data-driven companies.

## License
