import type { StorybookConfig } from '@storybook/react-webpack5';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import Dotenv from 'dotenv-webpack';
import TailwindcssPlugin from 'tailwindcss';
import AutoprefixerPlugin from 'autoprefixer';
import webpack, { type Compiler, type WebpackPluginInstance } from 'webpack';
import path from 'path';

// Custom plugin to handle Vite-style SVG imports with ?react query
class ViteSvgQueryPlugin implements WebpackPluginInstance {
  apply(compiler: Compiler) {
    compiler.hooks.normalModuleFactory.tap('ViteSvgQueryPlugin', (factory) => {
      factory.hooks.beforeResolve.tap('ViteSvgQueryPlugin', (resolveData) => {
        if (resolveData.request && resolveData.request.includes('.svg?react')) {
          // Remove the ?react query parameter from the request
          resolveData.request = resolveData.request.replace('?react', '');
        }
      });
    });
  }
}

const config: StorybookConfig = {
  stories: ['../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    {
      name: '@storybook/addon-docs',
      options: {
        configureJSX: true,
      },
    },
  ],
  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },
  typescript: {
    check: false,
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      propFilter: (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
    },
  },
  webpackFinal: async (config) => {
    // Resolve aliases to match your project structure
    config.resolve = {
      ...config.resolve,
      // Handle query parameters in imports (like ?react for SVG)
      extensionAlias: {
        '.svg': ['.svg'],
      },
      plugins: [new TsconfigPathsPlugin()],
    };

    // Handle TypeScript files
    config.module?.rules?.push({
      test: /\.tsx?$/,
      use: [
        {
          loader: 'ts-loader',
          options: {
            transpileOnly: true,
            configFile: path.resolve(__dirname, '../tsconfig.json'),
          },
        },
      ],
      exclude: /node_modules/,
    });

    // Handle SCSS files
    config.module?.rules?.push({
      test: /\.scss$/,
      use: [
        'style-loader',
        'css-loader',
        {
          loader: 'postcss-loader',
          options: {
            postcssOptions: {
              plugins: [TailwindcssPlugin, AutoprefixerPlugin],
            },
          },
        },
        'sass-loader',
      ],
      include: path.resolve(__dirname, '../src'),
    });

    // Handle CSS files (for Tailwind)
    config.module?.rules?.push({
      test: /\.css$/,
      use: [
        'style-loader',
        'css-loader',
        {
          loader: 'postcss-loader',
          options: {
            postcssOptions: {
              plugins: [TailwindcssPlugin, AutoprefixerPlugin],
            },
          },
        },
      ],
      include: path.resolve(__dirname, '../src'),
    });

    // Handle SVG files with SVGR (similar to Vite's svgr plugin)
    // Remove existing SVG rules that might conflict
    const fileLoaderRule = config.module?.rules?.find((rule) => {
      if (typeof rule === 'object' && rule?.test instanceof RegExp) {
        return rule.test.test('.svg');
      }
      return false;
    });

    if (fileLoaderRule && typeof fileLoaderRule === 'object') {
      // Modify existing rule to exclude SVG
      if (fileLoaderRule.test instanceof RegExp) {
        fileLoaderRule.exclude = /\.svg$/i;
      }
    }

    // Add SVG handling rule
    config.module?.rules?.push({
      test: /\.svg$/i,
      issuer: /\.[jt]sx?$/,
      use: [
        {
          loader: '@svgr/webpack',
          options: {
            // SVGR options
            typescript: true,
            dimensions: false,
          },
        },
      ],
    });

    // Add custom plugins
    config.plugins = config.plugins || [];
    config.plugins.push(new ViteSvgQueryPlugin());

    // Add dotenv-webpack plugin to load environment variables FIRST
    config.plugins.push(
      new Dotenv({
        path: path.resolve(__dirname, '../.env'),
        silent: true,
      }),
    );

    // Add DefinePlugin to define import.meta.env variables
    // This must come AFTER dotenv-webpack to have access to the loaded env vars
    config.plugins.push(
      new webpack.DefinePlugin({
        'import.meta.env': JSON.stringify(process.env),
      }),
    );

    return config;
  },
};

export default config;
