import { create } from '@storybook/theming/create';

// Dark theme configuration for Storybook UI
export const darkTheme = create({
  base: 'dark',

  // Brand
  brandTitle: 'GGG - Zalo Mini App',
  brandUrl: './',
  brandImage: undefined,
  brandTarget: '_self',

  // Colors
  colorPrimary: '#e4b653', // Primary color from design system
  colorSecondary: '#667685', // Secondary color from design system

  // UI
  appBg: '#1a1a1a', // Dark background
  appContentBg: '#2a2a2a', // Content background
  appBorderColor: '#404040', // Border color
  appBorderRadius: 8,

  // Text colors
  textColor: '#ffffff', // Primary text
  textInverseColor: '#000000', // Inverse text
  textMutedColor: '#a0a0a0', // Muted text

  // Toolbar
  barTextColor: '#ffffff', // Toolbar text
  barSelectedColor: '#e4b653', // Selected item color
  barBg: '#2a2a2a', // Toolbar background

  // Form colors
  inputBg: '#404040', // Input background
  inputBorder: '#666666', // Input border
  inputTextColor: '#ffffff', // Input text
  inputBorderRadius: 4,

  // Button colors
  buttonBg: '#404040', // Button background
  buttonBorder: '#666666', // Button border
  booleanBg: '#404040', // Boolean control background
  booleanSelectedBg: '#e4b653', // Boolean selected background
});

// Light theme configuration for Storybook UI
export const lightTheme = create({
  base: 'light',

  // Brand
  brandTitle: 'GS - Zalo Mini App',
  brandUrl: './',
  brandImage: undefined,
  brandTarget: '_self',

  // Colors
  colorPrimary: '#e4b653', // Primary color from design system
  colorSecondary: '#667685', // Secondary color from design system

  // UI
  appBg: '#ffffff', // Light background
  appContentBg: '#f8f9fa', // Content background
  appBorderColor: '#e1e5e9', // Border color
  appBorderRadius: 8,

  // Text colors
  textColor: '#001a33', // Primary text from design system
  textInverseColor: '#ffffff', // Inverse text
  textMutedColor: '#667685', // Muted text from design system

  // Toolbar
  barTextColor: '#001a33', // Toolbar text
  barSelectedColor: '#e4b653', // Selected item color
  barBg: '#f8f9fa', // Toolbar background

  // Form colors
  inputBg: '#ffffff', // Input background
  inputBorder: '#d6d9db', // Input border from design system
  inputTextColor: '#001a33', // Input text
  inputBorderRadius: 4,

  // Button colors
  buttonBg: '#ffffff', // Button background
  buttonBorder: '#d6d9db', // Button border
  booleanBg: '#ffffff', // Boolean control background
  booleanSelectedBg: '#e4b653', // Boolean selected background
});
