/* Mobile-friendly scrollbar styles for Storybook */

/* Hide scrollbars for Webkit browsers (iOS/Safari style) */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* For dark theme */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* For light theme */
.light ::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
}

.light ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.dark * {
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

.light * {
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

/* Mobile-like smooth scrolling */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Override Storybook specific scrollbars */
.sb-show-main {
  /* Story canvas scrollbars */
}

.sb-show-main iframe {
  /* Story content scrollbars */
}

#storybook-docs {
  /* Documentation scrollbars */
}

.docblock-source {
  /* Code block scrollbars */
}

.docblock-source::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.docblock-source::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 2px;
}

/* Controls panel scrollbars */
.sidebar-container::-webkit-scrollbar,
.panel-container::-webkit-scrollbar {
  width: 4px;
}

.sidebar-container::-webkit-scrollbar-thumb,
.panel-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 2px;
}

/* Story list scrollbars */
.os-content::-webkit-scrollbar {
  width: 4px;
}

.os-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 2px;
}

/* Mobile-friendly touch scrolling for story containers */
.sb-show-main,
.sb-main-padded {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Hide scrollbars completely on mobile viewports (like native mobile apps) */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    background: transparent;
  }
  
  * {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
}

/* For components that need visible scrollbars even on mobile */
.show-scrollbar::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
}

.show-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 2px !important;
}