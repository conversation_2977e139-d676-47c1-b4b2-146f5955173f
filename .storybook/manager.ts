import { addons } from '@storybook/manager-api';
import { darkTheme, lightTheme } from './theme';
import './scrollbar.css';

// Set the default theme
addons.setConfig({
  theme: darkTheme,
  // You can also add other configurations here
  showPanel: true,
  panelPosition: 'bottom',
  enableShortcuts: true,
  showToolbar: true,
  selectedPanel: 'controls',
  initialActive: 'sidebar',
  sidebar: {
    showRoots: true,
    collapsedRoots: [],
  },
  toolbar: {
    title: { hidden: false },
    zoom: { hidden: false },
    eject: { hidden: false },
    copy: { hidden: false },
    fullscreen: { hidden: false },
  },
});

// Function to switch theme (can be called from addon or manually)
export const switchTheme = (isDark: boolean) => {
  addons.setConfig({
    theme: isDark ? darkTheme : lightTheme,
  });
};
