# Storybook Environment Variables Setup

## Overview

This Storybook configuration has been set up to handle Vite's `import.meta.env` syntax by defining it as a JSON object with environment variables for webpack compatibility.

## Features

- **Dotenv Webpack Plugin**: Loads environment variables from `.env` file
- **DefinePlugin**: Defines `import.meta.env` as a JSON object with all environment variables
- **Fallback Values**: Provides default values for Storybook when `.env` file is not available
- **System Variables**: Also loads system environment variables

## Setup Instructions

### 1. Create .env file

Create a `.env` file in the project root with your environment variables:

```bash
# Zalo Mini App Configuration
VITE_OA_ID=your_oa_id_here
VITE_SECRET_KEY=your_secret_key_here

# Google Maps API
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# API Configuration
VITE_API_DOMAIN=https://api.example.com
VITE_DEV_ACCESS_TOKEN=your_dev_access_token_here

# Socket Configuration
VITE_SOCKET_URL=wss://socket.example.com

# Facebook Pixel
VITE_FACEBOOK_PIXEL_ID=your_facebook_pixel_id_here
```

### 2. How it works

The configuration automatically:

- Loads environment variables from `.env` file using dotenv-webpack
- Defines `import.meta.env` as a JSON object with all environment variables using DefinePlugin
- Provides fallback values for Storybook when `.env` file is not available
- Makes environment variables available in Storybook stories
- Handles both `.env` file and system environment variables

### 3. Usage in Stories

You can now use environment variables in your Storybook stories exactly as you do in your Vite app:

```typescript
// In your story files - works exactly like in Vite
const config = {
  OA_ID: import.meta.env.VITE_OA_ID,
  API_URL: import.meta.env.VITE_API_DOMAIN,
  // ... other config
};
```

### 4. Configuration Details

- **Dotenv Plugin**: Loads from `../.env` file
- **System Variables**: Enabled (`systemvars: true`)
- **Silent Mode**: Won't warn if `.env` file is missing
- **DefinePlugin**: Defines `import.meta.env` as a JSON object with all environment variables
- **Fallback Values**: Provides default values when environment variables are not available

## Troubleshooting

- If environment variables are not loading, check that your `.env` file is in the project root
- Make sure variable names start with `VITE_` prefix
- Restart Storybook after changing `.env` file
