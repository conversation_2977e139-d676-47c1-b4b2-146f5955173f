import type { Preview } from '@storybook/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { INITIAL_VIEWPORTS, MINIMAL_VIEWPORTS } from '@storybook/addon-viewport';
import { RecoilRoot } from 'recoil';
import { App } from 'zmp-ui';
import { ConfigProvider } from '@antscorp/ama-ui-v1';
import { ConfigProvider as MobileConfigProvider } from '@antscorp/ama-ui';
import { MotionConfig } from 'motion/react';
import { lightTheme } from './theme';
import '../src/css/tailwind.scss';
import '../src/css/base.scss';
import '../src/css/font.scss';
import './scrollbar.css';

// Theme configuration for components
const THEME = {
  token: {
    colorPrimary: '#b22a2e',
    fontFamily: 'Roboto',
  },
  cssVar: true,
};

// Create a query client for Storybook
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 60 * 1000,
      refetchOnReconnect: true,
    },
  },
});

const preview: Preview = {
  parameters: {
    // Storybook UI theme configuration
    docs: {
      theme: lightTheme,
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: 'dark',
      values: [
        {
          name: 'light',
          value: '#ffffff',
        },
        {
          name: 'dark',
          value: '#1a1a1a',
        },
        {
          name: 'surface',
          value: '#2a2a2a',
        },
      ],
    },
    viewport: {
      viewports: {
        ...INITIAL_VIEWPORTS,
        ...MINIMAL_VIEWPORTS,
      },
      defaultViewport: 'iphone14promax',
    },
  },
  decorators: [
    (Story) => (
      <RecoilRoot>
        <App>
          <ConfigProvider theme={THEME}>
            <MobileConfigProvider>
              <MotionConfig reducedMotion="user">
                <QueryClientProvider client={queryClient}>
                  <Story />
                </QueryClientProvider>
              </MotionConfig>
            </MobileConfigProvider>
          </ConfigProvider>
        </App>
      </RecoilRoot>
    ),
  ],
};

export default preview;
