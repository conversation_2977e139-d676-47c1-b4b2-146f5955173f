
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
# .env
# .env.development

# Misc
.DS_Store
Thumbs.db

# Production build
www/
dist/

.early.coverage

# Sentry Config File
.env.sentry-build-plugin

.codegpt

# Claude init context
CLAUDE.md
.cursor
.claude