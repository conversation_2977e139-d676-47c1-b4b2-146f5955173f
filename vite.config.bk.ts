import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { sentryVitePlugin } from "@sentry/vite-plugin";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default () => {
  return defineConfig({
    build: {
      sourcemap: true, // Source map generation must be turned on
    },
    root: "./src",
    base: "",
    plugins: [
      react(),
      tsconfigPaths(),
      sentryVitePlugin({
        org: "antsomi",
        url: "https://dev-sentry.ants.tech/",
        project: "zma-highlands-rewards",
        authToken:
          "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",
      }),
    ],
  });
};
