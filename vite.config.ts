import { defineConfig } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';
// import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react';
// import { visualizer } from 'rollup-plugin-visualizer';
import { compression } from 'vite-plugin-compression2';

export default () => {
  const isProd = process.env.NODE_ENV === 'production';

  return defineConfig({
    build: {
      sourcemap: false, // Only generate source maps in dev
      minify: 'terser', // Use terser for better minification
      terserOptions: {
        compress: {
          // drop_console: isProd, // Remove console logs in production
          // drop_debugger: isProd,
        },
        format: {
          comments: false, // Remove comments
        },
      },
      reportCompressedSize: false, // Speed up build by skipping size reporting
      chunkSizeWarningLimit: 1000, // Warning when chunk size exceeds 1MB
      rollupOptions: {
        output: {
          manualChunks: {
            // Split vendor packages into separate chunks
            react: ['react', 'react-dom', 'react-router', 'react-router-dom'],
            recoil: ['recoil'],
            antd: ['@antscorp/ama-ui'],
            zmp: ['zmp-ui', 'zmp-sdk'],
            utils: ['lodash-es', 'dayjs'],
            // Add more chunks as needed
          },
        },
      },
    },
    root: './src',
    base: '',
    plugins: [
      react(),
      tsconfigPaths(),
      svgr(),
      isProd && compression({ algorithm: 'gzip' }),
      // isProd && compression({ algorithm: "brotli" }),
      // Only add Sentry in production
      // isProd &&
      //   sentryVitePlugin({
      //     org: "antsomi",
      //     url: "https://dev-sentry.ants.tech/",
      //     project: "zma-goldengate-group",
      //     authToken:
      //       "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",
      //   }),
      // Visualize bundle size (generates stats.html)
      // process.env.ANALYZE &&
      //   visualizer({
      //     filename: "stats.html",
      //     open: true,
      //   }),
    ].filter(Boolean),
    // css: {
    //   preprocessorOptions: {
    //     scss: {
    //       javascriptEnabled: true,
    //       additionalData: '@import "./css/variables.scss";',
    //     },
    //   },
    //   // Minify CSS in production
    //   devSourcemap: !isProd,
    // },
    esbuild: {
      // drop: isProd ? ["console", "debugger"] : [],
      drop: [],
    },
  });
};
